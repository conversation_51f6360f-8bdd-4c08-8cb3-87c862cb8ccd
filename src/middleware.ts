// src/middleware.ts
import { NextRequest, NextResponse } from "next/server";
import * as jwt from "jsonwebtoken";
import * as cookie from "cookie";
import { decryptToken } from "@/utils/auth";
import { refreshAccessToken } from "./utils/api";

// Define types for improved type safety
interface DecodedToken {
  exp: number;
  [key: string]: any;
}

// Configuration
const PUBLIC_PATHS = [
  "/users/login",
  "/users/sign_up",
  "/users/forgot_password",
  "/users/set_password",
  "/users/confirm_signup",
  "/users/confirm_forgot_password",
  "/api",
  "/_next",
  "/favicon.ico",
  "/users/sso/callback/google",
  "/",
  "/pricing",
  "/warning",
];

const RESTRICTED_PATHS: string[] = [
  // Add restricted paths here if needed
];

export async function middleware(req: NextRequest) {
  const cookies = cookie.parse(req.headers.get("cookie") || "");
  const url = req.nextUrl.pathname;

  // Handle inactive tenant case
  if (handleInactiveTenant(url, cookies)) {
    return NextResponse.redirect(new URL("/warning", req.url));
  }

  if (url === "/warning" && cookies["inactive_tenant"] === "true") {
    return NextResponse.next();
  }

  // Parse authentication tokens
  const { idToken, refreshToken } = extractTokens(cookies);
  const decoded = decryptToken(idToken);

  // Try to get user from token
  const user = getUserFromToken(idToken);

  // Determine tenant ID with proper priority
  const tenant_id = determineTenantId(req, cookies, user);

  // Determine user roles
  const is_super_admin = tenant_id === process.env.NEXT_PUBLIC_ROOT_TENANT_ID;
  const is_admin = user ? user["custom:is_admin"] === "true" : false;

  // Handle path-specific access
  const isPublicPath = isPathInList(url, PUBLIC_PATHS);
  const isRestrictedPath = isPathInList(url, RESTRICTED_PATHS);

  if (idToken && isTokenExpired(decoded)) {
    return redirectToLogin(req, url);
  }

  if (!idToken && !isPublicPath) {
    return NextResponse.redirect(new URL("/users/login", req.url));
  }

  // Handle root redirect for authenticated users
  if (url === "/" && idToken) {
    const redirectResult = handleRootPathWithAuth(req, idToken, tenant_id, is_super_admin, is_admin);
    if (redirectResult) return redirectResult;
  }

  // Handle login page redirect for already authenticated users // default-
  if (idToken && url === "/users/login" && !tenant_id.startsWith('default')) {
    const loginRedirectResult = handleLoginPageWithAuth(req, idToken, refreshToken, cookies, is_super_admin, is_admin);
    if (loginRedirectResult) return loginRedirectResult;
  }

  // Handle sign-up page redirect for authenticated users
  if (idToken && url === "/users/sign_up") {
    return NextResponse.redirect(new URL("/home", req.url));
  }

  // Handle restricted paths without auth
  if (isRestrictedPath && !idToken) {
    return redirectToLogin(req, url);
  }

  // Allow access to public paths
  if (isPublicPath) {
    return NextResponse.next();
  }

  // Redirect to login if no token
  if (!idToken) {
    return handleUnauthenticatedAccess(req, url);
  }

  // Verify token and handle authenticated access
  return handleAuthenticatedAccess(req, idToken, refreshToken, cookies, url, is_super_admin, is_admin);
}

// Helper functions
function handleInactiveTenant(url: string, cookies: Record<string, string>): boolean {
  const inactiveTenant = cookies["inactive_tenant"];
  return inactiveTenant === "true" && url !== "/warning" && !url.startsWith("/users/login");
}

function extractTokens(cookies: Record<string, string>) {
  return {
    idToken: cookies["idToken"] || null,
    refreshToken: cookies["refreshToken"] || null
  };
}

function getUserFromToken(idToken: string | null) {
  if (!idToken) return null;

  try {
    return decryptToken(idToken);
  } catch (error) {
    return null;
  }
}

function determineTenantId(
  req: NextRequest,
  cookies: Record<string, string>,
  user: any
): string {
  const urlTenantId = req.nextUrl.searchParams.get("tenant_id");
  const cookieTenantId = cookies["tenant_id"];
  const userTenantId = user ? user["custom:tenant_id"] : null;

  // Priority: URL params → user token → cookie → 'T0000'
  return (urlTenantId && urlTenantId !== "null")
    ? urlTenantId
    : userTenantId || cookieTenantId || process.env.NEXT_PUBLIC_ROOT_TENANT_ID;
}

function isPathInList(url: string, pathList: string[]): boolean {
  return pathList.some(path => url.startsWith(path) || url === path);
}

function verifyToken(idToken: string): DecodedToken | null {
  try {
    const decoded = jwt.decode(idToken) as DecodedToken;
    if (!decoded || typeof decoded.exp !== 'number') {
      return null;
    }
    return decoded;
  } catch (error) {
    
    return null;
  }
}

function isTokenExpired(decoded: DecodedToken): boolean {
  const currentTime = Date.now() / 1000;
  return decoded.exp < currentTime;
}

function redirectToLogin(req: NextRequest, currentPath: string): NextResponse {
  const loginUrl = new URL("/users/login", req.url);
  const currentUrl = new URL(req.url);

  // Preserve query parameters
  currentUrl.searchParams.forEach((value, key) => {
    loginUrl.searchParams.set(key, value);
  });

  // Clear all cookies on token expiration/invalidation
  const response = NextResponse.redirect(loginUrl);
  const allCookies = req.cookies.getAll();
  allCookies.forEach((cookie) => {
    response.cookies.delete(cookie.name);
  });

  return response;
}

function handleUnauthenticatedAccess(req: NextRequest, url: string): NextResponse {
  const loginUrl = new URL("/users/login", req.url);
  const currentUrl = new URL(req.url);

  // Preserve query parameters
  currentUrl.searchParams.forEach((value, key) => {
    loginUrl.searchParams.set(key, value);
  });

  const response = NextResponse.redirect(loginUrl);

  // Clear all cookies on logout
  const allCookies = req.cookies.getAll();
  allCookies.forEach((cookie) => {
    response.cookies.delete(cookie.name);
  });

  return response;
}

function handleRootPathWithAuth(
  req: NextRequest,
  idToken: string,
  tenant_id: string,
  is_super_admin: boolean,
  is_admin: boolean
): NextResponse | null {
  const decoded = verifyToken(idToken);

  if (!decoded || isTokenExpired(decoded)) {
    
    // Clear all cookies on token expiration
    const response = NextResponse.redirect(new URL("/users/login", req.url));
    const allCookies = req.cookies.getAll();
    allCookies.forEach((cookie) => {
      response.cookies.delete(cookie.name);
    });
    return response;
  }
  // default-
  if (tenant_id.startsWith('default')) {
    return NextResponse.redirect(new URL("/home", req.url));
  }

  if (is_super_admin) {
    return NextResponse.redirect(new URL("/dashboard/organizations", req.url));

  } else {
    return NextResponse.redirect(new URL("/home", req.url));
  }
}

async function handleLoginPageWithAuth(
  req: NextRequest,
  idToken: string,
  refreshToken: string | null,
  cookies: Record<string, string>,
  is_super_admin: boolean,
  is_admin: boolean
): Promise<NextResponse | null> {
  const decoded = verifyToken(idToken);

  if (!decoded) {
    // Clear cookies if token is invalid
    const response = NextResponse.next();
    const allCookies = req.cookies.getAll();
    allCookies.forEach((cookie) => {
      response.cookies.delete(cookie.name);
    });
    return response;
  }

  if (isTokenExpired(decoded)) {
    // Token expired, try to refresh
    if (refreshToken) {
      try {
        const encryptedTenantId = cookies["encrypted_tenant_id"];
        const { id_token } = await refreshAccessToken(refreshToken, encryptedTenantId);

        // If refresh successful, continue with redirection
        const response = NextResponse.redirect(new URL("/dashboard", req.url));
        response.cookies.set("idToken", id_token);
        return response;
      } catch (error) {
        // If refresh fails, clear cookies and allow access to login page
        const response = NextResponse.next();
        const allCookies = req.cookies.getAll();
        allCookies.forEach((cookie) => {
          response.cookies.delete(cookie.name);
        });
        return response;
      }
    }
    // No refresh token, clear cookies
    const response = NextResponse.next();
    const allCookies = req.cookies.getAll();
    allCookies.forEach((cookie) => {
      response.cookies.delete(cookie.name);
    });
    return response;
  }

  // Token is valid, redirect to appropriate dashboard
  if (is_super_admin) {
    return NextResponse.redirect(new URL("/dashboard/organizations", req.url));
  } else if (is_admin) {
    return NextResponse.redirect(new URL("/dashboard/profile", req.url));
  } else {
    return NextResponse.redirect(new URL("/home", req.url));
  }
}

async function handleAuthenticatedAccess(
  req: NextRequest,
  idToken: string,
  refreshToken: string | null,
  cookies: Record<string, string>,
  url: string,
  is_super_admin: boolean,
  is_admin: boolean
): Promise<NextResponse> {
  const decoded = verifyToken(idToken);

  if (!decoded) {
    return redirectToLogin(req, url);
  }

  if (isTokenExpired(decoded)) {
    // Try to refresh token
    if (refreshToken) {
      const encryptedTenantId = cookies["encrypted_tenant_id"];
      try {
        const { id_token } = await refreshAccessToken(refreshToken, encryptedTenantId);

        // Create a NextResponse object
        const res = NextResponse.next();
        res.cookies.set("idToken", id_token);
        return res;
      } catch (error) {
        // If refresh fails, clear cookies and redirect to login
        return redirectToLogin(req, url);
      }
    } else {
      // No refresh token, clear cookies and redirect to login
      return redirectToLogin(req, url);
    }
  }

  // Handle specific routes

  // Dashboard redirect
  if (url === "/dashboard") {
    if (is_super_admin) {
      return NextResponse.redirect(new URL("/dashboard/organizations", req.url));
    } else if (is_admin) {
      return NextResponse.redirect(new URL("/dashboard/profile/details", req.url));
    } else {
      return NextResponse.redirect(new URL("/", req.url));
    }
  }

  // Super admin restrictions
  if (is_super_admin && !url.startsWith("/dashboard/organizations")) {
    return NextResponse.redirect(new URL("/dashboard/organizations", req.url));
  }

  // Organizations page restrictions
  if (url.startsWith("/dashboard/organizations") && !is_super_admin) {
    return NextResponse.redirect(new URL("/home", req.url));
  }

  // Dashboard access for admins
  if (url.startsWith("/dashboard/") && !url.startsWith("/dashboard/organizations")) {
    if (!is_admin || is_super_admin) {
      return NextResponse.redirect(new URL("/home", req.url));
    }
  }

  // Root path redirection
  if (url === "/" && !cookies["redirected"]) {
    const response = NextResponse.redirect(new URL("/home", req.url));
    response.cookies.set("redirected", "true", {
      path: "/",
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
    });
    return response;
  }

  // Project path handling
  if (url.startsWith("/project/")) {
    const path = url.split("/");
    const projectId = path[2];
    if (projectId && !path[3]) {
      return NextResponse.redirect(new URL(`/project/${projectId}/overview`, req.url));
    }
  }

  // Valid token and authorized access
  return NextResponse.next();
}

// Updated matcher to exclude static files and API routes
export const config = {
  matcher: [
    // Match all paths except static files, API routes, and public files
    "/((?!api|_next/static|_next/image|favicon.ico|public/).*)",
  ],
};
