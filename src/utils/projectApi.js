import { backend_base_url, getHeaders } from "./api";
import Cookies from "js-cookie";

/**
 * Fetches public projects with pagination
 * @param {number} skip - Number of items to skip (for pagination)
 * @param {number} limit - Maximum number of items to return
 * @returns {Promise<Object>} - The public projects data
 */
export async function fetchPublicProjects(skip = 0, limit = 50) {
  const url = `${backend_base_url}/node/projects/public?skip=${skip}&limit=${limit}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData?.detail || `Failed to fetch public projects: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
}

/**
 * Updates the visibility of a project (public/private)
 * @param {number} projectId - The ID of the project to update
 * @param {boolean} makePublic - Whether to make the project public (true) or private (false)
 * @returns {Promise<Object>} - The response with status and message
 */
export async function updateProjectVisibility(projectId, makePublic) {
  const url = `${backend_base_url}/node/project/visibility`;

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify({
        project_id: projectId,
        make_public: makePublic
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData?.detail || `Failed to update project visibility: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
}

/**
 * Gets the visibility status of a project
 * @param {number} projectId - The ID of the project to check
 * @returns {Promise<Object>} - The response with the project visibility status
 */
export async function checkProjectVisibility(projectId, tenant_id=null) {
  if (!tenant_id) {
    tenant_id = Cookies.get("selected_tenant_id");
  }
  const url = `${backend_base_url}/node/project/visibility/${tenant_id}/${projectId}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData?.detail || `Failed to get project visibility: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
}


/**
 * Initializes a project test with user comments
 * @param {string} userComment - The user comment to include in the project test
 * @returns {Promise<Object>} - The response from the project initialization
 */
export async function startProjectInit(userComment) {
  const base_url = backend_base_url;
  const url = `${base_url}/node/start_project_init`;

  const requestBody = {
    usercomment: userComment || null
  };

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData?.detail || `Failed to initialize project test: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}




/**
 * Update a project's title
 * @param {string} projectId Project ID to update
 * @param {string} newTitle New title for the project
 * @returns {Promise} Updated project data
 */
export const updateProjectTitle = async (projectId, newTitle) => {
  try {
    const url = `${backend_base_url}/node/v2/${projectId}?node_type=Project`;
    
    const response = await fetch(url, {
      method: 'PATCH',
      headers: await getHeaders(),
      body: JSON.stringify({
        node_type: "Project",
        Title: newTitle
      })
    });

    if (!response.ok) {
      throw new Error(`Error updating project title: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error updating project title:", error);
    throw error;
  }
};

/**
 * Delete a project
 * @param {string} projectId Project ID to delete
 * @returns {Promise} Response indicating success
 */
export const deleteProject = async (projectId) => {
  try {
    const url = `${backend_base_url}/node/${projectId}?node_type=Project`;
    
    const response = await fetch(url, {
      method: 'DELETE',
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Error deleting project: ${response.status}`);
    }

    return { success: true };
  } catch (error) {
    console.error("Error deleting project :", error);
    throw error;
  }
};

/**
 * Clone a project
 * @param {string} projectId Project ID to clone
 * @param {string} newTitle Title for the cloned project
 * @returns {Promise} New cloned project data
 */
export const cloneProject = async (projectId, newTitle) => {
  try {
    // Assuming there's a specific clone endpoint, or we need to create a new project
    // with the same properties as the original
    const url = `${backend_base_url}/node/clone_source_node?source_node_id=${projectId}`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify({
        title: newTitle
      })
    });

    if (!response.ok) {
      throw new Error(`Error cloning project: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error cloning project :", error);
    throw error;
  }
};