'use client'

import showdown from "showdown";

const classMap = {
  h1: "typography-heading-4 font-weight-bold mb-2 whitespace-pre-wrap break-words",
  h2: "typography-body-lg font-weight-semibold mb-2 whitespace-pre-wrap break-words",
  h3: "typography-body font-weight-medium mb-2 whitespace-pre-wrap break-words",
  h4: "text-medium font-weight-medium mb-2 whitespace-pre-wrap break-words",
  h5: "typography-body-sm font-weight-medium mb-2  whitespace-pre-wrap break-words",
  h6: "typography-caption font-weight-medium mb-2  whitespace-pre-wrap break-words",
  p: "typography-body-sm font-weight-medium mb-1 whitespace-pre-wrap break-words",
  ul: "list-disc list-inside typography-body-sm font-weight-medium mb-2 whitespace-pre-wrap break-words",
  ol: "list-decimal ml-2 typography-body-sm font-weight-medium mb-2 whitespace-pre-wrap break-words",
  li: "ml-2 mt-2  whitespace-pre-wrap break-words  whitespace-pre-wrap break-words",
  strong: "font-weight-bold mt-2 text-gray-800 whitespace-pre-wrap break-words",
  a: "text-orange-600 hover:text-orange-800 underline  whitespace-pre-wrap break-words",
  blockquote: "border-l-4 border-gray-300 pl-4 py-1 mb-2 italic text-gray-600  whitespace-pre-wrap break-words",
  code: "markdown-code  bg-gray-100 rounded px-1 py-0.5 typography-body-sm text-gray-800 whitespace-pre-wrap break-words",
  pre: "markdown-pre bg-gray-100 rounded p-2 mb-2 whitespace-pre-wrap break-words",
  table: "min-w-full border-collapse mb-2  whitespace-pre-wrap break-words",
  th: "bg-gray-200 border border-gray-300 px-2 py-1 text-left text-gray-700  whitespace-pre-wrap break-words",
  td: "border border-gray-300 px-2 py-1 text-gray-600  whitespace-pre-wrap break-words",
  img: "max-w-full h-auto rounded shadow-lg mb-2  whitespace-pre-wrap break-words",
  hr: "border-t border-gray-300 my-2  whitespace-pre-wrap break-words",
  json: " typography-body-sm text-white m-1 border border-gray-300 p-2 rounded-md mb-2 bg-gray-800 whitespace-pre-wrap break-words",
};
const bindings = Object.keys(classMap).map((key) => ({
  type: 'output',
  regex: new RegExp(`<${key}(.*)>`, 'g'),
  replace: `<${key} class="${classMap[key]}" $1>`,
}));

const conv = new showdown.Converter({
  extensions: [...bindings],
  omitExtraWLInCodeBlocks: true,
  parseImgDimensions: true,
  simplifiedAutoLink: true,
  literalMidWordUnderscores: true,
  strikethrough: true,
  tables: true,
  ghCodeBlocks: true,
  tasklists: true,
  smoothLivePreview: true,
  ghCompatibleHeaderId: true,
  encodeEmails: true,
  ellipsis: true,
  emoji: true
});

export const renderHTML = (text) => {
  if(!text || typeof text !== "string") return null;
  const html = conv.makeHtml(text);
  // Clean up extra whitespace
  return html
    .replace(/>\s+</g, '><')  // Remove whitespace between tags
    .replace(/\n\s*\n/g, '\n')  // Remove multiple empty lines
    .replace(/^\s+|\s+$/g, '')  // Trim start and end
    .replace(/<p><br><\/p>/g, '')  // Remove empty paragraphs with just line breaks
    .replace(/<p>\s*<\/p>/g, '');  // Remove empty paragraphs
};


export function findValueOfProperty(obj, propertyName) {
  let reg = new RegExp(propertyName, "i"); // "i" to make it case insensitive
  return Object.keys(obj).reduce((result, key) => {
    if (reg.test(key)) result.push(obj[key]);
    return result;
  }, []);
}

export const getTitleForRelationship = (relationship) => {
  if (relationship == "hasChild"){
    return "Child Nodes";
  }
  if (relationship == "interfacesWith"){
    return "Interfaces";
  }
  return relationship;
}

export function groupRelationshipsByType(relationships) {

  return relationships.reduce((acc, relationship) => {
    if (!acc[relationship.type]) {
      acc[relationship.type] = [];
    }
    acc[relationship.type].push(relationship);
    return acc;
  }, {});
}

export const updateSearchParams = (router, pathname, searchParams, paramKey, paramValue) => {
  const newParams = new URLSearchParams(searchParams);
  newParams.set(paramKey, paramValue);
  router.replace(`${pathname}?${newParams.toString()}`);
};

//will avoid going back to code generation or discussion panel after pressing back button by instructing browser to go 2 pages back
export const updateSessionStorageBackHistory = (steps = 2) => {
  if(sessionStorage.getItem("querySet")){
    let backSteps = Number(sessionStorage.getItem("querySet"));
    backSteps -= steps;
    sessionStorage.setItem("querySet", backSteps);
  }
  else{
    sessionStorage.setItem("querySet", "-3");
  }
}
