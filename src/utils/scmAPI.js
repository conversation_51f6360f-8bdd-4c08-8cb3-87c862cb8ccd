import { getHeaders } from "./api";

const base_url = process.env.NEXT_PUBLIC_API_URL;
const SHOW_NAME = 'scm';

/**
 * Configure SCM settings
 * @param {Object} config - SCM configuration object
 * @returns {Promise<Object>} Response from the server
 */
export const configureSCM = async (config) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/configure`, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify(config)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to configure SCM');
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

/**
 * Get SCM configuration(s)
 * @param {string} [scmId] - Optional SCM ID to get specific configuration
 */
export const getSCMConfiguration = async (scmId = null, isEncrypted = true) => {
  try {
    
    const url = new URL(`${base_url}/${SHOW_NAME}/configuration`);
    if (scmId) {
      url.searchParams.append('scm_id', scmId);
      url.searchParams.append('is_encrypted', isEncrypted);
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to get SCM configuration');
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

/**
 * Delete SCM configuration
 * @param {string} [scmId] - Optional SCM ID to delete specific configuration
 * @returns {Promise<Object>} Response from the server
 */
export const deleteSCMConfiguration = async (scmId = null, isEncrypted = true) => {
  try {
    const url = new URL(`${base_url}/${SHOW_NAME}/configuration`);
    if (scmId) {
      url.searchParams.append('scm_id', scmId);
      if(isEncrypted){
        url.searchParams.append('is_encrypted', isEncrypted);
      }
    }

    const response = await fetch(url, {
      method: 'DELETE',
      headers: await getHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to delete SCM configuration');
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

/**
 * Get OAuth login URL
 * @param {string} scmType - Type of SCM (github/gitlab)
 */
export const getOAuthLoginURL = async (scmType) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/oauth/${scmType}/login?return_url=true`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to get OAuth login URL');
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

/**
 * Handle OAuth callback
 * @param {string} scmType - Type of SCM (github/gitlab)
 * @param {string} state - OAuth state parameter
 * @param {string} code - OAuth code parameter
 */
export const handleOAuthCallback = async (scmType, state, code) => {
  try {
    const url = new URL(`${base_url}/${SHOW_NAME}/oauth/${scmType}/callback`);
    url.searchParams.append('state', state);
    url.searchParams.append('code', code);

    const response = await fetch(url, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to handle OAuth callback');
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

// SCM Types enum
export const SCMType = {
  GITHUB: 'github',
  GITLAB: 'gitlab'
};

// SCM Auth Types enum
export const SCMAuthType = {
  OAUTH: 'oauth',
  PAT: 'pat',
  SSH: 'ssh'
};
