// hooks/useDashboard.js
import { useState, useCallback, useRef, useMemo } from 'react';
import { 
  getOverallStats, 
  getTenants, 
  getUsersByTenant, 
  getSessionsByUser, 
  getSessionDetails 
} from '../utils/api';

export const VIEWS = {
  OVERVIEW: 'overview',
  TENANT: 'tenant',
  USER: 'user',
  SESSION: 'session'
};

export const useDashboard = () => {
  // Core state
  const [currentView, setCurrentView] = useState(VIEWS.OVERVIEW);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Data state
  const [overallStats, setOverallStats] = useState(null);
  const [tenants, setTenants] = useState([]);
  const [users, setUsers] = useState([]);
  const [sessions, setSessions] = useState([]);
  const [sessionDetails, setSessionDetails] = useState(null);
   const [overallFilters, setOverallFilters] = useState({
       dateFrom: null,
    dateTo: null,
      status: 'all',
      service: "all"
    });

  // Selection state
  const [selectedTenant, setSelectedTenant] = useState(null);
  const [selectedUser, setSelectedUser] = useState(null);

  // Session filters (backward compatibility)
  const [dateFrom, setDateFrom] = useState(null);
  const [dateTo, setDateTo] = useState(null);

  // Global filters
  const [globalFilters, setGlobalFilters] = useState({
    dateFrom: null,
    dateTo: null,
    status: null
  });

  // Loading cache to prevent duplicate calls
  const loadingCache = useRef({
    stats: false,
    tenants: false,
    users: false,
    sessions: false,
    sessionDetails: false
  });

  // Data cache to prevent unnecessary API calls
  const dataCache = useRef({
    stats: null,
    tenants: null,
    users: new Map(), // tenantId -> users data
    sessions: new Map(), // `${tenantId}-${userId}-${filters}` -> sessions data
    sessionDetails: new Map() // taskId -> session details
  });

  // Abort controller for cancelling requests
  const abortControllerRef = useRef(null);

  // Helper to cancel ongoing requests
  const cancelOngoingRequests = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    abortControllerRef.current = new AbortController();
    return abortControllerRef.current.signal;
  }, []);

  // Error handler
  const handleError = useCallback((error, context = '') => {
    if (error.name === 'AbortError') return;
    
    console.error(`Dashboard error ${context}:`, error);
    setError({
      message: error.message || 'An unexpected error occurred',
      context,
      timestamp: new Date().toISOString()
    });
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Format date helper
  const formatDateForAPI = useCallback((dateTimeLocal) => {
    if (!dateTimeLocal) return null;
    return new Date(dateTimeLocal).toISOString();
  }, []);

  // Get cache key for sessions
  const getSessionsCacheKey = useCallback((tenantId, userId, filters = {}) => {
    return `${tenantId}-${userId}-${JSON.stringify(filters)}`;
  }, []);

  // Data loading functions with caching
  const loadOverallStats = useCallback(async (force = false) => {
    if (loadingCache.current.stats && !force) {
      return;
    }

    if (!force && dataCache.current.stats) {
      setOverallStats(dataCache.current.stats);
      return;
    }

    loadingCache.current.stats = true;
    setLoading(true);
    
    try {
      // Apply global filters to stats
      const filters = {
        dateFrom: formatDateForAPI(globalFilters.dateFrom),
        dateTo: formatDateForAPI(globalFilters.dateTo),
        status: globalFilters.status
      };
      
      const stats = await getOverallStats(filters);
      
      dataCache.current.stats = stats;
      setOverallStats(stats);
      setError(null);
      
    } catch (error) {
      handleError(error, 'loading overall stats');
    } finally {
      loadingCache.current.stats = false;
      setLoading(false);
    }
  }, [handleError, formatDateForAPI, globalFilters]);

  const loadTenants = useCallback(async (force = false) => {
    if (loadingCache.current.tenants && !force) {
      return;
    }

    if (!force && dataCache.current.tenants) {
      setTenants(dataCache.current.tenants);
      return;
    }

    loadingCache.current.tenants = true;
    setLoading(true);
    
    try {
      // Apply global filters to tenants
      const filters = {
        dateFrom: formatDateForAPI(globalFilters.dateFrom),
        dateTo: formatDateForAPI(globalFilters.dateTo),
        status: globalFilters.status
      };
      
      const tenantsData = await getTenants(filters);
      
      dataCache.current.tenants = tenantsData || [];
      setTenants(tenantsData || []);
      setError(null);
    } catch (error) {
      handleError(error, 'loading tenants');
    } finally {
      loadingCache.current.tenants = false;
      setLoading(false);
    }
  }, [handleError, formatDateForAPI, globalFilters]);

  const loadUsers = useCallback(async (tenantId,filters = {}, force = false,) => {
    if (!tenantId) return;

    const cacheKey = `${tenantId}-${JSON.stringify(globalFilters)}`;
    
    if (loadingCache.current.users && !force) {
      return;
    }

    if (!force && dataCache.current.users.has(cacheKey)) {
      setUsers(dataCache.current.users.get(cacheKey));
      return;
    }

    loadingCache.current.users = true;
    setLoading(true);
    
    try {
      // Apply global filters to users
      const apiFilters = {
        dateFrom: formatDateForAPI(filters.dateFrom),
        dateTo: formatDateForAPI(filters.dateTo),
        status: filters.status,
        service : filters.service
      };
 
      
      const usersData = await getUsersByTenant(tenantId, apiFilters);
      
      dataCache.current.users.set(cacheKey, usersData || []);
      setUsers(usersData || []);
      setError(null);
    } catch (error) {
      handleError(error, 'loading users');
    } finally {
      loadingCache.current.users = false;
      setLoading(false);
    }
  }, [handleError, formatDateForAPI, globalFilters]);

  const loadSessions = useCallback(async (tenantId, userId, sessionFilters = {}, force = false) => {
    if (!tenantId || !userId) return;

    const filters = {
      dateFrom: formatDateForAPI(sessionFilters.dateFrom || dateFrom || globalFilters.dateFrom),
      dateTo: formatDateForAPI(sessionFilters.dateTo || dateTo || globalFilters.dateTo),
      status: sessionFilters.status || globalFilters.status,
      service : sessionFilters.service 
    };

    const cacheKey = getSessionsCacheKey(tenantId, userId, filters);
    
    if (loadingCache.current.sessions && !force) {
      return;
    }

    if (!force && dataCache.current.sessions.has(cacheKey)) {
      setSessions(dataCache.current.sessions.get(cacheKey));
      return;
    }

    loadingCache.current.sessions = true;
    setLoading(true);
    
    try {
      const sessionsData = await getSessionsByUser(tenantId, userId, filters.dateFrom, filters.dateTo,filters.status,filters.service);
      
      dataCache.current.sessions.set(cacheKey, sessionsData || []);
      setSessions(sessionsData || []);
      setError(null);
    } catch (error) {
      handleError(error, 'loading sessions');
    } finally {
      loadingCache.current.sessions = false;
      setLoading(false);
    }
  }, [handleError, formatDateForAPI, dateFrom, dateTo, globalFilters, getSessionsCacheKey]);

  const loadSessionDetails = useCallback(async (taskId, force = false) => {
    if (!taskId) return;

    if (loadingCache.current.sessionDetails && !force) {
      return;
    }

    if (!force && dataCache.current.sessionDetails.has(taskId)) {
      setSessionDetails(dataCache.current.sessionDetails.get(taskId));
      return;
    }

    loadingCache.current.sessionDetails = true;
    setLoading(true);
    
    try {
      const details = await getSessionDetails(taskId);
      
      dataCache.current.sessionDetails.set(taskId, details);
      setSessionDetails(details);
      setError(null);
    } catch (error) {
      handleError(error, 'loading session details');
    } finally {
      loadingCache.current.sessionDetails = false;
      setLoading(false);
    }
  }, [handleError]);

  // Navigation functions
  const navigateToOverview = useCallback(() => {
    setCurrentView(VIEWS.OVERVIEW);
    setSelectedTenant(null);
    setSelectedUser(null);
    setSessionDetails(null);
    setSessions([]);
    setUsers([]);
  }, []);

  const navigateToTenant = useCallback(async (tenant,filters = {}) => {
    if (selectedTenant?.id === tenant?.id) {
      return;
    }

    setSelectedTenant(tenant);
    setCurrentView(VIEWS.TENANT);
    setSelectedUser(null);
    setSessionDetails(null);
    setSessions([]);

    await loadUsers(tenant.id,filters);
  }, [selectedTenant, loadUsers]);

  const navigateToTenantView = useCallback(() => {
    if (selectedTenant && currentView !== VIEWS.TENANT) {
      setCurrentView(VIEWS.TENANT);
      setSelectedUser(null);
      setSessionDetails(null);
      setSessions([]);
    }
  }, [selectedTenant, currentView]);

  const navigateToUser = useCallback(async (user,filter={}) => {
    if (!selectedTenant) return;
    
    if (selectedUser?.id === user?.id) {
      return;
    }

    setSelectedUser(user);
    setCurrentView(VIEWS.USER);
    setSessionDetails(null);
    
    await loadSessions(selectedTenant.id, user.id,filter);
  }, [selectedTenant, selectedUser, loadSessions]);

  const navigateToUserView = useCallback(() => {
    if (selectedUser && selectedTenant && currentView !== VIEWS.USER) {
      setCurrentView(VIEWS.USER);
      setSessionDetails(null);
    }
  }, [selectedUser, selectedTenant, currentView]);

  const navigateToSession = useCallback(async (taskId) => {
    setCurrentView(VIEWS.SESSION);
    await loadSessionDetails(taskId);
  }, [loadSessionDetails]);

  // Filter functions
  const updateGlobalFilters = useCallback((newFilters) => {
    setGlobalFilters(prev => {
      const updated = { ...prev, ...newFilters };
      return updated;
    });
  }, []);

  const applyFilters = useCallback(async () => {
    // Clear relevant caches when filters change
    dataCache.current.stats = null;
    dataCache.current.tenants = null;
    dataCache.current.users.clear();
    dataCache.current.sessions.clear();
    
    // Apply filters based on current view
    switch (currentView) {
      case VIEWS.OVERVIEW:
        await Promise.all([
          loadOverallStats(true),
          loadTenants(true)
        ]);
        break;
      case VIEWS.TENANT:
        await Promise.all([
          loadOverallStats(true),
          loadTenants(true)
        ]);
        if (selectedTenant) {
          await loadUsers(selectedTenant.id, true);
        }
        break;
      case VIEWS.USER:
        await Promise.all([
          loadOverallStats(true),
          loadTenants(true)
        ]);
        if (selectedTenant && selectedUser) {
          await loadUsers(selectedTenant.id, true);
          await loadSessions(selectedTenant.id, selectedUser.id, {}, true);
        }
        break;
      default:
        await Promise.all([
          loadOverallStats(true),
          loadTenants(true)
        ]);
        break;
    }
  }, [currentView, selectedTenant, selectedUser, globalFilters, loadOverallStats, loadTenants, loadUsers, loadSessions]);

  const clearFilters = useCallback(() => {
    setGlobalFilters({
      dateFrom: null,
      dateTo: null,
      status: null
    });
    setDateFrom(null);
    setDateTo(null);
  }, []);

  const clearAllFilters = useCallback(async () => {
    clearFilters();
    // Auto-apply after clearing
    setTimeout(async () => {
      await applyFilters();
    }, 100);
  }, [clearFilters, applyFilters]);

  // Session-specific filter functions (backward compatibility)
  const filterSessions = useCallback(async () => {
    if (selectedTenant && selectedUser) {
      await loadSessions(selectedTenant.id, selectedUser.id, { dateFrom, dateTo }, true);
    }
  }, [selectedTenant, selectedUser, dateFrom, dateTo, loadSessions]);

  // Refresh data
  const refreshData = useCallback(async () => {
    // Clear all caches
    dataCache.current.stats = null;
    dataCache.current.tenants = null;
    dataCache.current.users.clear();
    dataCache.current.sessions.clear();
    dataCache.current.sessionDetails.clear();
    
    // Always refresh stats and tenants
    await Promise.all([
      loadOverallStats(true),
      loadTenants(true)
    ]);

    // Refresh current view data
    if (currentView === VIEWS.TENANT && selectedTenant) {
      await loadUsers(selectedTenant.id, true);
    } else if (currentView === VIEWS.USER && selectedTenant && selectedUser) {
      await Promise.all([
        loadUsers(selectedTenant.id, true),
        loadSessions(selectedTenant.id, selectedUser.id, {}, true)
      ]);
    } else if (currentView === VIEWS.SESSION && sessionDetails?.task_id) {
      await loadSessionDetails(sessionDetails.task_id, true);
    }
  }, [currentView, selectedTenant, selectedUser, sessionDetails, loadOverallStats, loadTenants, loadUsers, loadSessions, loadSessionDetails]);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    loadingCache.current = {
      stats: false,
      tenants: false,
      users: false,
      sessions: false,
      sessionDetails: false
    };
  }, []);

  // Memoized return object
  return useMemo(() => ({
    // State
    currentView,
    loading,
    error,
    overallStats,
    tenants,
    users,
    sessions,
    sessionDetails,
    selectedTenant,
    selectedUser,
    dateFrom,
    dateTo,
    globalFilters,
    overallFilters,
    
    // Actions
    setDateFrom,
    setDateTo,
    navigateToOverview,
    navigateToTenant,
    navigateToTenantView,
    navigateToUser,
    navigateToUserView,
    navigateToSession,
    refreshData,
    filterSessions,
    clearError,
    setOverallFilters,
    
    // Global filter actions
    updateGlobalFilters,
    applyFilters,
    clearFilters,
    clearAllFilters,
    
    // Data loading functions
    loadOverallStats,
    loadTenants,
    
    // Utility functions
    cleanup
  }), [
    currentView, loading, error, overallStats, tenants, users, sessions, sessionDetails,
    selectedTenant, selectedUser, dateFrom, dateTo, globalFilters,overallFilters,
    setDateFrom, setDateTo, navigateToOverview, navigateToTenant, navigateToTenantView,
    navigateToUser, navigateToUserView, navigateToSession, refreshData, filterSessions,
    clearError, updateGlobalFilters, applyFilters, clearFilters, clearAllFilters,
    loadOverallStats, loadTenants, cleanup,setOverallFilters
  ]);
};