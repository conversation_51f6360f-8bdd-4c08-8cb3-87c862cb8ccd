"use client";

import { useRouter } from "next/navigation";
import { useState, useEffect, useContext } from "react";
import TableComponent from "@/components/SimpleTable/table";
import {
    getAllUsedPod,
    getAllAvailablePod,
    checkProjectIdToPod,
    deleteProjectIdToPod,
    deletePodByPodId,
    deleteAvailablePodByPodId,
    deleteAllAvailablePod,
    deleteAllUsedPod,
    deleteMultipleUsedPods
} from "@/utils/api";
import { Search, Trash2, TrashIcon, Box, RefreshCw, } from 'lucide-react';
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import DeleteProjectModal from "@/components/Modal/DeleteProjectModal";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";

const PodManagement = () => {
    const router = useRouter();
    const [usedPods, setUsedPods] = useState([]);
    const [availablePods, setAvailablePods] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [usedSearchTerm, setUsedSearchTerm] = useState("");
    const [availableSearchTerm, setAvailableSearchTerm] = useState("");
    const [checkProjectId, setCheckProjectId] = useState("");
    const [checkResult, setCheckResult] = useState([]);
    const [checkLoading, setCheckLoading] = useState(false);
    const [activeTab, setActiveTab] = useState('used');
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [deleteItem, setDeleteItem] = useState(null);
    const [deleteLoading, setDeleteLoading] = useState(false);
    const [hasSearched, setHasSearched] = useState(false);
    const [selectedUsedPods, setSelectedUsedPods] = useState([]);
    const [refreshing, setRefreshing] = useState(false);

    const { showAlert } = useContext(AlertContext);

    // Headers for used pods table
    const usedPodHeaders = [
        { key: 'checkbox', label: '' },
        { key: 'unique_id', label: 'Unique ID' },
        { key: 'pod_name', label: 'Pod Name' },
        { key: 'pod_id', label: 'Pod ID' },
        { key: 'project_id', label: 'Project ID' },
        { key: 'status', label: 'Status' },
        { key: 'assigned_at', label: 'Assigned At' },
         { key: 'age', label: 'Age' },
        { key: 'action', label: 'Action' }
    ];

    // Headers for available pods table
    const availablePodHeaders = [
        { key: 'name', label: 'Name' },
        { key: 'project_id', label: 'Project ID' },
        { key: 'ready', label: 'Ready' },
        { key: 'status', label: 'Status' },
        { key: 'restarts', label: 'Restarts' },
        { key: 'ip', label: 'IP Address' },
        { key: 'node', label: 'Node' },
        { key: 'age', label: 'Age' },
        { key: 'action', label: 'Action' }
    ];

    const checkPodHeaders = usedPodHeaders;


    // Fetch data from both endpoints

    const fetchPods = async () => {
        try {
            setLoading(true);
            setError(null);

            const usedPodsResponse = await getAllUsedPod();
            if (usedPodsResponse.status === 'success') {
                setUsedPods(usedPodsResponse.data);
            }

            const availablePodsResponse = await getAllAvailablePod();
            if (availablePodsResponse.status === 'success') {
                setAvailablePods(availablePodsResponse.data);

            }

        } catch (err) {

            setError('Failed to fetch pod data');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchPods();
    }, []);

    // Update filtered data based on active tab
    useEffect(() => {
        if (activeTab === "used") {
            const formattedUsedPods = usedPods.map((pod) => ({
                ...pod,
                assigned_at: (() => {
                    try {
                       
                        const timestamp = pod.assigned_at.endsWith('Z') ? pod.assigned_at : pod.assigned_at + 'Z';
                        const date = new Date(timestamp);

                        // Validate date and convert to local time format
                        return isNaN(date.getTime())
                            ? '--:--'
                            : date.toLocaleTimeString('en-US', {
                                month: 'short',
                                day: 'numeric',
                                year: 'numeric',
                                hour: 'numeric',
                                minute: '2-digit',
                                hour12: true
                            });
                    } catch {
                        return '--:--';
                    }
                })(),

                age: (() => {
                    try {
                       
                        const timestamp = pod.assigned_at.endsWith('Z') ? pod.assigned_at : pod.assigned_at + 'Z';
                        const assignedDate = new Date(timestamp);
                        const currentDate = new Date();

                      
                        if (isNaN(assignedDate.getTime())) {
                            return '--';
                        }

                        // Calculate difference in milliseconds
                        const diffMs = currentDate.getTime() - assignedDate.getTime();

                        // If the assigned time is in the future, return '--'
                        if (diffMs < 0) {
                            return '--';
                        }

                        // Convert to different time units
                        const diffMinutes = Math.floor(diffMs / (1000 * 60));
                        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
                        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

                        // Return appropriate format based on time elapsed
                        if (diffMinutes < 1) {
                            return 'Just now';
                        } else if (diffMinutes < 60) {
                            return `${diffMinutes} ${diffMinutes === 1 ? 'minute' : 'minutes'} ago`;
                        } else if (diffHours < 24) {
                            return `${diffHours} ${diffHours === 1 ? 'hour' : 'hours'} ago`;
                        } else {
                            return `${diffDays} ${diffDays === 1 ? 'day' : 'days'} ago`;
                        }
                    } catch {
                        return '--';
                    }
                })()
            }));


            const filtered = formattedUsedPods.filter((pod) =>
                Object.values(pod).some((val) =>
                    String(val).toLowerCase().includes(usedSearchTerm.toLowerCase())
                )
            );

            setFilteredData(filtered);
        } else if (activeTab === "available") {
            const getRelativeTime = (date) => {
                const now = new Date();
                const past = new Date(date);
                const diffInSeconds = Math.floor((now - past) / 1000);

                const rtf = new Intl.RelativeTimeFormat('en', { numeric: 'auto' });

                if (diffInSeconds < 60) {
                    return rtf.format(-diffInSeconds, 'second');
                } else if (diffInSeconds < 3600) {
                    const minutes = Math.floor(diffInSeconds / 60);
                    return rtf.format(-minutes, 'minute');
                } else if (diffInSeconds < 86400) {
                    const hours = Math.floor(diffInSeconds / 3600);
                    return rtf.format(-hours, 'hour');
                } else {
                    const days = Math.floor(diffInSeconds / 86400);
                    return rtf.format(-days, 'day');
                }
            };

            const formattedAvailablePods = availablePods.map((pod) => ({
                ...pod,
                age: getRelativeTime(pod.age),
            }));


            const filtered = formattedAvailablePods.filter((pod) =>
                Object.values(pod).some((val) =>
                    String(val).toLowerCase().includes(availableSearchTerm.toLowerCase())
                )
            );

            setFilteredData(filtered);
        }
    }, [activeTab, usedSearchTerm, availableSearchTerm, usedPods, availablePods]);

    const handleCheckProject = async (projectId) => {
        if (!projectId) return;
        setCheckLoading(true);
        setHasSearched(true);

        try {
            const numericProjectId = Number(projectId.trim());

            if (isNaN(numericProjectId)) {
                setCheckResult([]);
                return;
            }
            const response = await checkProjectIdToPod(numericProjectId);
            if (response.status === "success") {
                const formatted = response.data.map(p => ({
                    ...p,
                    assigned_at: new Date(p.assigned_at).toLocaleString()
                }));
                setCheckResult(formatted);
            } else {
                setCheckResult([]);
            }
        } catch (err) {
            setCheckResult([]);
        } finally {
            setCheckLoading(false);
        }
    };

    // Handle refresh
    const handleRefresh = async () => {
        setRefreshing(true);
        await fetchPods();
        setRefreshing(false);
    };

    // Handle checkbox selection for used pods
    const handleCheckboxChange = (podId, checked) => {
        if (checked) {
            setSelectedUsedPods(prev => [...prev, podId]);
        } else {
            setSelectedUsedPods(prev => prev.filter(id => id !== podId));
        }
    };


    const handleDeleteClick = (item, type) => {
        setDeleteItem({ ...item, type });
        setShowDeleteModal(true);
    };

    // Handle bulk delete actions
    const handleBulkDeleteClick = (type) => {
        if (type === 'multiple-used' && selectedUsedPods.length === 0) {
            showAlert("Please select pods to delete", "warning");
            return;
        }

        setDeleteItem({
            type,
            count: type === 'multiple-used' ? selectedUsedPods.length : null,
            selectedPods: type === 'multiple-used' ? selectedUsedPods : null
        });
        setShowDeleteModal(true);
    };

    // Handle delete confirmation
    const handleDeleteConfirm = async () => {
        if (!deleteItem) return;

        setDeleteLoading(true);
        try {
            let response;

            switch (deleteItem.type) {
                case 'project':
                    response = await deleteProjectIdToPod(deleteItem.project_id);
                    break;

                case 'pod':
                    response = await deletePodByPodId(deleteItem.pod_id);
                    break;

                case 'available-pod':
                    const fullName = deleteItem.pod_id || deleteItem.name;
                    const extractedName = fullName?.match(/^\d+/)?.[0];
                    response = await deleteAvailablePodByPodId(extractedName);
                    break;

                case 'all-available':
                    response = await deleteAllAvailablePod();
                    break;

                case 'all-used':
                    response = await deleteAllUsedPod();
                    break;

                case 'multiple-used':
                    response = await deleteMultipleUsedPods({
                        pod_ids: deleteItem.selectedPods
                    });
                    break;

                default:
                    throw new Error('Unknown delete type');
            }

            if (response.status === 'success') {
                let successMessage = "Deleted successfully";

                switch (deleteItem.type) {
                    case 'multiple-used':
                        successMessage = `${deleteItem.count} pods deleted successfully`;
                        setSelectedUsedPods([]); // Clear selection
                        break;
                    case 'all-available':
                        successMessage = "All available pods deleted successfully";
                        break;
                    case 'all-used':
                        successMessage = "All used pods deleted successfully";
                        setSelectedUsedPods([]); // Clear selection
                        break;
                    default:
                        successMessage = "Pod deleted successfully";
                }

                showAlert(successMessage, "success");

                // Refresh data based on delete type
                if (deleteItem.type === 'project' && checkProjectId) {
                    await handleCheckProject(checkProjectId);
                } else {
                    // Refresh pod data
                    const usedPodsResponse = await getAllUsedPod();
                    if (usedPodsResponse.status === 'success') {
                        setUsedPods(usedPodsResponse.data);
                    }

                    const availablePodsResponse = await getAllAvailablePod();
                    if (availablePodsResponse.status === 'success') {
                        setAvailablePods(availablePodsResponse.data);
                    }
                }

                setShowDeleteModal(false);
                setDeleteItem(null);
            }
        } catch (error) {
            showAlert("Failed to delete. Please try again.", "error");
        } finally {
            setDeleteLoading(false);
        }
    };

    const onRowClick = (rowData) => {

    };

    const handleTableDelete = (row) => {
        if (activeTab === 'used') {
            handleDeleteClick(row, 'pod');
        } else if (activeTab === 'available') {
            handleDeleteClick(row, 'available-pod');
        }
    };

    const handleCloseModel = () => {
        setShowDeleteModal(false);
        setDeleteItem(null);
    };

    // Custom row renderer for checkboxes
    const renderTableWithCheckbox = () => {
        const dataWithCheckbox = filteredData.map(pod => ({
            ...pod,
            checkbox: (
                <input
                    type="checkbox"
                    checked={selectedUsedPods.includes(pod.pod_id)}
                    onChange={(e) => handleCheckboxChange(pod.pod_id, e.target.checked)}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:outline-none focus:ring-0"
                />
            )
        }));

        return (
            <TableComponent
                data={dataWithCheckbox}
                onRowClick={onRowClick}
                headers={getCurrentHeaders()}
                sortableColumns={getSortableColumns()}
                itemsPerPage={20}
                onDelete={handleTableDelete}
                type={activeTab === 'used' ? 'pod' : activeTab === 'available' ? 'available-pod' : undefined}
            />
        );
    };

    const SkeletonCard = () => (
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm animate-pulse">
            <div className="flex justify-between items-start mb-4">
                <div className="h-6 bg-gray-200 rounded w-32"></div>
                <div className="h-5 w-5 bg-gray-200 rounded"></div>
            </div>
            <div className="space-y-3">
                <div className="flex justify-between">
                    <div className="h-4 bg-gray-200 rounded w-20"></div>
                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                </div>
                <div className="flex justify-between">
                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                    <div className="h-4 bg-gray-200 rounded w-20"></div>
                </div>
                <div className="flex justify-between">
                    <div className="h-4 bg-gray-200 rounded w-24"></div>
                    <div className="h-4 bg-gray-200 rounded w-12"></div>
                </div>
                <div className="flex justify-between">
                    <div className="h-4 bg-gray-200 rounded w-14"></div>
                    <div className="h-6 w-16 bg-gray-200 rounded-full"></div>
                </div>
                <div className="flex justify-between">
                    <div className="h-4 bg-gray-200 rounded w-20"></div>
                    <div className="h-4 bg-gray-200 rounded w-28"></div>
                </div>
            </div>
        </div>
    );

    const SkeletonCardGrid = ({ count = 3 }) => (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array.from({ length: count }, (_, index) => (
                <SkeletonCard key={index} />
            ))}
        </div>
    );

    const getCurrentHeaders = () => {
        if (activeTab === 'used') return usedPodHeaders;
        if (activeTab === 'available') return availablePodHeaders;
        return checkPodHeaders;
    };


    const SkeletonLoader = () => {
        return (
            <div className="animate-pulse">
                {/* Header Skeleton */}
                <header className="bg-white shadow-sm border-b border-gray-200">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="flex items-center justify-between h-16">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <div className="w-8 h-8 bg-gray-300 rounded-lg"></div>
                                </div>
                                <div className="ml-3">
                                    <div className="h-6 bg-gray-300 rounded w-32 mb-1"></div>
                                    <div className="h-4 bg-gray-200 rounded w-40"></div>
                                </div>
                            </div>
                            <div className="h-10 bg-gray-300 rounded-lg w-24"></div>
                        </div>
                    </div>
                </header>

                <div className="bg-gray-50 min-h-screen">
                    {/* Stats Cards Skeleton */}
                    <div className="flex justify-center pt-8 pb-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                            {Array.from({ length: 3 }, (_, i) => (
                                <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse min-w-[250px]">
                                    <div className="flex items-center justify-between flex-row-reverse">
                                        <div className="w-8 h-8 bg-gray-200 rounded-full" />
                                        <div className="mr-4 flex-1">
                                            <div className="h-4 bg-gray-200 rounded mb-2 w-24" />
                                            <div className="h-6 bg-gray-200 rounded w-16" />
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>


                    {/* Main Content Area Skeleton */}
                    <div className="flex justify-center px-6 pb-8">
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 w-full max-w-7xl p-6">

                            {/* Tabs Skeleton */}
                            <div className="flex space-x-4 border-b mb-6">
                                {[...Array(3)].map((_, index) => (
                                    <div key={index} className="flex items-center space-x-2 px-4 py-2">
                                        <div className="w-4 h-4 bg-gray-300 rounded"></div>
                                        <div className="h-4 bg-gray-300 rounded w-20"></div>
                                    </div>
                                ))}
                            </div>

                            {/* Action Buttons Skeleton */}
                            <div className="flex justify-end mb-5">
                                <div className="flex gap-3 flex-wrap">
                                    <div className="relative">
                                        <div className="h-10 bg-gray-300 rounded-md w-64"></div>
                                    </div>
                                    <div className="h-10 bg-gray-300 rounded w-32"></div>
                                    <div className="h-10 bg-gray-300 rounded w-36"></div>
                                </div>
                            </div>

                            {/* Table Skeleton */}
                            <div className="overflow-hidden">
                                {/* Table Header */}
                                <div className="bg-gray-50 border-b border-gray-200">
                                    <div className="grid grid-cols-6 gap-4 px-6 py-3">
                                        {[...Array(6)].map((_, index) => (
                                            <div key={index} className="h-4 bg-gray-300 rounded"></div>
                                        ))}
                                    </div>
                                </div>

                                {/* Table Rows */}
                                {[...Array(8)].map((_, rowIndex) => (
                                    <div key={rowIndex} className="border-b border-gray-100">
                                        <div className="grid grid-cols-6 gap-4 px-6 py-4">
                                            {[...Array(6)].map((_, colIndex) => (
                                                <div key={colIndex} className="h-4 bg-gray-200 rounded"></div>
                                            ))}
                                        </div>
                                    </div>
                                ))}
                            </div>

                            {/* Pagination Skeleton */}
                            <div className="flex items-center justify-between mt-6">
                                <div className="h-4 bg-gray-300 rounded w-32"></div>
                                <div className="flex space-x-2">
                                    {[...Array(5)].map((_, index) => (
                                        <div key={index} className="w-8 h-8 bg-gray-300 rounded"></div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    };


    const getSortableColumns = () => {
        if (activeTab === 'used') {
            return {
                unique_id: true,
                pod_name: true,
                project_id: true,
                status: true,
                assigned_at: true
            };
        } else {
            return {
                name: true,
                project_id: true,
                status: true,
                restarts: true,
                age: true
            };
        }
    };

    if (loading) {
        return <SkeletonLoader />
    }

    if (error) {
        return (
            <div className="flex justify-center items-center h-64">
                <div className="text-red-500 typography-body-lg">{error}</div>
            </div>
        );
    }

    return (
        <div className="p-6">

            <div className="">
                <header className="bg-white shadow-sm border-b border-gray-200">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="flex items-center justify-between h-16">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                                        <Box className="w-5 h-5 text-white" />
                                    </div>
                                </div>
                                <div className="ml-3">
                                    <h1 className="typography-heading-4 font-weight-semibold text-gray-900">
                                        Pod Management Dashboard
                                    </h1>
                                    <p className="typography-body-sm text-gray-500">
                                        Real-time monitoring & insights
                                    </p>
                                </div>
                            </div>

                            <button
                                onClick={handleRefresh}
                                disabled={refreshing}
                                className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors disabled:opacity-50"
                            >
                                <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
                                <span>Refresh</span>
                            </button>
                        </div>
                    </div>
                </header>
                <div className="bg-gray-50 min-h-screen">

                    <div className="flex justify-center pt-8 pb-6">

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {/* Used Pods */}
                            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all metric-card">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="typography-body-sm font-weight-medium text-gray-600">Used Pods</p>
                                        <p className="text-2xl font-bold text-yellow-600">
                                            {usedPods.length}
                                        </p>
                                        <p className="typography-body-sm mt-1 flex items-center">
                                            🟡 Currently in use
                                        </p>
                                    </div>
                                    <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                                        <Box className="w-6 h-6 text-yellow-600" />
                                    </div>
                                </div>
                            </div>

                            {/* Available Pods */}
                            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all metric-card">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="typography-body-sm font-weight-medium text-gray-600">Available Pods</p>
                                        <p className="text-2xl font-bold text-green-600">
                                            {availablePods.length}
                                        </p>
                                        <p className="typography-body-sm mt-1 flex items-center">
                                            🟢 Ready for use
                                        </p>
                                    </div>
                                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                        <Box className="w-6 h-6 text-green-600" />
                                    </div>
                                </div>
                            </div>

                            {/* Total Pods */}
                            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all metric-card">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="typography-body-sm font-weight-medium text-gray-600">Total Pods</p>
                                        <p className="text-2xl font-bold text-blue-600">
                                            {usedPods.length + availablePods.length}
                                        </p>
                                        <p className="typography-body-sm mt-1 flex items-center">
                                            Sum of used and available pods
                                        </p>
                                    </div>
                                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <Box className="w-6 h-6 text-blue-600" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="flex justify-center px-6 pb-8">
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 w-full max-w-7xl p-6">

                            <div className="flex space-x-4 border-b">
                                {['used', 'available', 'check'].map(tab => (
                                    <button
                                        key={tab}
                                        className={`flex items-center space-x-2 px-4 py-2 font-weight-medium ${activeTab === tab ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
                                        onClick={() => setActiveTab(tab)}
                                    >
                                        <Box className="w-4 h-4" />
                                        <span>
                                            {tab === 'used' && `Used Pods `}
                                            {tab === 'available' && `Available Pods `}
                                            {tab === 'check' && 'Check Project ID to Pod'}
                                        </span>
                                    </button>
                                ))}
                            </div>

                            {/*  Action Buttons */}
                            {(activeTab === 'used' || activeTab === 'available') && (
                                <div className=" flex justify-end">
                                    {activeTab === 'used' && (
                                        <div className=" mb-5 flex gap-3 flex-wrap  mt-5">
                                            <div className="relative">
                                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                                                <input
                                                    type="text"
                                                    placeholder="Search used pods..."
                                                    className="border border-gray-300 rounded-md p-2 pl-10 w-64"
                                                    value={usedSearchTerm}
                                                    onChange={(e) => setUsedSearchTerm(e.target.value)}
                                                />
                                            </div>
                                            <DynamicButton
                                                variant="danger"
                                                size="medium"
                                                icon={Trash2}
                                                text={`Delete Selected (${selectedUsedPods.length})`}
                                                onClick={() => handleBulkDeleteClick('multiple-used')}
                                                disabled={selectedUsedPods.length === 0}
                                                tooltip="Delete selected pods"
                                            />
                                            <DynamicButton
                                                variant="danger"
                                                size="medium"
                                                icon={TrashIcon}
                                                text="Delete All Used Pods"
                                                onClick={() => handleBulkDeleteClick('all-used')}
                                                disabled={usedPods.length === 0}
                                                tooltip="Delete all used pods"
                                            />
                                        </div>
                                    )}

                                    {activeTab === 'available' && (
                                        <div className="mb-5 flex gap-3 flex-wrap mt-5">
                                            <div className="relative">
                                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                                                <input
                                                    type="text"
                                                    placeholder="Search available pods..."
                                                    className="border border-gray-300 rounded-md p-2 pl-10 w-64"
                                                    value={availableSearchTerm}
                                                    onChange={(e) => setAvailableSearchTerm(e.target.value)}
                                                />
                                            </div>
                                            <DynamicButton
                                                variant="danger"
                                                size="medium"
                                                icon={TrashIcon}
                                                text="Delete All Available Pods"
                                                onClick={() => handleBulkDeleteClick('all-available')}
                                                disabled={availablePods.length === 0}
                                                tooltip="Delete all available pods"
                                            />
                                        </div>
                                    )} </div>)}


                            {activeTab === "check" && (
                                <div className="mb-6">
                                    <p className="text-gray-600 mb-2 mt-5">
                                        Enter a project ID below to check associated pods.
                                    </p>
                                    <div className="relative mb-4">
                                        <div className="space-x-4">
                                            <input
                                                type="text"
                                                placeholder="Enter Project ID..."
                                                className="border border-gray-300 rounded-md p-2 pl-10 w-64"
                                                value={checkProjectId}
                                                onChange={(e) => {
                                                    const value = e.target.value;
                                                    setCheckProjectId(value);
                                                }}
                                            />
                                            <DynamicButton
                                                variant="primary"
                                                size="medium"
                                                text="Find Pod"
                                                onClick={() => handleCheckProject(checkProjectId)}
                                                tooltip="Find Pod for the project id"
                                                disabled={!checkProjectId.trim()}
                                            />
                                        </div>
                                    </div>
                                    {checkLoading && (
                                        <SkeletonCardGrid count={3} />
                                    )}
                                    {checkProjectId && !checkLoading && checkResult.length === 0 && hasSearched && (
                                        <EmptyStateView type="noSearchResult" />
                                    )}
                                    {!checkLoading && checkResult.length > 0 && (
                                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                            {checkResult.map((pod) => (
                                                <div key={pod.unique_id} className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                                                    <div className="flex justify-between items-start mb-4">
                                                        <h3 className="typography-body-lg font-weight-semibold text-gray-900">{pod.pod_name}</h3>
                                                        <button
                                                            onClick={() => handleDeleteClick(pod, 'pod')}
                                                            className="text-red-500 hover:text-red-700 p-1"
                                                            title="Delete pod mapping"
                                                        >
                                                            <Trash2 size={18} />
                                                        </button>
                                                    </div>
                                                    <div className="space-y-2">
                                                        <div className="flex justify-between">
                                                            <span className="text-gray-600">Unique ID:</span>
                                                            <span className="font-weight-medium">{pod.unique_id}</span>
                                                        </div>
                                                        <div className="flex justify-between">
                                                            <span className="text-gray-600">Pod ID:</span>
                                                            <span className="font-weight-medium">{pod.pod_id}</span>
                                                        </div>
                                                        <div className="flex justify-between">
                                                            <span className="text-gray-600">Project ID:</span>
                                                            <span className="font-weight-medium">{pod.project_id}</span>
                                                        </div>
                                                        <div className="flex justify-between">
                                                            <span className="text-gray-600">Status:</span>
                                                            <span className={`px-2 py-1 rounded-full typography-caption font-weight-medium ${pod.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                                                {pod.status}
                                                            </span>
                                                        </div>
                                                        <div className="flex justify-between">
                                                            <span className="text-gray-600">Assigned At:</span>
                                                            <span className="font-weight-medium typography-body-sm">{pod.assigned_at}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            )}

                            {/* Table Component */}
                            {(activeTab === 'used' || activeTab === 'available') && (
                                filteredData.length > 0 ? (
                                    activeTab === 'used' ? renderTableWithCheckbox() : (
                                        <TableComponent
                                            data={filteredData}
                                            onRowClick={onRowClick}
                                            headers={getCurrentHeaders()}
                                            sortableColumns={getSortableColumns()}
                                            itemsPerPage={20}
                                            onDelete={handleTableDelete}
                                            type="available-pod"
                                        />
                                    )
                                ) : (
                                    <EmptyStateView type="noSearchResult" />
                                )
                            )}
                        </div>
                    </div>
                </div>
            </div>


            <DeleteProjectModal
                isOpen={showDeleteModal}
                onClose={handleCloseModel}
                onDelete={handleDeleteConfirm}
                isDeleting={deleteLoading}
                type={deleteItem?.type || "pod"}
                deleteItem={deleteItem}
            />
        </div>
    );
};

export default PodManagement;