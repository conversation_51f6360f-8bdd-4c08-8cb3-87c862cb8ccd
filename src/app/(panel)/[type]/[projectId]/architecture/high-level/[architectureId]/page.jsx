"use client"
import ArchitectureDetails from "@/components/BrowsePanel/Architecture/ArchitectureDetails";
import { getArchitectureById } from "@/utils/api"
import { usePathname } from 'next/navigation';
import { useContext, useState, useEffect } from "react";
import { ArchitectureContext } from "@/components/Context/ArchitectureContext";
import { useSearchParams } from 'next/navigation';
const Page = ({ params }) => {
  const { selectedArchitecture, setDataType } = useContext(ArchitectureContext);
  const searchParams = useSearchParams();
  const pathname = usePathname()
  const projectId = pathname.split("/")[2];
  const architectureId = params.architectureId
  const [data, setData] = useState({
  });

  useEffect(() => {
    const fetchData = async () => {
      let node = await getArchitectureById(architectureId, projectId);
      setDataType(node.properties.Type)
      setData(node);
    }
    fetchData();
  }
    , [params.architectureId, searchParams]);

  if (selectedArchitecture && selectedArchitecture.id == params.architectureId) {
    return (
      <div className='h-full overflow-y-auto custom-scrollbar'>
        <div className="relative">
          <ArchitectureDetails data={selectedArchitecture} />
        </div>
      </div>
    );
  }

  return (
    <div className='h-full overflow-y-auto custom-scrollbar'>
      <div className="relative">
        <ArchitectureDetails data={data} />
      </div>
    </div>
  );
};


export default Page;