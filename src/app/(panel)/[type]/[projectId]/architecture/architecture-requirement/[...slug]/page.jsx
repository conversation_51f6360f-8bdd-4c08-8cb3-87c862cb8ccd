"use client";
import React, { useEffect, useState } from "react";
import "@/styles/tabs/architecture/architectureRequirement.css";
import { usePathname, useRouter } from "next/navigation";
import { fetchRelatedNodes } from "@/utils/api";
import en from "@/en.json";
import TableComponent from "@/components/SimpleTable/ArchitectureTable";
import Badge from "@/components/UIComponents/Badge/Badge";
import ErrorView from "@/components/Modal/ErrorViewModal";
import { IconButton } from "@/components/UIComponents/Buttons/IconButton";
import { ArrowLeft } from "lucide-react";
import { Accordion } from "@/components/UIComponents/Accordions/Accordion";
import NavigationTree from "@/components/Modal/NavigationTreeComponent";
import { TwoColumnSkeletonLoader } from "@/components/UIComponents/Loaders/LoaderGroup";
import { useResponsiveDimensions } from "@/utils/responsiveness";

const Page = ({ params }) => {
  const [nodeData, setNodeData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const pathname = usePathname();
  const router = useRouter();
  const projectId = pathname.split("/")[2];
  const childType = params.slug[0];
  const childId = params.slug[1];
  const { mainContentWidth, calculateDimensions } = useResponsiveDimensions();

  const fetchData = async () => {
    try {

      const data = await fetchRelatedNodes(projectId, childId, childType);
      setNodeData(data);
    } catch (err) {

      if (err.message && err.message.includes("status: 500")) {
        setError(`There was an internal server error while fetching ${childType}. Please try again later.`);
      } else {
        setError(`Failed to load ${childType}. Please check your connection or try again later.`);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [projectId, childId, childType]);

  useEffect(() => {
    calculateDimensions();
  }, [calculateDimensions]);

  if (loading) return <TwoColumnSkeletonLoader />;
  if (error)
    return (
      <ErrorView
        title={`Unable to Load ${childType}`}
        message={error}
        onRetry={() => fetchData()}
        panelType="main"
      />
    );
  if (!nodeData) return <div>{en.ArchitectureRequirementNotFound}</div>;

  const headers = [
    { key: "title", label: "Title" },
    { key: "type", label: "Type" },
    { key: "description", label: "Description" },
  ];

  const tableData = nodeData.relatedNodes.map((data) => ({
    id: data.id,
    title: data.properties.Title,
    type: data.properties.Type,
    description: data.properties.Description,
  }));

  const handleRowClick = (node) => {
    router.push(`/project/${projectId}/requirements/${node.type}/${node.id}`);
  };

  const handleGoBack = () => {
    router.back();
  };

  const treeData = [
    ...Object.entries(nodeData?.properties || {})
      .filter(
        ([key, value]) =>
          !["Title", "Type", "RelatedUserStoryIDs"].includes(key) &&
          value.hidden !== true
      )
      .map(([key, value]) => ({
        id: `${(value.Label || key).toLowerCase().replace(/[_\s]+/g, "-")}`,
        name: (value.Label || key).replace(/[_-]/g, " "),
        children: [],
      })),
    ...(nodeData.relatedNodes?.length > 0
      ? [
          {
            id: "related-nodes",
            name: "Related Nodes",
            children: [],
          },
        ]
      : []),
  ];

  const handleScrollToSection = (id) => {
    const element = document.getElementById(id);
    const mainContent = document.getElementById("main-content");

    if (element && mainContent) {
      const topOffset = element.offsetTop;
      mainContent.scrollTo({
        top: topOffset - 80,
        behavior: "smooth",
      });
    }
  };

  const HeaderSection = () => (
    <div className="flex flex-col space-y-4 top-1" style={{ zIndex: 5 }}>
      <div className="flex flex-col border border-gray-100">
        <div className="relative px-2 py-2 space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <IconButton
                icon={<ArrowLeft className="w-5 h-5 text-gray-600" />}
                tooltip="Go back"
                onClick={handleGoBack}
                className="hover:bg-gray-100"
              />
              <div className="flex items-center gap-2">
                <h2 className="typography-body-lg font-weight-semibold text-gray-800">
                  {nodeData.properties.Title}
                </h2>
                <div className="flex items-center gap-1">
                  <Badge type={nodeData.properties.Type} />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-100/50 via-blue-300/20 to-transparent" />
        </div>
      </div>
    </div>
  );

  return (
    <div className="relative flex max-h-[78vh] overflow-hidden bg-white-50">
      <div>
        {treeData && (
          <NavigationTree treeData={treeData} handleScrollToSection={handleScrollToSection} />
        )}
      </div>
      <main
        id="main-content"
        className="
          flex-1
          relative
          overflow-y-auto
          overflow-x-hidden
          transition-all
          duration-300
          ease-in-out
        "
        style={{ width: mainContentWidth }}
      >
        <div className="w-full pl-4 pr-4">
          <HeaderSection />

          <div className="architecture-requirement-child-node-description-wrapper">
            <Accordion title="Description" defaultOpen>
              {nodeData.properties.Description || "Description"}
            </Accordion>
          </div>

          <div id="related-nodes" className="architecture-requirement-child-node-related-node">
            <div
              className={`${
                nodeData.relatedNodes.length > 0 ? "border rounded-lg overflow-hidden" : ""
              }`}
            >
              {nodeData.relatedNodes.length > 0 ? (
                <TableComponent
                  data={tableData}
                  onRowClick={handleRowClick}
                  headers={headers}
                  sortableColumns={{ title: true, type: true }}
                  itemsPerPage={20}
                  component="architecture-child-requirements"
                  title="Related Nodes"
                />
              ) : (
                <></>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Page;
