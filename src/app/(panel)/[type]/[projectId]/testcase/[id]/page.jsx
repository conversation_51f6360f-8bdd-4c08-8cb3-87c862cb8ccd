"use client";

import { useEffect, useState } from "react";
import { useRouter, usePathname } from "next/navigation";

const TestCaseRedirect = () => {
  const pathname = usePathname();
  const router = useRouter();
  const projectId = pathname.split("/")[2];
  const testCaseId = pathname.split("/")[4];
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchTestCaseAndRedirect = async () => {
      if (!projectId || !testCaseId) return;

      try {
        // Default type if we can't determine the actual type
        let testCaseType = "category";
        
        try {
          // Real implementation would fetch the test case data
          // Example:
          // const response = await fetch(`/api/projects/${projectId}/testcases/${testCaseId}`);
          // const testCase = await response.json();
          // if (testCase && testCase.type) {
          //   // Only remove spaces but preserve original casing
          //   testCaseType = testCase.type.replace(/\s+/g, '');
          // }
          
          // For demonstration, you can add mock data here:
          // Uncomment and modify this to test with specific types
          /*
          const mockTestCases = {
            "1": { type: "SampleTestCase" },
            "2": { type: "Integration" },
            "3": { type: "Functional" }
          };
          
          if (mockTestCases[testCaseId]) {
            // Only remove spaces but preserve original casing
            testCaseType = mockTestCases[testCaseId].type.replace(/\s+/g, '');
          }
          */
          
          
        } catch (error) {
          
        }

        // Redirect to the appropriate page based on the test case type
        router.replace(`/project/${projectId}/testcase/${testCaseId}/${testCaseType}`);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTestCaseAndRedirect();
  }, [projectId, testCaseId, router]);

  if (isLoading) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>;
  }

  return null;
};

export default TestCaseRedirect;
