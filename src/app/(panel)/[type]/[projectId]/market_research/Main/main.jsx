import React, { useState, useEffect, useContext } from "react";
import Modal from "./Modal";
import UploadModal from "./UploadModal";
import {
  usePathname,
  useSearchParams,
  useParams,
  useRouter,
} from "next/navigation";
import TableComponent from "../Secondary/Table";
import DetailView from "../Secondary/DetailView";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import CustomDropdown from '@/components/UIComponents/Dropdowns/CustomDropdown';
import { Settings, Plus, FileUp } from 'lucide-react';
import { TOOLTIP_CONTENT } from "@/utils/constant/tooltip";

const TabbedInterface = ({ handleMenuClick }) => {
  const [activeTab, setActiveTab] = useState("Secondary");
  const [isCreateOpen, setCreateOpen] = useState(false);
  const [isUploadOpen, setUploadOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [loading, setLoading] = useState(true);
  const connectedSettingsArray = ["Upload Document"];
  const pathname = usePathname();
  const router = useRouter();
  const searchParams = useSearchParams();
  const params = useParams();
  const projectId = params.projectId;
  const { showAlert } = useContext(AlertContext);


  useEffect(() => {
    const itemId = searchParams.get("item_id");
    if (itemId) {
      setLoading(true);
      const storedItem = sessionStorage.getItem("selectedItem");

      if (storedItem) {
        const item = JSON.parse(storedItem);
        setSelectedItem(item);
      }

      setLoading(false);
    } else {
      setLoading(false);
    }
  }, [searchParams]);

  const handleTabClick = (tab) => {
    setActiveTab(tab);
  };

  const handleCreateOpen = () => setCreateOpen(true);
  const handleCreateClose = () => setCreateOpen(false);

  const handleUploadOpen = () => setUploadOpen(true);
  const handleUploadClose = () => setUploadOpen(false);

  const _handleMenuClick = (item) => {
    if (item === "Upload Document") {
      handleUploadOpen();
    }
    handleMenuClick && handleMenuClick(item);
  };

  const handleOnUpdate = () => {
    
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", projectId);
    newSearchParams.set("node_type", "design");
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const handleUpdate = () => {
    
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", projectId);
    newSearchParams.set("node_type", "design");
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };
  const handleRowClick = (item) => {
    setLoading(true);
    
      setSelectedItem(item);
      sessionStorage.setItem("selectedItem", JSON.stringify(item));
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set("item_id", item.id); 
      setTimeout(() => {
        router.push(`${pathname}?${newSearchParams.toString()}`);
        setLoading(false); 
      }, 3000)
  };

  const handleBackToTable = () => {
    setSelectedItem(null);
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.delete("item_id");
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const showBtn = () => {
    showAlert("Functionality was not implemented", "info")
  }


  if (loading) {
    return (
      <div className="p-4 w-full mx-auto mt-[15vh] flex justify-center">
        <div className="spinner"></div>
      </div>
    );
  }
  const menuOptions = [
    {
      label: "Upload Document",
      icon: FileUp,
      onClick: handleUploadOpen,
      tooltip: TOOLTIP_CONTENT.marketReasearch.upload,
    },
  ];

  return (
    <>
      <div className="container mx-auto p-6 flex justify-between items-center">
        <div className="flex items-center space-x-2 bg-gray-100 rounded-md -mt-4">
          <button
            onClick={() => handleTabClick("Secondary")}
            className={`h-10 px-4 py-2 transition typography-body rounded-md ${activeTab === "Secondary"
              ? "bg-white shadow border border-b-0 text-blue-600"
              : "text-gray-600"
              }`}
          >
            Secondary Research
          </button>
          <button
            onClick={() => handleTabClick("Guided")}
            className={`h-10 px-4 py-2 transition typography-body rounded-md ${activeTab === "Guided"
              ? "bg-white shadow border border-b-0 text-blue-600"
              : "text-gray-600"
              }`}
          >
            Guided Research
          </button>
        </div>

        <div className="flex space-x-2 items-center">
          {!selectedItem && (
            <DynamicButton
              variant="square"
              size="sqDefault"
              tooltip="Create Secondary Research Item"
              placement="bottom"
              icon={Plus}
              onClick={handleCreateOpen}
            />
          )}
          {!selectedItem && (
            <DynamicButton
              type="submit"
              variant="primary"
              icon={Settings}
              onClick={handleOnUpdate}
              text="Update Secondary Research"
            />
          )}
          {selectedItem && (
            <DynamicButton
              type="submit"
              variant="primary"
              icon={Settings}
              onClick={handleOnUpdate}
              text="Update Steps"
            />
          )}

          <DynamicButton
            type="submit"
            variant="primary"
            icon={Plus}
            onClick={showBtn}
            text="Auto Configure"
          />

          <CustomDropdown
            options={menuOptions}
            align="right"
          />
        </div>
      </div>

      <div className="container mx-auto p-0 max-h-screen overflow-y-auto">
        {selectedItem ? (
          <div className="mb-16">
            <DetailView
              item={selectedItem}
              isGenerated={true}
              onBack={handleBackToTable}
              handleUpdate={handleUpdate}
            />
          </div>
        ) : (
          activeTab === "Secondary" && (
            <div>
              <TableComponent
                togglebuttonsList={["View Details"]}
                handleRowClick={handleRowClick}
              />
            </div>
          )
        )}
        {activeTab === "Guided" && (
          <div className="mt-6">
            <h1 className="flex justify-center  items-center text-center overflow-hidden">
              <EmptyStateView type="comingSoon" />
            </h1>
          </div>
        )}
      </div>

      <Modal showModal={isCreateOpen} onClose={handleCreateClose} />
      <UploadModal showModal={isUploadOpen} onClose={handleUploadClose} />
    </>
  );
};

export default TabbedInterface;
