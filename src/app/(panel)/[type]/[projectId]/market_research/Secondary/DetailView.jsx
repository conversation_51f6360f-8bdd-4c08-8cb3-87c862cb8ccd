import React, { useState } from "react";
import Image from "next/image";
import {
  FaArrowLeft,
  FaCog,
  FaExternalLinkAlt,
  FaChevronDown,
  FaChevronUp,
} from "react-icons/fa";
import { MdVerified } from "react-icons/md";
import logo from "../../../../../../../public/logo/kavia_logo.svg";

const Stepper = ({ steps, currentStep, setCurrentStep }) => {
  return (
    <div className="flex items-center justify-between mb-8 px-12">
      {steps.map((step, index) => (
        <div
          key={index}
          className="flex-1 flex flex-col justify-center items-center h-full"
        >
          <div className="flex items-center w-full">
            <div className="flex-shrink-0">
              <div
                onClick={() => setCurrentStep(index)}
                className={`w-8 h-8 flex items-center justify-center cursor-pointer ${
                  index <= currentStep
                    ? "bg-blue-500 text-white"
                    : "bg-gray-200 text-gray-600"
                }
                ${index + 1 === 5 ? "bg-white" : "rounded-full"}`}
              >
                {index + 1 === 5 ? (
                  index <= currentStep ? (
                    <MdVerified size={45} color="#3B82F6" />
                  ) : (
                    <MdVerified
                      size={45}
                      style={{
                        color: "#E5E7EB",
                      }}
                    />
                  )
                ) : (
                  index + 1
                )}
              </div>
            </div>
            {index < steps.length - 1 && (
              <div className="flex-grow h-1 mx-2">
                <div
                  className={`h-full ${
                    index < currentStep
                      ? "bg-blue-500"
                      : index === currentStep
                      ? "bg-gradient-to-r from-blue-500 to-gray-200"
                      : "bg-gray-200"
                  }`}
                ></div>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

const AccordionItem = ({ maintitle, items, isOpen, toggleOpen }) => {
  return (
    <div className="border-b border-gray-200 last:border-b-0">
      <button
        className="flex justify-between items-center w-full py-4 px-6 text-left project-panel-heading hover:bg-gray-50 focus:outline-none"
        onClick={toggleOpen}
      >
        {maintitle}
        {isOpen ? <FaChevronUp size={20} /> : <FaChevronDown size={20} />}
      </button>
      {isOpen && (
        <div className="border-t border-gray-200">
          {items.map((item, index) => (
            <div
              key={index}
              className="px-6 py-4 bg-gray-50 border-b border-gray-200 last:border-b-0"
            >
              <h2 className="project-panel-heading mb-2 ">{item.title}</h2>
              <p className="text-font">{item.content}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const Accordion = ({ items }) => {
  const [openIndex, setOpenIndex] = useState(true);

  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden">
      <AccordionItem
        key={"Outcome"}
        maintitle={"Outcome"}
        items={items}
        isOpen={openIndex}
        toggleOpen={() => setOpenIndex(!openIndex)}
      />
    </div>
  );
};

const OutcomeAccordion = () => {
  const accordionItems = [
    {
      title: "Accordion",
      content:
        "Porter's Five Forces Analysis is a framework used to evaluate the competitive forces within an industry: competitive rivalry, threat of new entrants, bargaining power of suppliers, bargaining power of buyers, and the threat of substitutes. It helps in understanding the market dynamics and strategic positioning.",
    },
    {
      title: "Identifying sources",
      content:
        "Porter's Five Forces Analysis is a framework used to evaluate the competitive forces within an industry: competitive rivalry, threat of new entrants, bargaining power of suppliers, bargaining power of buyers, and the threat of substitutes. It helps in understanding the market dynamics and strategic positioning.",
    },
    {
      title: "Framework designing",
      content:
        "Porter's Five Forces Analysis is a framework used to evaluate the competitive forces within an industry: competitive rivalry, threat of new entrants, bargaining power of suppliers, bargaining power of buyers, and the threat of substitutes. It helps in understanding the market dynamics and strategic positioning.",
    },
    {
      title: "Framework designing",
      content:
        "Porter's Five Forces Analysis is a framework used to evaluate the competitive forces within an industry: competitive rivalry, threat of new entrants, bargaining power of suppliers, bargaining power of buyers, and the threat of substitutes. It helps in understanding the market dynamics and strategic positioning.",
    },
    {
      title: "Framework designing",
      content:
        "Porter's Five Forces Analysis is a framework used to evaluate the competitive forces within an industry: competitive rivalry, threat of new entrants, bargaining power of suppliers, bargaining power of buyers, and the threat of substitutes. It helps in understanding the market dynamics and strategic positioning.",
    },
  ];

  return <Accordion items={accordionItems} />;
};

const DetailView = ({ item, isGenerated, onBack, handleUpdate }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isStepper, setStepper] = useState(true);
  const steps = isGenerated
    ? ["Objective", "Analysis", "Insights", "Conclusion", "Report"]
    : ["Objective", "Analysis", "Insights", "Conclusion"];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
    if (currentStep === steps.length - 1) {
      setStepper(false);
    }
  };

  const handlePrev = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };
  

  return (
    <div className="p-6 max-h-[calc(100vh-200px)] overflow-y-auto custom-scrollbar">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center mb-4">
          <button
            onClick={onBack}
            className="p-1 text-gray-600 hover:text-gray-800 transition-colors duration-200 flex items-center justify-center rounded-full bg-gray-200 mr-2"
          >
            <div className="bg-gray-200 rounded-full p-1 inline-flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-700" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
              </svg>
            </div>
          </button>
          <h2 className="project-panel-heading">{item.name}</h2>
        </div>
        <p className="mt-4 mb-6 text-gray-600 text-font">
          {item.description || "No additional information available."}
        </p>

        <div className="grid grid-cols-2 gap-4 text-left mb-4">
          <div className="flex items-center">
            <span className="project-panel-heading w-24">Created by</span>
            <Image
              src={logo}
              alt="KaviaAI Logo"
              width={24}
              height={24}
              className="mr-2"
            />
            <span className="">KaviaAI</span>
          </div>

          <div className="flex">
            <span className="project-panel-heading w-24">Date</span>
            <span className="text-font">{item.lastUpdate}</span>
          </div>

          <div className="flex">
            <span className="project-panel-heading w-24">Type</span>
            <span className="text-font">{item.type}</span>
          </div>

          <div className="flex items-center">
            <span className="project-panel-heading w-24">Priority</span>
            <span className="rounded-lg px-2 py-1 bg-gray-200 text-font">
              {item.priority || "LOW"}
            </span>
          </div>

          <div className="flex items-center">
            <span className="project-panel-heading w-24">Report</span>
            <a href="#" className="underline pr-2 text-font">
              View
            </a>
            <FaExternalLinkAlt />
          </div>
        </div>

        <div className="flex justify-between items-center mb-4">
          <h3 className="project-panel-heading">Steps</h3>
          <div className="flex items-center">
            <button
              className="h-10 px-4 py-2 bg-gray-100 border rounded-l-md hover:bg-gray-200 flex items-center"
              onClick={handleUpdate}
            >
              <FaCog size={18} className="mr-2" />
              Update Content
            </button>
          </div>
        </div>

        {isStepper && (
          <div style={{ margin: "0 80px" }}>
            <Stepper
              steps={steps}
              currentStep={currentStep}
              setCurrentStep={setCurrentStep}
            />

            <div className="mt-4 mb-8 border-t border-gray-200 pt-4">
              <h4 className="project-panel-heading">{steps[currentStep]}</h4>
              <p className="mt-2 text-gray-600 text-font">
                {currentStep === 0 && "Content for Objective..."}
                {currentStep === 1 && "Content for Analysis..."}
                {currentStep === 2 && "Content for Insights..."}
                {currentStep === 3 && "Content for Conclusion..."}
                {currentStep === 4 && "Content for Report..."}
              </p>
            </div>

            <div className="flex justify-between mt-6 mb-16 ml-12 mr-12">
              <button
                className={`px-8 py-1 rounded-md ${
                  currentStep === 0
                    ? "opacity-50 cursor-not-allowed"
                    :  "border border-blue-600 text-blue-600 bg-transparent hover:bg-blue-600 hover:text-white"
                }`}
                onClick={handlePrev}
                disabled={currentStep === 0}
              >
                <FaArrowLeft />
              </button>
              <button
                className={`px-4 py-2 rounded-md bg-blue-600 text-white `}
                onClick={handleNext}
              >
                {currentStep === 3
                  ? "Complete"
                  : currentStep === 4
                  ? "Generate"
                  : "Next"}
              </button>
            </div>
          </div>
        )}
        {!isStepper && <div className="mb-8">{OutcomeAccordion()}</div>}
      </div>
    </div>
  );
};

export default DetailView;
