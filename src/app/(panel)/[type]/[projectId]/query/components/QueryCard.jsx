import React, { useState, useRef, useEffect, useContext } from 'react';
import { MoreHorizontal, Trash2, Calendar } from 'lucide-react';
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";
import { deleteQueryDiscussion } from '@/utils/api';
import { AlertContext } from "@/components/NotificationAlertService/AlertList";

const QueryCard = ({ query, onImport, onClick }) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef(null);
  const { showAlert } = useContext(AlertContext);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleCardClick = () => {
    if (onClick) onClick(query);
  };

  const handleMoreClick = (e) => {
    e.stopPropagation();
    setShowDropdown(!showDropdown);
  };

  const handleDeleteClick = async (e) => {
    e.stopPropagation();
    try {
      const response = await deleteQueryDiscussion(query.id);
      if(response){
        showAlert('Query deleted successfully', 'success');
        onImport();
      }
    } catch (error) {
      
      showAlert('Failed to delete query', 'error');
    }
    setShowDropdown(false);
  };

  return (
    <div 
      className="p-5 border-b border-gray-200 hover:bg-gray-50 transition-colors py-4 cursor-pointer"
      onClick={handleCardClick}
    >
      <div className="flex flex-col">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-weight-medium typography-body text-gray-900">Discussion</h3>
          <div className="relative" ref={dropdownRef}>
            <BootstrapTooltip title="More options" placement="left">
              <button 
                className="p-1 hover:bg-gray-100 rounded-md"
                onClick={handleMoreClick}
              >
                <MoreHorizontal className="w-5 h-5 text-gray-400" />
              </button>
            </BootstrapTooltip>
            
            {showDropdown && (
              <div className="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                <BootstrapTooltip title="Remove this query" placement="left">
                  <button
                    className="flex items-center w-full px-4 py-2 typography-body-sm text-red-600 hover:bg-gray-50"
                    onClick={handleDeleteClick}
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete Query
                  </button>
                </BootstrapTooltip>
              </div>
            )}
          </div>
        </div>
        
        <p className="text-gray-600 typography-body-sm line-clamp-2 mb-2">
          {query.description}
        </p>

        <div className="flex items-center">
          <span className="flex items-center gap-2">
            <Calendar className="w-4 h-4 text-gray-400" />
            <span className="typography-caption text-gray-500">
              {query.timestamp}
            </span>
          </span>
        </div>
      </div>
    </div>
  );
};

export default QueryCard;