"use client";
import React, { useEffect, useState, useContext, useRef } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import QueryDiscussionModal from "./components/QueryDiscussionModal";
import GitRepoModal from "./components/GitRepoModal";
import { getkginfo, updateSelectedBranch } from "@/utils/gitAPI";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import RepositoryTable from "@/components/SimpleTable/RepositoryTable";
import { DiscussionChatContext } from "@/components/Context/DiscussionChatContext";
import { fetchQueryHistory, deleteRepoBranch } from "@/utils/api";
import { Plus, FileCode2, RefreshCw, Search, BookOpen, } from "lucide-react";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { ProjectAssetContext } from "@/components/Context/ProjectAssetContext";
import AdvancedQueryHandler from "@/app/modal/AdvancedQueryHandler";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import Badge from "@/components/Badge";
import WebSocketSessionsComponent from "@/components/WebSocket/WebSocketSessionsComponent";
import PastQueryModal from "./components/PastQueryModal";
import { getPastMaintenanceTasks } from "@/utils/batchAPI";
import { TableLoadingSkeleton } from "@/components/UIComponents/Loaders/LoaderGroup";
import { useRightContent } from "@/components/Context/RightContentContext";
import { useBuildProgress } from "@/components/Context/BuildProgressContext";
import { useUser } from "@/components/Context/UserContext";
import CodeGenerationModal from "@/app/modal/CodeGenerationModal";
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";
import { useWebSocket } from "@/components/Context/WebsocketContext";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
const ITEMS_PER_PAGE = 10;
const REPOSITORY_ITEMS_PER_PAGE = 10;

const QueryPage = () => {
  const [, setIsLoading] = useState(false);
  const [tableLoading, setTableLoading] = useState(true);
  const [queries, setQueries] = useState([]);
  const [searchQuery] = useState("");
  const [tableSearch, setTableSearch] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isPastModalOpen, setIsPastModalOpen] = useState(false);
  const [selectedRepositories, setSelectedRepositories] = useState([]);
  const [isRepoModalOpen, setIsRepoModalOpen] = useState(false);
  const [repositories, setRepositories] = useState([]);
  const [, setSelectedBuildIds] = useState([]);
  const { showAlert } = useContext(AlertContext);
  const { onClose, setInstantCloseStatus, wsConnection } = useContext(DiscussionChatContext);
  useContext(ProjectAssetContext);
  const [details, setDetails] = useState([]);
  const [totalQueryCount, setTotalQueryCount] = useState(0);
  const {refreshRepos, setRefreshRepos} = useWebSocket();
  const [totalAnalysisCount, setTotalAnalysisCount] = useState(0);
  const [limit, setLimit] = useState(() => {
    if(typeof window !== "undefined"){
      return localStorage.getItem('preferredPageSize') ? Number(localStorage.getItem('preferredPageSize')) : ITEMS_PER_PAGE
    }
    else {
      return 0;
    }
  });
  const [skip, setSkip] = useState(0);
  const [, setIsAdvancedQuery] = useState(false);
  const [showAdvancedQueryHandler, setShowAdvancedQueryHandler] = useState(false);
  const { isVisible, setIsVisible } = useCodeGeneration();
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [isQueryDropdownOpen, setIsQueryDropdownOpen] = useState(false);
  const [pastAnalysis, setPastAnalysis] = useState([]);
  const [isQueryLoading, setIsQueryLoading] = useState(false);
  const [isDeepAnalysisLoading, setIsDeepAnalysisLoading] = useState(false);

  const [currentPage, setCurrentPage] = useState(1);
  const [repositoryCurrentPage, setRepositoryCurrentPage] = useState(1);
  const [repositoryTotalItems, setRepositoryTotalItems] = useState(0);
  const [repositoryPageSize, setRepositoryPageSize] = useState(REPOSITORY_ITEMS_PER_PAGE);
  const [queryPage, setQueryPage] = useState(1);
  const [analysisPage, setAnalysisPage] = useState(1);

  const pathname = usePathname();
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathParts = pathname.split("/");
  const projectId = pathParts[2];
  const tabName = pathParts[3];
  const searchInputRef = useRef(null);
  const dropdownRef = useRef(null);

  const { setRightContent } = useRightContent();
  const { clearBuildProgress, initiateBuildStatus, buildStatus } = useBuildProgress();

  const { is_admin, tenant_id, fetchUserData, is_having_permission } = useUser();

  useEffect(() => {
    if (is_admin == null || tenant_id == null) {
      fetchUserData();
    }
  }, [is_admin, tenant_id]);

  useEffect(() => {
    const discussion = searchParams.get("discussion");
    const queryDiscussionId = searchParams.get("query_discussion_id");
    const sessionId = searchParams.get("sessionId");
    if (discussion === "New" || sessionId) {
      if (queryDiscussionId) {
        const historyQuery = queries.find(
          (q) => q.id.toString() === queryDiscussionId
        );
        if (historyQuery) {
          setSelectedBuildIds(historyQuery.buildIds);
        }
      }
      if (discussion) {
        setIsModalOpen(true);
      }
    }
    //fetchPastAnalysis();
  }, [searchParams, queries]);

  useEffect(() => {
    const taskId = searchParams.get("task_id");
    if (taskId) setIsVisible(true);
  }, [searchParams, setIsVisible]);

  useEffect(() => {
    fetchData();
  }, [projectId, tabName]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsQueryDropdownOpen(false);
      }
    };

    if (isQueryDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isQueryDropdownOpen]);

  useEffect(() => {
      Promise.all([fetchHistory(), fetchPastAnalysis()]);
  }, [isPastModalOpen,]);

  const fetchData = async () => {
    setIsLoading(true);
    setTableLoading(true);
    try {
      await Promise.all([fetchRepositoryData(), fetchHistory(), fetchPastAnalysis()]);
    } catch (error) {

      showAlert("Failed to fetch data", "error");
    } finally {
      setIsLoading(false);
      setTableLoading(false);
    }
  };

  const fetchRepositoryData = async () => {
    try {
      const response = await getkginfo(projectId, undefined, true);
      if (response.details?.length > 0) {
        const repoData = response.details.map((repo) => {
          if(repo.service == "github"){
            const firstBranch = repo.selected_branch ? repo.branches.find((branch) => branch.name == repo.selected_branch) : repo.branches[0];
            const firstBranchStatus = repo.selected_branch?.builds?.kg_creation_status || firstBranch?.builds?.kg_creation_status;

            repo.branches.forEach((branch) => {
              let buildId = branch.builds.build_id;
              let kg_creation_status = branch.builds.kg_creation_status;

              if (!buildStatus[buildId] && kg_creation_status == 1){ //initiate build for buildIds in progress
                initiateBuildStatus([buildId]);
              }
            })

            return {
              //id: repo.git_url.split("/").pop().split(".")[0],
              id: repo.repo_id,
              name: repo.git_url.split("/").pop().split(".")[0],
              service: repo.service,
              gitUrl: repo.git_url.replace(".git", ""),
              repoType: repo.repo_type,
              branches: repo.branches.map((branch) => ({
                name: branch.name,
                buildId: branch.builds.build_id,
                upstream: branch.upstream,
                kg_creation_status: branch.builds.kg_creation_status,
                pr_creation_status: branch.builds.pr_created,
                pr_details: branch.builds.pr_details,
                status:
                  branch.builds.kg_creation_status === 0
                    ? "Not Started"
                    : branch.builds.kg_creation_status === 1
                      ? "In Progress"
                      : branch.builds.kg_creation_status === 2
                        ? "Completed"
                        : branch.builds.kg_creation_status === 3
                          ? "Needs Rebuild"
                          : branch.builds.kg_creation_status === 4
                          ? "Rebuilding"
                          : "Failed",
                lastUpdated: branch.builds.build_info.last_updated,
                type:
                  branch.name === "main" || branch.name === "master"
                    ? "main"
                    : branch.name.startsWith("feature/")
                      ? "feature"
                      : branch.name.startsWith("fix/")
                        ? "fix"
                        : branch.name.startsWith("dev")
                          ? "development"
                          : "other",
                upstream: branch.builds.kg_creation_status <= 1 ? false : branch.upstream
              })),
              selectedBranch: repo.selected_branch || firstBranch?.name || "main",
              status:
                firstBranchStatus === 0
                  ? "Not Started"
                  : firstBranchStatus === 1
                    ? "In Progress"
                    : firstBranchStatus === 2
                      ? "Completed"
                      : firstBranchStatus === 3
                        ? "Needs Rebuild"
                        : firstBranchStatus === 4
                          ? "Rebuilding"
                          : "Failed",
              created_at: response.created_at,
            };
          }
          else {
            let buildId = repo.builds.build_id;
            let kg_creation_status = repo.builds.kg_creation_status;

            if (!buildStatus[buildId] && kg_creation_status == 1){
              initiateBuildStatus([buildId])
            }

            return {
              id: repo.repo_id,
              name: repo.repository_name,
              service: repo.service,
              repoType: "files",
              builds: {
                buildId: repo.builds.build_id,
                kg_creation_status: repo.builds.kg_creation_status,
                status:
                  repo.builds.kg_creation_status === 0
                    ? "Not Started"
                    : repo.builds.kg_creation_status === 1
                      ? "In Progress"
                      : repo.builds.kg_creation_status === 2
                        ? "Completed"
                        : repo.builds.kg_creation_status === 3
                          ? "Needs Rebuild"
                          : repo.builds.kg_creation_status === 4
                          ? "Rebuilding"
                          : "Failed",
                lastUpdated: repo.builds.build_info.last_updated,
              },
              status:
                repo.builds.kg_creation_status === 0
                ? "Not Started"
                : repo.builds.kg_creation_status === 1
                  ? "In Progress"
                  : repo.builds.kg_creation_status === 2
                    ? "Completed"
                    : repo.builds.kg_creation_status === 3
                      ? "Needs Rebuild"
                      : repo.builds.kg_creation_status === 4
                      ? "Rebuilding"
                      : "Failed",
              created_at: response.created_at,
            }
          }
        });
        setRepositories(repoData);
        setDetails(response.details);
        setRepositoryTotalItems(response.details.length);
      } else {
        setRepositories([]);
        setRepositoryTotalItems(0);
      }
    } catch (error) {
      setRepositories([]);
      setRepositoryTotalItems(0);

    }
  };

  const updateBuildStatuses = (buildIds, type="clone") => {
    initiateBuildStatus(buildIds, type)
  }

  const fetchHistory = async (skip = 0, currentLimit = limit) => {
    setIsQueryLoading(true);
    try {
      const history = await fetchQueryHistory(projectId, currentLimit, skip);

      if (!history?.data || !Array.isArray(history.data)) {
        setQueries([]);
        setTotalQueryCount(0);
        return;
      }

      const formattedQueries = history.data.map((item) => {
        const storageKeys = Object.keys(sessionStorage);
        const matchingKey = storageKeys.find((key) =>
          key.startsWith(`${projectId}_${item.discussion_id}_`)
        );
        const session_id = matchingKey ? matchingKey.split("_")[2] : null;
        const isArchived =
          matchingKey ? sessionStorage.getItem(matchingKey) === "archive" : false;
        return {
          id: item.discussion_id,
          title: item.project_name,
          buildIds: item.build_ids,
          discussionId: item.discussion_id,
          username: item.username,
          session_name: item.session_name,
          duration: item.duration === "unknown" ? "-" : item.duration,
          description: item.description || "-",
          timestamp: item.created_at,
          session_status: !isArchived,
          session_id,
        };
      });

      setTotalQueryCount(history.total || 0);
      setQueries(formattedQueries);
    } catch (error) {

    } finally {
      setIsQueryLoading(false);
    }
  };


  const handleSetPastAnalysis = (analyses) => {
    let newData = []
    newData.push(...analyses);
    setPastAnalysis(newData);
  }

  const fetchPastAnalysis = async (currentSkip = 0, currentLimit = limit) => {
    setIsDeepAnalysisLoading(true);
    try {
      const data = await getPastMaintenanceTasks(projectId, currentLimit, currentSkip, "DocumentCreation");
      handleSetPastAnalysis(data.tasks);
      setTotalAnalysisCount(data.total_count);
      setSkip(currentSkip);
      setLimit(currentLimit);
    } catch (error) {
      // showAlert("Failed to fetch past deep analysis query", "error");
    } finally {
      setIsDeepAnalysisLoading(false);
    }
  };

  const handleQueryPageChange = async (newPage) => {
    setQueryPage(newPage);
    setSkip((newPage - 1) * limit);
    const newSkip = (newPage - 1) * limit;
    await fetchHistory(newSkip, limit);
  };

  const handleAnalysisPageChange = async (newPage) => {
    setAnalysisPage(newPage);
    setSkip((newPage - 1) * limit);
    const newSkip = (newPage - 1) * limit;
    await fetchPastAnalysis(newSkip, limit);
  };

  const handleLimitChange = async (newLimit) => {
    setLimit(newLimit);
    setQueryPage(1);
    setAnalysisPage(1);
    await fetchHistory(0, newLimit);
    await fetchPastAnalysis(0, newLimit);
  };

  const handleRemoveRepository = async (buildId, repoService) => {
    try {
      const response = await deleteRepoBranch(String(buildId), projectId, repoService);
      if (response) {
        showAlert("Successfully deleted branch.", "success");
        fetchRepositoryData();
      } else {
        throw new Error("Failed to delete branch");
      }
    } catch (error) {
      showAlert("Failed to delete branch", "error");

    }
  };

  const handleQueryClick = (query) => {
    setSelectedBuildIds(query.buildIds);
    const storageKeys = Object.keys(sessionStorage);
    const params = new URLSearchParams();

    params.set("discussion", "New");
    params.set("query_discussion_id", query.id.toString());
    params.set("buildIds", query.buildIds.join(","));
    params.set("session_name", query.session_name);

    router.push(`${pathname}?${params.toString()}`);
    setIsModalOpen(true);
  };

  const handleCloseModal = async () => {
    setSelectedRepositories([]);
    setIsModalOpen(false);
    setRightContent({
      type: 'empty',
      data: []
    });
    await Promise.all([fetchHistory(), fetchPastAnalysis()]);
    onClose();
  };

  const handleBuildComplete = (repoId, repoService, branchName) => {

    if (!repositories || repositories.length === 0 || !repoId) return;
    if(repoService == "localFiles"){
      setRepositories((prev) => {
        const newRepositories = prev.map((repo) =>
          repo.id === repoId ?
          {
            ...repo,
            builds: {...repo.builds, kg_creation_status: 2, status: "Completed"},
            status: "Completed"
          } : repo
        );

        if (JSON.stringify(newRepositories) === JSON.stringify(prev)) {
          return prev;
        }

        const buildId = repositories.filter((repo) => repo.id === repoId)[0].builds.buildId;

        clearBuildProgress(buildId);

        return newRepositories;
      })
    }
    else {
      setRepositories((prev) => {
        const newRepositories = prev.map((repo) =>
          repo.id === repoId
            ? {
              ...repo,
              status: repo.selectedBranch === branchName ? "Completed" : repo.status,
              branches: repo.branches.map((b) =>
                b.name === branchName
                  ? { ...b, builds: { ...b.builds, kg_creation_status: 2 } }
                  : b
              ),
            }
            : repo
        );

        if (JSON.stringify(newRepositories) === JSON.stringify(prev)) {
          return prev;
        }

        const buildId = repositories.filter((repo) => repo.id === repoId)[0].branches.filter((branch) => branch.name === branchName)[0].buildId;

        clearBuildProgress(buildId);

        return newRepositories;
      });
    }
  };

  const changeRepoStatus = (repoId, branchName, status) => {
    setRepositories((prev) => {
      const newRepositories = prev.map((repo) =>
        repo.id === repoId
          ? {
            ...repo,
            status: repo.selectedBranch === branchName ? status : repo.status,
            branches: repo.branches.map((b) =>
              b.name === branchName && (status === "In Progress" || status == "Rebuilding")
                ? { ...b, upstream: false }
                : b
            ),
          }
          : repo
      );
      return newRepositories;
    })
  }

  const handleBranchChange = async (repoId, branchName) => {
    const current_branch = repositories.find((repo) => repo.id === repoId).selectedBranch;
    const currentRepo = repositories.find((repo) => repo.id === repoId);
    const buildId = currentRepo?.branches.find(
                      (branch) => branch.name === branchName
                    ).buildId;

    setRepositories((prev) =>
      prev.map((repo) =>
        repo.id === repoId
          ? {
            ...repo,
            selectedBranch: branchName,
            status:
              repo.branches.find((b) => b.name === branchName)?.kg_creation_status === 0
                ? "Not Started"
                : repo.branches.find((b) => b.name === branchName)?.kg_creation_status === 1
                  ? "In Progress"
                  : repo.branches.find((b) => b.name === branchName)?.kg_creation_status === 2
                    ? "Completed"
                    : repo.branches.find((b) => b.name === branchName)?.kg_creation_status === 3
                      ? "Needs Rebuild"
                      : repo.branches.find((b) => b.name === branchName)?.kg_creation_status === 4
                        ? "Rebuilding"
                        : "Failed",
          }
          : repo
      )
    );

    const success = await updateSelectedBranch(Number(projectId), buildId, branchName);
    if(!success){
      showAlert("Failed to update the branch.", "error")
      setRepositories((prev) =>
        prev.map((repo) =>
          repo.id === repoId
            ? {
              ...repo,
              selectedBranch: current_branch,
              status:
                repo.branches.find((b) => b.name === branchName)?.kg_creation_status === 0
                  ? "Not Started"
                  : repo.branches.find((b) => b.name === branchName)?.kg_creation_status === 1
                    ? "In Progress"
                    : repo.branches.find((b) => b.name === branchName)?.kg_creation_status === 2
                      ? "Completed"
                      : repo.branches.find((b) => b.name === branchName)?.kg_creation_status === 3
                        ? "Needs Rebuild"
                        : repo.branches.find((b) => b.name === branchName)?.kg_creation_status === 4
                          ? "Rebuilding"
                          : "Failed",
            }
            : repo
        )
      );
    }
  };

  const handleCheckboxChange = (repoId) => {
    setSelectedRepositories((prev) =>
      prev.includes(repoId) ? prev.filter((id) => id !== repoId) : [...prev, repoId]
    );
  };

  const handleSelectAll = (repoIds, allChecked) => {
    setSelectedRepositories((prev) =>
      !allChecked ? [...prev, ...repoIds] : prev.filter((repoId) => !repoIds.includes(repoId))
    );
  };

  const handleStartAdvancedQuery = () => {
    setShowAdvancedQueryHandler(true);
    setIsQueryDropdownOpen(false);
  };

  const toggleViewPastQuery = () => {
    setIsPastModalOpen((prev) => {
      if (prev) {
        fetchHistory();
        fetchPastAnalysis();
      }
      return !prev;
    });
  };

  const handleAddRepoClick = () => setIsRepoModalOpen(true);

  const handleRefresh = async (showLoading = true) => {
    setSelectedRepositories([]);
    if (showLoading) {
      setTableLoading(true);
    }
    try {
      await fetchRepositoryData();
    } catch (error) {
      showAlert("Failed to refresh repository table data", "error");

    } finally {
      setTableLoading(false);
    }
  };

  useEffect(() => {
    if(refreshRepos) {
      handleRefresh(false);
      setRefreshRepos(false);
    }
  }, [refreshRepos])

  const validateStartQuery = () => {
    if (selectedRepositories.length === 0) {
      showAlert("Please select at least one repository", "warning");
      return false;
    }
    const reposWithPendingPRs = selectedRepositories
      .map((repoId) => {
        const repo = repositories.find((r) => r.id === repoId);
        if(repo.service == "localFiles") return null;
        const selectedBranch = repo?.branches.find((b) => b.name === repo.selectedBranch);
        return selectedBranch?.pr_creation_status ? { repoName: repo.name, branchName: selectedBranch.name } : null;
      })
      .filter(Boolean);
    if (reposWithPendingPRs.length > 0) {
      const prMessage = reposWithPendingPRs.map((r) => `${r.repoName} (branch: ${r.branchName})`).join(" and ");
      showAlert(`Please accept the PRs for repositories: ${prMessage} before starting the query`, "warning");
      return false;
    }
    const invalidRepos = selectedRepositories.filter((repoId) => {
      const repo = repositories.find((r) => r.id === repoId);
      return (repo?.status !== "Completed" && repo?.status !== "Rebuilding");
    });
    if (invalidRepos.length > 0) {
      showAlert("Selected repositories must have Completed status", "warning");
      return false;
    }
    return true;
  };

  const handleStartQuery = () => {
    setIsAdvancedQuery(false);
    if (!validateStartQuery()) return;
    const selectedBuildIds = selectedRepositories
      .map((repoId) => {
        const repo = repositories.find((r) => r.id === repoId);

        if (!repo) return null; // or handle if repo not found

        if (repo.service === "github") {
          return repo.branches.find((b) => b.name === repo.selectedBranch)?.buildId;
        } else {
          return repo.builds.buildId;
        }
      })
      .filter(Boolean);
    if (selectedBuildIds.length === 0) {
      showAlert("No valid build IDs found", "error");
      return;
    }
    const params = new URLSearchParams();
    params.set("discussion", "New");
    params.set("buildIds", selectedBuildIds.join(","));
    router.push(`${pathname}?${params.toString()}`);
    setIsModalOpen(true);
    setIsQueryDropdownOpen(false);
  };

  const filteredQueries = queries.filter(
    (query) =>
      query.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      query.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredRepositories = repositories.filter((repo) =>
    repo.name.toLowerCase().includes(tableSearch.toLowerCase())
  );

  useEffect(() => {
    if (isSearchFocused && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isSearchFocused, filteredRepositories]);

  const paginatedQueries = filteredQueries.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  const totalPages = Math.ceil(filteredQueries.length / ITEMS_PER_PAGE);

  const HeaderSection = React.memo(({ toggleViewPastQuery }) => {
    return (
      <div className="flex flex-col space-y-2 mb-2 mt-1">
        <div className="py-1">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2 pl-2">
                <Badge type="Query" />
              </div>
            </div>
          </div>

          {/* Search and Button Section */}
          <div className="flex flex-col sm:flex-row items-center justify-between pt-3 gap-4">
            {/* Search Input */}
            <div className="relative w-full sm:w-auto sm:max-w-xs">
              <input
                type="text"
                placeholder="Search Repositories..."
                value={tableSearch}
                onChange={(e) => {
                  setRepositoryCurrentPage(1);
                  setTableSearch(e.target.value);
                }}
                onFocus={() => setIsSearchFocused(true)}
                onBlur={() => setIsSearchFocused(false)}
                ref={searchInputRef}
                className="w-full pl-11 pr-4 py-2 border border-gray-300 rounded-md
                           focus:outline-none focus:ring-1 focus:ring-blue-300 focus:border-blue-300
                           typography-body-sm shadow-md"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            </div>

            {/* Button Section */}
            <div className="flex flex-row gap-2">
              {/* Refresh Button */}
              <button
                onClick={handleRefresh}
                disabled={tableLoading}
                className="min-w-[100px] flex items-center gap-1 px-3 py-2 bg-white border border-gray-300
                           rounded-md text-black hover:bg-gray-100 focus:ring-2 focus:ring-gray-500
                           focus:ring-offset-2 typography-body-sm disabled:bg-gray-400 disabled:text-white
                           disabled:border-gray-400 disabled:cursor-not-allowed"
              >
                <RefreshCw size={14} className={tableLoading ? "animate-spin" : ""} />
                {tableLoading ? "Refreshing..." : "Refresh"}
              </button>

              {/* History Button */}
              <button
                onClick={toggleViewPastQuery}
                className="min-w-[100px] flex items-center gap-1 px-3 py-2 bg-white border border-gray-300
                           rounded-md text-black hover:bg-gray-100 focus:ring-2 focus:ring-gray-500
                           focus:ring-offset-2 typography-body-sm"
              >
                <BookOpen size={14} />
                History
              </button>

              <BootstrapTooltip title={!is_having_permission() ? "You don't have permission" : "Add Repository"}>
                <span>
                  <button
                    onClick={handleAddRepoClick}
                    disabled={!is_having_permission()}
                    className="min-w-[100px] flex items-center gap-1 px-3 py-2 bg-white border border-gray-300
                             rounded-md text-black hover:bg-gray-100 focus:ring-2 focus:ring-gray-500
                             focus:ring-offset-2 typography-body-sm disabled:bg-gray-300 disabled:text-gray-500
                             disabled:cursor-not-allowed"
                  >
                    <Plus size={14} />
                    Add Repository
                  </button>
                </span>
              </BootstrapTooltip>

              {/* Query Dropdown */}
              <div className="relative" ref={dropdownRef}>
                <DynamicButton
                  variant="orange"
                  size="default"
                  icon={FileCode2}
                  text="Query"
                  tooltip={!is_having_permission() ? "You don't have permission" :
                    selectedRepositories.length === 0 ? "Select repositories to query" : "Query repositories"}
                  onClick={() => setIsQueryDropdownOpen(!isQueryDropdownOpen)}
                  disabled={
                    !is_having_permission() ||
                    selectedRepositories.length === 0 ||
                    !selectedRepositories.every(
                      (repoId) => repositories.find((r) => r.id === repoId)?.status === "Completed" ||
                      repositories.find((r) => r.id === repoId)?.status === "Rebuilding"
                    )
                  }
                  className="min-w-[100px]"
                >
                  <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </DynamicButton>

                {isQueryDropdownOpen && (
                  <div
                    className="absolute right-0 mt-2 w-48 bg-white border border-gray-100 rounded-md
                               shadow-lg z-50 overflow-hidden"
                  >
                    <button
                      onClick={handleStartQuery}
                      className="w-full text-left px-4 py-3 typography-body-sm text-gray-700
                                 hover:bg-gray-100 transition duration-150"
                    >
                      Basic Query
                    </button>
                    <button
                      onClick={handleStartAdvancedQuery}
                      className="w-full text-left px-4 py-3 typography-body-sm text-gray-700
                                 hover:bg-gray-100 transition duration-150"
                    >
                      Deep Analysis
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }, (prevProps, nextProps) => prevProps.toggleViewPastQuery === nextProps.toggleViewPastQuery);

  HeaderSection.displayName = "HeaderSection";

  const PaginationControls = () => (
    <div className="flex justify-center gap-4 mt-4">
      <button
        onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
        disabled={currentPage === 1}
        className="px-4 py-2 bg-white border border-gray-300 rounded-md text-black hover:bg-gray-100 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 typography-body-sm font-weight-medium h-8 disabled:bg-gray-400 disabled:text-white disabled:border-gray-400 disabled:cursor-not-allowed disabled:opacity-40"
      >
        Previous
      </button>
      <span>Page {currentPage} of {totalPages}</span>
      <button
        onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
        disabled={currentPage === totalPages}
        className="px-4 py-2 bg-white border border-gray-300 rounded-md text-black hover:bg-gray-100 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 typography-body-sm font-weight-medium h-8 disabled:bg-gray-400 disabled:text-white disabled:border-gray-400 disabled:cursor-not-allowed disabled:opacity-40"
      >
        Next
      </button>
    </div>
  );

  return (
    <div className="relative flex flex-col min-h-[80vh] bg-white">
      <div
        className="flex-1 w-full overflow-hidden px-4 sm:px-6 lg:px-8"
        style={{ zIndex: 10 }}
      >
        {tableLoading ? (
          <div className="p-4 bg-white rounded-lg">
            <TableLoadingSkeleton />
          </div>
        ) : (
          <>
            {/* Show HeaderSection only when loading is complete */}
            <HeaderSection toggleViewPastQuery={toggleViewPastQuery} />
            {filteredRepositories.length > 0 ? (
              <div className="w-full overflow-x-auto">
                <WebSocketSessionsComponent repositories={details} />
                <RepositoryTable
                  repositories={filteredRepositories}
                  selectedRepositories={selectedRepositories}
                  onCheckboxChange={handleCheckboxChange}
                  onSelectAll={handleSelectAll}
                  onBranchChange={handleBranchChange}
                  onRemoveRepository={handleRemoveRepository}
                  onBuildComplete={handleBuildComplete}
                  currentPage={repositoryCurrentPage}
                  totalItems={filteredRepositories.length}
                  onPageChange={setRepositoryCurrentPage}
                  onPageSizeChange={setRepositoryPageSize}
                  pageSize={repositoryPageSize}
                  handleRefresh={handleRefresh}
                  changeRepoStatus={changeRepoStatus}
                  updateBuildStatuses={updateBuildStatuses}
                />
              </div>
            ) : (
              <EmptyStateView type="repositories" />
            )}
          </>
        )}
      </div>

      {isPastModalOpen && (
        <PastQueryModal
          queries={queries}
          onClose={toggleViewPastQuery}
          handleRowClick={handleQueryClick}
          onPageChange={handleQueryPageChange}
          onLimitChange={handleLimitChange}
          totalCount={totalQueryCount}
          currentPage={queryPage}
          isLoading={isQueryLoading}
          pastDeepAnalysis={{
            pastAnalysis,
            isLoading: isDeepAnalysisLoading,
            setPastAnalysis: setPastAnalysis,
            pageSize: limit,
            totalCount: totalAnalysisCount,
            onPageChange: handleAnalysisPageChange,
            currentPage: analysisPage
          }}
        />
      )}

      {showAdvancedQueryHandler && (
        <AdvancedQueryHandler
          projectId={projectId}
          onComplete={() => setShowAdvancedQueryHandler(false)}
          selectedRepos={selectedRepositories}
        />
      )}

      {isVisible && <CodeGenerationModal />}
      <QueryDiscussionModal isOpen={isModalOpen} setIsOpen={setIsModalOpen} onClose={handleCloseModal} setInstantCloseStatus={setInstantCloseStatus} wsConnection={wsConnection}/>

      <GitRepoModal
        repositoriesData={repositories}
        isOpen={isRepoModalOpen}
        onClose={() => setIsRepoModalOpen(false)}
        onImport={fetchRepositoryData}
        handleRefresh={() => handleRefresh()}
        updateBuildStatuses={updateBuildStatuses}
      />

      {isPastModalOpen && <PaginationControls />}
      <div className="pb-8" />
    </div>

  );
};

export default QueryPage;