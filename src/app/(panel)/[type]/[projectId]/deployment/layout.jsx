'use client';

import React, { useEffect } from "react";
import { useUser } from "@/components/Context/UserContext";

export default function DeploymentLayout({ children }) {
  const { is_admin, tenant_id, fetchUserData } = useUser();

  useEffect(() => {
    if (is_admin == null || tenant_id == null) {
      fetchUserData();
    }
  }, [is_admin, tenant_id]);

  return (
    <div className="relative min-h-screen bg-white">
      {children}
    </div>
  );
}