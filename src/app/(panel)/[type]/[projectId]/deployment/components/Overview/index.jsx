'use client'
import React, { useState, useEffect } from "react";
import Editor from "@monaco-editor/react";
import Badge from "@/components/UIComponents/Badge/Badge";
import { FolderGit2 } from "lucide-react";
import CustomDropdown from "@/components/UIComponents/Dropdowns/CustomDropdown";
import RepositoryDetailsModal from "@/components/Modal/RepositoryModal";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { getComponentDeployments } from '@/utils/api';
import { Loading2 } from "@/components/Loaders/Loading";
const CodeSection = ({ title, content, language }) => {
  if (!content) return null;

  // Format the HCL/Terraform code
  const formatCode = (code) => {
    try {
      // Remove escaped quotes and format JSON to HCL-like structure
      return code
        .replace(/\\"/g, '"')
        .replace(/^\{|\}$/g, '')
        .split('\\n')
        .join('\n');
    } catch (e) {
      return code;
    }
  };

  return (
    <div className="mb-6">
      <h3 className="project-panel-heading mb-2">{title}</h3>
      <div className="border rounded">
        <Editor
          height="300px"
          theme="vs-dark"
          defaultLanguage={language}
          value={formatCode(content)}
          options={{
            readOnly: true,
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            automaticLayout: true,
            scrollbar: {
              vertical: 'visible',
              horizontal: 'visible'
            },
            padding: {
              top: 12,
              bottom: 12
            },
            lineNumbers: 'on',
            renderLineHighlight: 'all',
            lineHeight: 21,
            fontSize: 'var(--font-size-code)',
            tabSize: 2
          }}
        />
      </div>
    </div>
  );
};

const Overview = () => {
  const [projectId, setProjectId] = useState('');
  const [containerId, setContainerId] = useState('');
  const [showRepoDetails, setShowRepoDetails] = useState(false);
  const [showData, setShowData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const path = window.location.pathname.split("/");
    const newProjectId = path[2];
    const newContainerId = path[4];

    setProjectId(newProjectId);
    setContainerId(newContainerId);

    if (newProjectId && newContainerId) {
      fetchDeploymentData(newProjectId, newContainerId);
    }
  }, []);

  const fetchDeploymentData = async (projectId, containerId) => {
    try {
      setIsLoading(true);
      const data = await getComponentDeployments(projectId, containerId);
      setShowData(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return <Loading2 />;
  }

  if (error) {
    return <div className="text-red-500 text-center p-4">{error}</div>;
  }

  if (!showData?.deployments?.[0]) {
    return <EmptyStateView type="noDeploymentFound" onClick={() => {setBranches}} />;
  }

  const handleRepoDetailsOpen = () => setShowRepoDetails(true);

  const menuOptions = [
    {
      label: "View Repository Details",
      icon: FolderGit2,
      onClick: handleRepoDetailsOpen,
    },
  ];

  const HeaderSection = () => (
    <div className="flex flex-col space-y-4 top-1" style={{ zIndex: 5 }}>
      <div className="flex flex-col border border-gray-200">
        <div className="relative px-2 py-1 space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <h2 className="typography-body-lg font-weight-semibold text-gray-800">
                  {showData?.deployments?.[0]?.properties?.Title ||
                    "Untitled Deployment"}
                </h2>
                <div className="flex items-center gap-1">
                  <Badge
                    type={
                      showData?.deployments?.[0]?.properties?.Type || "default"
                    }
                  />
                </div>
              </div>
            </div>
            <CustomDropdown options={menuOptions} align="right" />
          </div>
          <div className="flex items-center justify-between pl-3 py-1">
            <div className="flex items-center gap-2 ml-auto"></div>
          </div>
          <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-100/50 via-blue-300/20 to-transparent"></div>
        </div>
      </div>
    </div>
  );

  const properties = showData.deployments[0].properties;

  return (
    <div className="px-4 py-8 overflow-y-auto max-h-[70vh] custom-scrollbar">
      <div className="mx-auto">
        <div className="text-left mb-12">
          <HeaderSection />
        </div>

        {properties.Description && (
          <div className="mb-3 -mt-5">
            <h3 className="project-panel-heading">Description</h3>
            <p className="text-gray-700">{properties.Description}</p>
          </div>
        )}

        <div className="space-y-8">
          <CodeSection
            title="Main Terraform Configuration"
            content={properties.main_tf}
            language="hcl"
          />
          <CodeSection
            title="Variables Configuration"
            content={properties.variables_tf}
            language="hcl"
          />
          <CodeSection
            title="Providers Configuration"
            content={properties.providers_tf}
            language="hcl"
          />
          <CodeSection
            title="Outputs Configuration"
            content={properties.outputs_tf}
            language="hcl"
          />
          <CodeSection
            title="Dockerfile"
            content={properties.docker_file}
            language="dockerfile"
          />
          <CodeSection
            title="Docker Compose"
            content={properties.docker_compose}
            language="yaml"
          />
          <CodeSection
            title="Workflow Configuration"
            content={properties.workflow_file}
            language="yaml"
          />
        </div>
      </div>

      {showRepoDetails && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-4 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <RepositoryDetailsModal
              open={true}
              projectId={projectId}
              containerId={containerId}
              onClose={() => setShowRepoDetails(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default Overview;