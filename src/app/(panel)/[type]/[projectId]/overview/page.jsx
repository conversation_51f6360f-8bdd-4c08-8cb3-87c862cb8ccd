"use client";

import React, { useEffect } from "react";
import OverviewTabContent from "@/components/BrowsePanel/OverviewTabContent";
import "@/styles/tabs/overview.css"
import { useUser } from "@/components/Context/UserContext";

const Page = () => {
  const { is_admin, tenant_id, fetchUserData } = useUser();

  useEffect(() => {
    if (is_admin == null || tenant_id == null) {
      fetchUserData();
    }
  }, [is_admin, tenant_id]);

  return (
    <>
      <div className="relative overviewTabWrapper custom-scrollbar w-full h-full">
        <OverviewTabContent />
      </div>
    </>
  );
};

export default Page;