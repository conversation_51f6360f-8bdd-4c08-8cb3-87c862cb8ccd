"use client";

import React, { useEffect, useState, useContext, useRef } from "react";
import { usePathname, useRouter } from "next/navigation";
import { MoreVertical } from "lucide-react";
import { FaCogs } from 'react-icons/fa';
import { X } from "lucide-react";
import Search from "@/components/BrowsePanel/TableComponents/Search";
import DeleteProjectModal from "@/components/Modal/DeleteProjectModal";
import EnParser from "@/utils/enParser";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { getPastDiscussionById, deleteNodeById } from "@/utils/api";
import en from "@/en.json";
import { formatUTCToLocal } from "@/utils/datetime";

const Page = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [pastDiscussions, setPastDiscussions] = useState([]);
  const [allPastDiscussions, setAllPastDiscussions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [discussionId, setDiscussionId] = useState("");
  const [openIndex, setOpenIndex] = useState(null);
  const pathname = usePathname();
  const router = useRouter();
  const { showAlert } = useContext(AlertContext);
  const dropdownRef = useRef(null);
  const nodeId = pathname.split("/")[2];

  useEffect(() => {
    const fetchData = async () => {
      try {
        const discussions = await getPastDiscussionById(nodeId);
        setPastDiscussions(discussions);
        setAllPastDiscussions(discussions);
      } catch (error) {
        
        showAlert("Failed to load discussions", "danger");
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, [nodeId, showAlert]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setOpenIndex(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Helper functions
  const extractTitleAndDate = (input) => {
    const parts = input.split(" on ");
    if (parts.length < 2) return [input, "N/A"];
    return [parts[0], parts[1].split(" ")[0]];
  };

  // Search functionality
  const handleSearchChange = (term) => {
    setSearchTerm(term);
    const filtered = allPastDiscussions.filter((discussion) =>
      discussion.Title.toLowerCase().includes(term.toLowerCase())
    );
    setPastDiscussions(filtered);
  };

  // Navigation functions
  const navigateToDiscussion = (discussionId, type = "existing") => {
    const params = new URLSearchParams();
    params.set("discussion_id", discussionId);
    params.set("discussion", type);
    router.push(`/project/${nodeId}/overview/past_discussion?${params.toString()}`);
  };

  const createNewDiscussion = () => {
    const params = new URLSearchParams();
    params.set("discussion", "new");
    params.set("node_id", nodeId);
    params.set("node_type", "RequirementRoot");
    router.push(`/project/${nodeId}/overview/past_discussion?${params.toString()}`);
  };

  const handleBack = () => router.replace(`/project/${nodeId}/overview`);

  // Action handlers
  const confirmDelete = (id) => {
    setDiscussionId(id);
    setIsDeleteModalOpen(true);
  };

  const handleEdit = () => showAlert("Functionality was not implemented", "info");

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      const response = await deleteNodeById(discussionId, "Project");

      if (response.status >= 200 && response.status < 300) {
        const updatedDiscussions = pastDiscussions.filter(
          (discussion) => discussion.id !== discussionId
        );
        setPastDiscussions(updatedDiscussions);
        setAllPastDiscussions(updatedDiscussions);
        showAlert("Discussion deleted successfully", "success");
      } else {
        throw new Error(response.statusText || "Unknown error");
      }
    } catch (error) {
      
      showAlert("Failed to delete the Discussion!", "danger");
    } finally {
      setIsDeleting(false);
      setIsDeleteModalOpen(false);
    }
  };

  // Sample empty rows for consistency in loading state
  const emptyRows = [1, 2, 3, 4, 5];

  return (
    <div className="flex flex-col h-full w-full bg-white">
      {/* Header */}
      <div className="flex items-center border-b border-gray-200 p-4">
        <button
          className="hover:bg-gray-100 p-1 rounded-full"
          onClick={handleBack}
          aria-label="Close"
        >
          <X className="w-5 h-5 text-gray-500" />
        </button>
        <h1 className="ml-3 text-md font-weight-medium text-gray-700">Past Discussions</h1>
      </div>

      {/* Search and Actions */}
      <div className="flex items-center justify-between p-4">
        <div className="w-1/2">
          <Search searchTerm={searchTerm} setSearchTerm={handleSearchChange} />
        </div>
        <button
          className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md typography-body-sm font-weight-medium transition-colors"
          onClick={createNewDiscussion}
        >
          <FaCogs size={16} className="mr-2" />
          New Discussion
        </button>
      </div>

      {/* Table */}
      <div className="flex-grow px-4 pb-4">
        <div className="overflow-hidden border border-gray-200 rounded-lg shadow-sm">
          <table className="w-full typography-body-sm text-left text-gray-500">
            <thead className="typography-caption text-gray-600 uppercase bg-gray-50 border-b border-gray-200">
              <tr>
                <th scope="col" className="px-6 py-3 font-weight-medium">Title</th>
                <th scope="col" className="px-6 py-3 font-weight-medium">Date</th>
                <th scope="col" className="px-6 py-3 font-weight-medium">Status</th>
                <th scope="col" className="px-6 py-3 w-16"></th>
              </tr>
            </thead>
            <tbody>
              {isLoading ? (
                // Consistent height loading state with skeleton rows
                <>
                  {emptyRows.map((_, index) => (
                    <tr key={`loading-${index}`} className="animate-pulse bg-white border-b last:border-0">
                      <td className="px-6 py-4">
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="h-4 bg-gray-200 rounded w-24"></div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="h-5 bg-gray-200 rounded-full w-16"></div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="h-5 w-5 bg-gray-200 rounded-full mx-auto"></div>
                      </td>
                    </tr>
                  ))}
                </>
              ) : pastDiscussions.length > 0 ? (
                pastDiscussions.map((discussion, index) => (
                  <tr
                    key={index}
                    className="bg-white border-b last:border-0 hover:bg-gray-50 transition-colors"
                  >
                    <td
                      className="px-6 py-4 font-weight-medium text-gray-900 cursor-pointer"
                      onClick={() => navigateToDiscussion(discussion.id)}
                    >
                      {extractTitleAndDate(discussion.Title)[0]}
                    </td>
                    <td className="px-6 py-4 text-gray-600">
                      {formatUTCToLocal(discussion.CreatedAt)}
                    </td>
                    <td className="px-6 py-4">
                      <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full typography-caption font-weight-medium">
                        Merged
                      </span>
                    </td>
                    <td className="px-6 py-4 relative">
                      <button
                        className="p-1 rounded-full hover:bg-gray-100"
                        onClick={() => setOpenIndex(openIndex === index ? null : index)}
                        aria-label="Options"
                      >
                        <MoreVertical className="h-5 w-5 text-gray-500" />
                      </button>

                      {openIndex === index && (
                        <div
                          ref={dropdownRef}
                          className="absolute right-6 top-10 bg-white shadow-lg rounded-md border border-gray-200 z-50 w-32 overflow-hidden"
                        >
                          <button
                            className="w-full text-left px-4 py-2 hover:bg-gray-100 text-gray-700 transition-colors"
                            onClick={handleEdit}
                          >
                            Edit
                          </button>
                          <button
                            className="w-full text-left px-4 py-2 hover:bg-red-50 text-red-600 transition-colors"
                            onClick={() => confirmDelete(discussion.id)}
                          >
                            Delete
                          </button>
                        </div>
                      )}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="4" className="px-6 py-12 text-center text-gray-500">
                    <EnParser content={en.DiscussionList_NoDiscussionsFound} />
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && (
        <DeleteProjectModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          onDelete={handleDelete}
          isDeleting={isDeleting}
          type="discussion"
        />
      )}
    </div>
  );
};

export default Page;