import React from "react";

const CardView = ({ data, onCardClick }) => {
  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-4 gap-4 cursor-pointer">
      {data.map((item, index) => (
        <div
          key={index}
          className="w-55 h-[122px] bg-white rounded-lg border border-gray-200 flex-col justify-start items-start inline-flex overflow-hidden"
          onClick={() => onCardClick(item)}
        >
          <div className="relative self-stretch h-[72px] px-6 pt-[19px] pb-6 bg-[#f9f9f9] flex-col justify-center items-center flex">
            <img src="/images/pdf-icon.png" alt="PDF" className="w-16 h-16" />
            <div className="absolute top-2 right-2">
              <button className="w-[30px] h-[30px] p-1.5 rounded-md justify-center items-center flex text-gray-400 hover:text-gray-500 hover:bg-gray-100">
                <svg
                  className="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                </svg>
              </button>
            </div>
          </div>
          <div className="self-stretch h-[50px] p-2 flex-col justify-start items-start gap-px flex">
            <div className="self-stretch h-[15px] flex-col justify-start items-end gap-2 flex">
              <div className="self-stretch text-[#1f2a37] typography-caption font-weight-medium leading-[15px] truncate">
                {item.name}
              </div>
            </div>
            {item.lastModified && item.fileSize && (
              <div className="self-stretch justify-between items-start inline-flex">
                <div className="grow shrink basis-0 text-[#616161] typography-caption font-weight-normal leading-[18px]">
                  {item.lastModified} / {item.fileSize}
                </div>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default CardView;
