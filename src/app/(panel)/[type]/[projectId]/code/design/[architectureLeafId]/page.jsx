"use client"
import { ItemDetails } from '@/components/BrowsePanel/Architecture/ItemDetails';
import { useParams } from "next/navigation"

const Page = () => {
    const params = useParams();
    const archId = params.architectureLeafId;

    return (
        <div className='h-full overflow-y-auto custom-scrollbar'>
            <ItemDetails archId={archId} />
        </div>
    );
}

export default Page;