"use client";

import React, { useState, useEffect, Suspense, useContext } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { confirmForgotPassword } from "../../../../utils/api";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { decryptTenantId, encryptedTenantId, getRootTenantId } from "@/utils/hash";
import LoginSignupContainer from "@/components/LoginSignupContainer";

const ConfirmForgotPasswordContent = () => {
  const [email, setEmail] = useState("");
  const queryParams = useSearchParams();
  const [tenant_id, setTenant_id] = useState(null);
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [confirmationCode, setConfirmationCode] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { showAlert } = useContext(AlertContext)
  const router = useRouter();
  const [buttonStatus, setButtonStatus] = useState(false);
  const searchParams = useSearchParams();
  const initialEmail = searchParams.get("email");
  const [passwordError, setPasswordError] = useState("");

  useEffect(() => {
    if (queryParams.get("tenant_id")) {
      if (decryptTenantId(queryParams.get("tenant_id"))) {
        setTenant_id(queryParams.get("tenant_id"));
      } else {
        showAlert("Invalid tenant ID.", "danger");
      }
    } else {
      router.push("/users/login?tenant_id=" + encodeURIComponent(getRootTenantId()));
      setTenant_id(encryptedTenantId())
    }
  }, [queryParams]);

  useEffect(() => {
    if (initialEmail) {
      setEmail(initialEmail);
    }
  }, [initialEmail]);

  const validatePassword = (password) => {
    const hasSymbol = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    const hasUppercase = /[A-Z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasMinLength = password.length >= 8;

    return {
      hasSymbol,
      hasUppercase,
      hasNumber,
      hasMinLength,
    };
  };

  const handleSubmit = async (event) => {
    setButtonStatus(true);
    event.preventDefault();

    const passwordValidation = validatePassword(password);

    if (!tenant_id) {
      showAlert("Tenant ID is required.", "danger");
      setButtonStatus(false);
      return;
    }

    if (password !== confirmPassword) {
      showAlert("Passwords do not match.", "danger");
      setButtonStatus(false);
      setPasswordError("Passwords do not match.");
      return;
    }

    if (!passwordValidation.hasSymbol || !passwordValidation.hasUppercase || !passwordValidation.hasNumber || !passwordValidation.hasMinLength) {
      showAlert("Password does not meet the requirements.", "danger");
      setButtonStatus(false);
      setPasswordError("Your password must contain at least one symbol, one uppercase letter, one number, and be at least 8 characters long.");
      return;
    }

    if (!confirmationCode || confirmationCode.trim() === '') {
      showAlert('Confirmation code is required.', "danger");
      setButtonStatus(false);
      return;
    }

    try {
      const response = await confirmForgotPassword(tenant_id, email, password, confirmationCode);

      if (response) {
        showAlert("Password reset successfully!", "success");
        setButtonStatus(false);
        setTimeout(() => {
          router.push(`/users/login?tenant_id=${encodeURIComponent(tenant_id)}`);
        }, 2000);
      } else {
        showAlert("Confirmation code has expired.", "danger");
        setButtonStatus(false);
      }
    } catch (error) {
      
      showAlert("Password reset failed. Please check your confirmation code.", "danger");
      setButtonStatus(false);
    }
  };


  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  return (
    <LoginSignupContainer>
      <div className="bg-white rounded-lg shadow-lg p-6 max-w-sm w-full z-10 text-center sm:max-w-md md:max-w-lg lg:max-w-md">
        <h1 className="user-panel-content mb-2">Change your password</h1>
        <p className="text-font mb-4 mt-4">
          Before you dive in, let&apos;s get the rest of your account details in order.
        </p>
        <form onSubmit={handleSubmit} className="flex flex-col items-center">
          <label htmlFor="email" className="self-start mb-1 user-panel-info">
            <strong>Email Address</strong> <span className="text-red-500">*</span>
          </label>
          <input
            id="email"
            type="email"
            value={email}
            readOnly
            placeholder="Email Address"
            required
            className="w-full px-3 py-2 mb-3 border border-gray-300 rounded-md"
          />
          <label
            htmlFor="confirmationCode"
            className="self-start mb-1 user-panel-info"
          >
            <strong> Confirmation Code</strong> <span className="text-red-500">*</span>
          </label>
          <input
            id="confirmationCode"
            type="text"
            value={confirmationCode}
            onChange={(e) => setConfirmationCode(e.target.value)}
            placeholder="Confirmation Code"
            required
            className="w-full px-3 py-2 mb-3 border border-gray-300 rounded-md"
          />
          <label htmlFor="password" className="self-start mb-1 user-panel-info">
            <strong> Password</strong> <span className="text-red-500">*</span>
          </label>
          <div className="relative w-full mb-3">
            <input
              id="password"
              type={showPassword ? "text" : "password"}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Password"
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md pr-10"
            />
            <button
              type="button"
              onClick={togglePasswordVisibility}
              className="absolute right-2 top-2 text-gray-500"
            >
              {showPassword ? (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-5 h-5"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"
                  />
                  <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                </svg>
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-5 h-5"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"
                  />
                </svg>
              )}
            </button>
          </div>
          <label htmlFor="confirmPassword" className="self-start mb-1 user-panel-info">
            <strong> Confirm Password</strong> <span className="text-red-500">*</span>
          </label>
          <div className="relative w-full mb-3">
            <input
              id="confirmPassword"
              type={showConfirmPassword ? "text" : "password"}
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              placeholder="Confirm Password"
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md pr-10"
            />
            <button
              type="button"
              onClick={toggleConfirmPasswordVisibility}
              className="absolute right-2 top-2 text-gray-500"
            >
              {showConfirmPassword ? (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-5 h-5"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"
                  />
                  <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                </svg>
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-5 h-5"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"
                  />
                </svg>
              )}
            </button>
          </div>
          {passwordError && (
            <div className="mb-3 self-start w-full">
              <h3 className="text-left mb-1 user-panel-info">
                {passwordError.includes("match") ? "" : "Your password must contain:"}
              </h3>
              {!passwordError.includes("match") && (
                <ul className="list-disc pl-6 password-info">
                  <li>a symbol</li>
                  <li>an uppercase letter</li>
                  <li>a number</li>
                  <li>8 characters minimum</li>
                </ul>
              )}

            </div>
          )}
          <button
            type="submit"
            disabled={buttonStatus}
            className={`w-full bg-[#F26A1B] border border-[#8C3E10] border-opacity-50 text-white rounded-md px-4 py-2 typography-body ${buttonStatus ? 'bg-gray-400' : 'bg-[#F26A1B]'}`}
          >
            {!buttonStatus ? (
              <>
                Change password
              </>
            ) : (
              <div
                className=" inline-block h-6 w-6 animate-spin rounded-full border-4 border-solid border-current border-e-transparent align-[-0.125em] text-white"
                role="status"
              >
                <span className="absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
                  Loading...
                </span>
              </div>
            )}
          </button>
        </form>
        <p className="mt-4 text-link">
          Have an account?{" "}
          <Link href="/users/login" className="text-[#F26A1B]">
            Sign in
          </Link>
        </p>
      </div>
    </LoginSignupContainer>
  );
};

const ConfirmForgotPasswordPage = () => (
  <Suspense fallback={<div>Loading...</div>}>
    <ConfirmForgotPasswordContent />
  </Suspense>
);

export default ConfirmForgotPasswordPage;