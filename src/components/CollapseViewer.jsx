import React, { useState, useContext, useEffect } from 'react';
import ChatPanel from './ChatPanel';
import { StateContext } from './Context/StateContext';
import { ExecutionContext } from './Context/ExecutionContext';
import { getFlowlineStatus } from '@/utils/api';
import ProjectTimeline from './ProjectTimeline';
import { useRouter, usePathname } from "next/navigation";
import { MessageSquare, ListTodo } from 'lucide-react';

const TimelineSkeleton = () => {
    // Updated CSS for the lighter shimmer animations
    const skeletonStyles = `
      @keyframes shimmer {
        0% {
          background-position: -1000px 0;
        }
        100% {
          background-position: 1000px 0;
        }
      }

      .skeleton-pulse {
        animation: shimmer 2.5s infinite linear;
        background: linear-gradient(to right,
          #f8fafc 0%,
          #f1f5f9 20%,
          #f8fafc 40%
        );
        background-size: 1000px 100%;
        border-radius: 4px;
      }
    `;

    // Create an array to simulate the number of activities in the timeline
    const skeletonItems = [
      { hasSubItems: true, subItemCount: 3 },
      { hasSubItems: false },
      { hasSubItems: true, subItemCount: 2 },
      { hasSubItems: true, subItemCount: 6 },
      { hasSubItems: false },
    ];

    // Add style tag to head on component mount
    React.useEffect(() => {
      const styleSheet = document.createElement("style");
      styleSheet.innerText = skeletonStyles;
      document.head.appendChild(styleSheet);
      return () => styleSheet.remove();
    }, []);

    return (
      <div>
        {/* Sticky header skeleton */}
        <div className="sticky top-0 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/75 z-[10] flex justify-between items-center mb-6 py-2 border-b">
          <div className="flex-1">
            <div className="skeleton-pulse h-5 w-32 ml-4"></div>
          </div>
          <div className="flex items-center gap-1 pr-4">
            <div className="skeleton-pulse h-4 w-16"></div>
            <div className="skeleton-pulse p-1 w-6 h-6 rounded-full"></div>
          </div>
        </div>

        <div className="max-w-2xl mx-auto px-6">
          <div className="relative space-y-6">
            {/* Timeline vertical line */}
            <div className="absolute left-[21px] top-0 bottom-0 w-0.5 bg-gray-200"></div>

            {/* Generate skeleton items */}
            {skeletonItems.map((item, index) => (
              <div key={index} className="relative">
                {/* Timeline vertical line for each item */}
                {index !== skeletonItems.length - 1 && (
                  <div
                    className="absolute left-[21px] w-0.5 bg-gray-200"
                    style={{
                      top: '2.5rem',
                      height: item.hasSubItems ? `${(item.subItemCount * 3) + 3}rem` : '3rem',
                      opacity: 1,
                      visibility: 'visible',
                    }}
                  />
                )}

                <div className="flex items-start">
                  {/* Icon placeholder */}
                  <div className="relative z-[1] bg-white p-2 rounded-full border border-gray-200 shadow-sm">
                    <div className="skeleton-pulse w-6 h-6 rounded-full"></div>
                  </div>

                  {/* Content placeholder */}
                  <div className="ml-4 flex-1">
                    <div className="flex items-center space-x-2">
                      <div className="flex-1">
                        <div className="skeleton-pulse h-5 w-32"></div>
                      </div>
                      <div className="skeleton-pulse w-2.5 h-2.5 rounded-full"></div>
                    </div>
                    <div className="skeleton-pulse h-3 w-48 mt-1"></div>
                  </div>
                </div>

                {/* Sub-activities skeleton */}
                {item.hasSubItems && (
                  <div className="ml-7 space-y-4 mt-2.5 pl-4">
                    {Array(item.subItemCount).fill().map((_, subIndex) => (
                      <div key={`sub-${index}-${subIndex}`} className="relative flex items-start group pl-6">
                        <div className="flex-1">
                          <div className="flex items-center">
                            <div className="flex items-center justify-between w-full">
                              <div className="flex items-center space-x-2">
                                <div className="skeleton-pulse h-4 w-24"></div>
                                {/* Random width for variety */}
                                {Math.random() > 0.7 && (
                                  <div className="skeleton-pulse h-4 w-6 rounded-full"></div>
                                )}
                              </div>
                              <div className="flex items-center justify-end min-w-[20px]">
                                <div className="skeleton-pulse w-2 h-2 rounded-full"></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

const CollapseViewer = () => {
    const pathname = usePathname();
    const router = useRouter();
    const projectId = pathname.split("/")[2];
    const { activeLeftPanelTab, setActiveLeftPanelTab } = useContext(StateContext);
    const { isAutoConfigInProgress } = useContext(ExecutionContext);

    const [projectStatus, setProjectStatus] = useState({});
    const [refresh, setRefresh] = useState(false);
    const [isLoading, setIsLoading] = useState(true);

    // Modify useEffect to handle loading state
    useEffect(() => {
        const fetchFlowlineStatus = async () => {
            setIsLoading(true);
            try {
                const response = await getFlowlineStatus(projectId);
                if (response) {
                    const sys=Object.entries(response.architecture.systemContext)
                                .filter(([_, value]) => value === true)
                                .map(([key]) => key)
                    const req=Object.entries(response.architecture.Requirement)
                    .filter(([_, value]) => value === true)
                    .map(([key]) => key)
                    // Transform the response
                    const transformedResponse = {
                        project_assets: {
                            repo: response.project_assets.repo > 0 ? "completed" : "pending",
                            doc: "pending", // You might want to add doc field in original response
                            figma: "pending", // You might want to add figma field in original response
                            // overall: response.project_assets.repo > 0 ? "completed" : "inprogress"
                            repoCount: response.project_assets.repo,
                            overall: response.project_assets.repo > 0 ? "completed" : "pending"
                        },
                        project_setup: Object.values(response.project_setup).every(value => value === true)
                            ? "completed"
                            : "pending",
                        workItems: response.workItems.task > 0 ? "completed" : "pending",
                        requirement: {
                            epic: response.requirements.epic > 0 ? "completed" : "pending",
                            epicCount: response.requirements.epic,
                            userStoryCount: response.requirements.userStory,
                            userStory: response.requirements.userStory > 0 ? "completed" : "pending",
                            overall: response.requirements.epic > 0 && response.requirements.userStory > 0
                                ? "completed"
                                : (response.requirements.epic > 0 || response.requirements.userStory > 0)
                                ? "in-progress"
                                : "pending"
                        },
                        architecture: {
                            systemContext: sys ,
                            requirement: req,
                            design: response.architecture.design,
                            component: response.architecture.component,
                            container: response.architecture.container,
                            interface: response.architecture.interface,
                            overall: req.length > 0 && sys.length> 0 && response.architecture.design.length > 0 && response.architecture.component.length > 0
                                && response.architecture.container.length > 0 && response.architecture.interface.length > 0
                                ? "completed"
                                : (response.architecture.design.length > 0 || response.architecture.component.length > 0
                                || response.architecture.container.length > 0 || response.architecture.interface.length > 0 || req.length > 0 || sys.length > 0)
                                ? "in-progress"
                                : "pending"
                        },
                        codegen_status: response.codegen_status,
                    };



                    setProjectStatus(transformedResponse);
                }
            } catch (error) {
                
            } finally {
                setIsLoading(false);
            }
        };

        if (projectId) {
            fetchFlowlineStatus();
        }
    }, [projectId, refresh]);

    const onRedirect = (activityType) => {

        switch (activityType) {
            case 'Project Setup':
                router.push(`/project/${projectId}/overview`);
                break;
            case 'Code Query':
                router.push(`/project/${projectId}/query`);
                break;
            case 'Code Maintenance':
                router.push(`/project/${projectId}/code/maintenance`);
                break;
            case 'Code Generation':
                router.push(`/project/${projectId}/architecture/design`);
                break;
            case 'ArchitectureRequirements':
                router.push(`/project/${projectId}/architecture/architecture-requirement`);
                break;
            case 'Epics':
            case 'User Story':
            case 'Requirements':
                router.push(`/project/${projectId}/requirements`);
                break;
            case 'Work Items':
                router.push(`/project/${projectId}/workitems`);
                break;
            case 'Add UI/UX':
                router.push(`${pathname}?ProjectAsset=design`);
                break;
            case 'Add Repo':
                router.push(`${pathname}?ProjectAsset=code`);
                break;
            case 'Add Doc':
                router.push(`${pathname}?ProjectAsset=documents`);
                break;
            case 'SystemContext':
                router.push(`/project/${projectId}/architecture/system-context`);
                break;
            case 'Container':
                router.push(`/project/${projectId}/architecture/container`);
                break;
            case 'Component':
                router.push(`/project/${projectId}/architecture/component`);
                break;
            case 'Design':
                router.push(`/project/${projectId}/architecture/design`);
                break;
            case 'Interfaces':
                router.push(`/project/${projectId}/architecture/interfaces`);
                break;
            // Add more cases as needed
            default:

                break;
        }
    };

    const handleTabChange = (tabName) => {
        setActiveLeftPanelTab(tabName);
    };

    return (
        <div className="flex flex-col h-full overflow-hidden">
            {/* Tab navigation */}
            <div className="sticky top-0 z-10 w-full bg-white border-b border-gray-200 shadow-xs">
                <div className="flex w-full h-8">
                    <button
                        onClick={() => handleTabChange('timeline')}
                        className={`flex items-center justify-center gap-2 px-4 py-0.5 flex-1 transition-all duration-200 ease-in-out
                            ${activeLeftPanelTab === 'timeline'
                                ? 'text-blue-600 font-weight-medium border-b border-blue-600 bg-blue-50/20'
                                : 'text-gray-600 hover:bg-gray-50'}`}
                        aria-selected={activeLeftPanelTab === 'timeline'}
                    >
                        <ListTodo className="w-4 h-4" />
                        <span className="typography-body-sm font-weight-medium">Timeline</span>
                    </button>
                    <button
                        onClick={() => handleTabChange('collapse')}
                        className={`flex items-center justify-center gap-2 px-4 py-0.5 flex-1 transition-all duration-200 ease-in-out
                            ${activeLeftPanelTab === 'collapse'
                                ? 'text-blue-600 font-weight-medium border-b border-blue-600 bg-blue-50/20'
                                : 'text-gray-600 hover:bg-gray-50'}`}
                        aria-selected={activeLeftPanelTab === 'collapse'}
                    >
                        <MessageSquare className="w-4 h-4" />
                        <span className="typography-body-sm font-weight-medium">Chat</span>
                    </button>
                </div>
            </div>

            {/* Tab content */}
            <div className="flex-1 overflow-hidden">
                <div
                    className={`h-full w-full transition-opacity duration-300 ${activeLeftPanelTab === 'collapse' ? 'opacity-100 z-10' : 'opacity-0 z-0 absolute inset-0 pointer-events-none'}`}
                >
                    <div className="h-full overflow-hidden w-full">
                        <div className="max-w-2xl mx-auto h-full">
                            <ChatPanel />
                        </div>
                    </div>
                </div>

                <div
                    className={`h-full w-full transition-opacity duration-300 ${activeLeftPanelTab === 'timeline' ? 'opacity-100 z-10' : 'opacity-0 z-0 absolute inset-0 pointer-events-none'}`}
                >
                    <div className="h-full overflow-y-auto scrollbar-hide w-full" id="projectTimeline">
                        {isLoading ? (
                            <TimelineSkeleton />
                        ) : (
                            <ProjectTimeline
                                projectStatus={projectStatus}
                                onRedirect={onRedirect}
                                refetchData={() => {
                                    setRefresh(!refresh);
                                    setProjectStatus({});
                                }}
                                isAutoConfigInProgress={isAutoConfigInProgress}
                            />
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CollapseViewer;
