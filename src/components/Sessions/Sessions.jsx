import React, { useState, useEffect, useRef, createRef, useContext } from 'react';
import { Search, Code, FileEdit, X, Loader2, ExternalLink, Calendar, Filter, Clock, Tag } from 'lucide-react';
import { useSearchParams, usePathname, useRouter, useParams } from 'next/navigation';
import { useCodeGeneration } from '../Context/CodeGenerationContext';
import { listDeployments } from '@/utils/deploymentApi';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import { useUser } from '../Context/UserContext';
import { resumeStartCodeGeneration } from '@/utils/api';
import { AlertContext } from '../NotificationAlertService/AlertList';

const SessionSkeleton = () => (
  <div className="animate-pulse bg-white border border-gray-200 rounded-lg shadow-sm px-4 py-3 mb-4">
    <div className="flex items-center justify-between">
      {/* Left side with icon, title and metadata */}
      <div className="flex items-center gap-2 flex-1">
        <div className="w-8 h-8 bg-gray-200 rounded-full flex-shrink-0"></div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1.5">
            <div className="w-40 h-4 bg-gray-200 rounded-md"></div>
            <div className="w-16 h-4 bg-gray-200 rounded-full"></div>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-20 h-3 bg-gray-200 rounded-md"></div>
            <div className="w-16 h-3 bg-gray-200 rounded-md"></div>
            <div className="w-24 h-3 bg-gray-200 rounded-full"></div>
          </div>
        </div>
      </div>

      {/* Right side with action buttons */}
      <div className="flex items-center gap-2 flex-shrink-0">
        <div className="w-24 h-7 bg-gray-200 rounded-md"></div>
        <div className="w-28 h-7 bg-gray-200 rounded-md"></div>
      </div>
    </div>
  </div>
);

const Sessions = ({ initialSessions = [], isLoading = false, onFilterChange, onCloseModal }) => {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();
  const { is_having_permission } = useUser();
  const { showAlert } = useContext(AlertContext);
  
  // State management
  const [sessions, setSessions] = useState(initialSessions);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState('All types');
  const [statusFilter, setStatusFilter] = useState('All statuses');
  const [dateFilter, setDateFilter] = useState(null); // Change to null instead of empty string
  const { isVisible, setIsVisible, setCurrentIframeUrl, setLlmModel } = useCodeGeneration();
  const [resumingSession, setResumingSession] = useState(null); // Track which session is being resumed

  // Keep existing deployment logic
  const [activeDeploymentModal, setActiveDeploymentModal] = useState(null);
  const [sessionDeployments, setSessionDeployments] = useState({});
  const [loadingDeployments, setLoadingDeployments] = useState({});
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
  const deploymentRefs = useRef({});
  const { projectId } = useParams();
  // Fetch deployments for specific session
  const fetchSessionDeployments = async (sessionId) => {
    setLoadingDeployments(prev => ({ ...prev, [sessionId]: true }));
    try {
      const response = await listDeployments(projectId);
      const sortedDeployments = response.sort(
        (a, b) => new Date(b.created_at) - new Date(a.created_at)
      );
      setSessionDeployments(prev => ({
        ...prev,
        [sessionId]: sortedDeployments
      }));
    } catch (error) {

      showAlert("Failed to fetch deployments", "error");
    } finally {
      setLoadingDeployments(prev => ({ ...prev, [sessionId]: false }));
    }
  };

  // Handle showing deployments for specific session
  const handleShowDeployments = (sessionId, event) => {
    if (activeDeploymentModal === sessionId) {
      setActiveDeploymentModal(null);
    } else {
      // Calculate position based on the button's position
      const buttonRect = event.currentTarget.getBoundingClientRect();
      const viewportWidth = window.innerWidth;

      // Calculate the best position for the dropdown
      let top = buttonRect.bottom + window.scrollY + 5; // Add a small gap
      let left = buttonRect.left + window.scrollX;

      // Check if dropdown would go off the right edge of the screen
      if (left + 384 > viewportWidth) { // 384px is the width of the dropdown (w-96)
        left = Math.max(10, viewportWidth - 394); // Keep at least 10px from the left edge
      }

      // Check if dropdown would go off the bottom of the screen
      // We'll check this dynamically after rendering

      setDropdownPosition({ top, left });
      setActiveDeploymentModal(sessionId);

      if (!sessionDeployments[sessionId]) {
        fetchSessionDeployments(sessionId);
      }
    }
  };

  // Add click outside handler for deploy dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (activeDeploymentModal && !event.target.closest('.deployments-dropdown')) {
        setActiveDeploymentModal(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [activeDeploymentModal]);

  // Adjust dropdown position if it's too tall for the viewport
  useEffect(() => {
    if (activeDeploymentModal) {
      // Give the DOM time to render the dropdown
      setTimeout(() => {
        const dropdown = document.querySelector('.fixed.w-96.bg-white');
        if (dropdown) {
          const dropdownRect = dropdown.getBoundingClientRect();
          const viewportHeight = window.innerHeight;

          // If dropdown extends beyond viewport bottom, adjust its position
          if (dropdownRect.bottom > viewportHeight) {
            // Try to position it above the button if there's enough space
            const button = deploymentRefs.current[activeDeploymentModal]?.current;
            if (button) {
              const buttonRect = button.getBoundingClientRect();
              if (buttonRect.top > dropdownRect.height) {
                // Position above the button
                setDropdownPosition(prev => ({
                  ...prev,
                  top: buttonRect.top + window.scrollY - dropdownRect.height - 5
                }));
              } else {
                // Not enough space above, just make sure it doesn't extend too far below
                setDropdownPosition(prev => ({
                  ...prev,
                  top: Math.max(10, viewportHeight - dropdownRect.height - 10 + window.scrollY)
                }));
              }
            }
          }
        }
      }, 50);
    }
  }, [activeDeploymentModal, sessionDeployments]);

  // Update sessions when props change
  useEffect(() => {
    setSessions(initialSessions);
  }, [initialSessions]);

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  // Update the applyFilters function to format the date
  const applyFilters = () => {
    const filters = {
      search: searchQuery,
      type: typeFilter !== 'All types' ? typeFilter : null,
      status: statusFilter !== 'All statuses' ? statusFilter : null,
      created_at: dateFilter ? dayjs(dateFilter).format('YYYY-MM-DD') : null
    };
    onFilterChange(filters);
  };

  // Get icon based on type
  const getIcon = (iconType) => {
    switch (iconType) {
      case 'code':
        return <Code size={16} />;
      case 'edit':
        return <FileEdit size={16} />;
      default:
        return <Code size={16} />;
    }
  };

  // Get type badge style
  const getTypeBadgeStyle = (iconType) => {
    switch (iconType) {
      case 'code':
        return 'bg-green-100 text-green-800';
      case 'edit':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleRowClick = (row) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("task_id", row.id);
    router.push(`${pathname}?${newSearchParams.toString()}`);
    setIsVisible(true);
  };

  // Get type label
  const getTypeLabel = (iconType) => {
    switch (iconType) {
      case 'code':
        return 'Code Generation';
      case 'edit':
        return 'Code Maintenance';
      default:
        return 'Unknown';
    }
  };

  // Get status style
  const getStatusStyle = (status) => {
    switch (status?.toLowerCase()) {
      case 'running':
        return 'bg-blue-100 text-blue-600';
      case 'submitted':
        return 'bg-yellow-100 text-yellow-600';
      case 'failed':
        return 'bg-red-100 text-red-600';
      default:
        return 'bg-gray-100 text-gray-600';
    }
  };

  // Handle resume code generation functionality
  const handleResumeClick = async (session) => {
    if (!session.id) {
      showAlert("Session ID not found", "error");
      return;
    }

    setResumingSession(session.id);
    
    try {
      // Extract architecture ID and container ID from session if available
      const architectureId = session.architecture_id || null;
      const containerId = session.container_id || null;
      
      const response = await resumeStartCodeGeneration(
        projectId, 
        architectureId, 
        session.id,
        containerId
      );

      if (response && response.task_id) {
        showAlert("Code generation resumed successfully", "success");
        
        // Close the modal if callback is provided
        if (onCloseModal) {
          onCloseModal();
        }
        
        // Navigate with the task_id parameter
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.set("task_id", response.task_id);
        router.push(`${pathname}?${newSearchParams.toString()}`);
      } else if (response && response.message) {
        // Handle case where response doesn't have task_id but has a message
        showAlert(response.message, response.end ? "success" : "info");
        if (response.end && onCloseModal) {
          onCloseModal();
        }
      } else {
        // Handle unexpected response format
        showAlert("Unexpected response format. Please try again.", "warning");
      }
    } catch (error) {
      console.error("Failed to resume code generation:", error);
      
      // Better error handling for different types of errors
      let errorMessage = "Failed to resume code generation. Please try again.";
      
      if (error.message.includes("JSON")) {
        errorMessage = "Response format error. Please try again or contact support.";
      } else if (error.message.includes("network") || error.message.includes("fetch")) {
        errorMessage = "Network error. Please check your connection and try again.";
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      showAlert(errorMessage, "error");
    } finally {
      setResumingSession(null);
    }
  };

  // Common JSX for the filters section that appears in both loading and loaded states
  const renderFilters = () => (
    <div className="sticky top-0 z-10 bg-white border-b border-gray-200 px-6 py-5">
      <div className="flex flex-col space-y-4">
        <h3 className="typography-body-lg font-weight-medium text-gray-800">Filter Sessions</h3>

        <div className="flex flex-wrap gap-4 items-center">
          {/* Search input with improved styling */}
          <div className="relative flex-shrink-0 w-72">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-4 w-4 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search sessions..."
              className="w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg typography-body-sm focus:ring-2 focus:ring-orange-500 focus:border-transparent shadow-sm"
              value={searchQuery}
              onChange={handleSearchChange}
              onKeyDown={(e) => e.key === 'Enter' && applyFilters()}
            />
            <button
              onClick={applyFilters}
              className="absolute inset-y-0 right-0 px-3 flex items-center bg-orange-500 hover:bg-orange-600 text-white rounded-r-lg transition-colors"
            >
              <Search className="h-4 w-4" />
            </button>
          </div>

          {/* Type filter with icon */}
          <div className="relative flex-shrink-0">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Tag className="h-4 w-4 text-gray-400" />
            </div>
            <select
              className="pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg typography-body-sm focus:ring-2 focus:ring-orange-500 focus:border-transparent shadow-sm appearance-none bg-white min-w-[160px]"
              value={typeFilter}
              onChange={(e) => {
                setTypeFilter(e.target.value);
                applyFilters();
              }}
            >
              <option>All types</option>
              <option>Generation</option>
              <option>Maintenance</option>
            </select>
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>

          {/* Status filter with icon */}
          <div className="relative flex-shrink-0">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Filter className="h-4 w-4 text-gray-400" />
            </div>
            <select
              className="pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg typography-body-sm focus:ring-2 focus:ring-orange-500 focus:border-transparent shadow-sm appearance-none bg-white min-w-[160px]"
              value={statusFilter}
              onChange={(e) => {
                setStatusFilter(e.target.value);
                applyFilters();
              }}
            >
              <option>All statuses</option>
              <option>Running</option>
              <option>Submitted</option>
              <option>Failed</option>
            </select>
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>

          {/* Date picker with icon */}
          <div className="relative flex-shrink-0">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
              <Calendar className="h-4 w-4 text-gray-400" />
            </div>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DatePicker
                value={dateFilter}
                onChange={(newValue) => {
                  setDateFilter(newValue);
                  // Apply filters immediately with the new date
                  const filters = {
                    search: searchQuery,
                    type: typeFilter !== 'All types' ? typeFilter : null,
                    status: statusFilter !== 'All statuses' ? statusFilter : null,
                    created_at: newValue ? dayjs(newValue).format('YYYY-MM-DD') : null
                  };
                  onFilterChange(filters);
                }}
                slotProps={{
                  textField: {
                    size: "small",
                    style: { width: '180px' },
                    className: "pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg typography-body-sm focus:ring-2 focus:ring-orange-500 focus:border-transparent shadow-sm",
                    placeholder: "Filter by date..."
                  },
                  field: {
                    clearable: true,
                    onClear: () => {
                      setDateFilter(null);
                      const filters = {
                        search: searchQuery,
                        type: typeFilter !== 'All types' ? typeFilter : null,
                        status: statusFilter !== 'All statuses' ? statusFilter : null,
                        created_at: null
                      };
                      onFilterChange(filters);
                    }
                  }
                }}
              />
            </LocalizationProvider>
          </div>
        </div>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className="flex flex-col flex-1 min-h-0 bg-gray-50">
        {/* Keep filters section visible during loading */}
        {renderFilters()}
        {/* Show skeleton loader only for sessions list */}
        <div className="flex-1 overflow-auto p-6">
          <div className="mb-6">
            <h2 className="typography-heading-4 font-weight-semibold text-gray-800 mb-1">Session History</h2>
            <p className="typography-body-sm text-gray-500">Loading your recent sessions...</p>
          </div>

          <div className="grid grid-cols-1 gap-4">
            {[...Array(4)].map((_, idx) => (
              <SessionSkeleton key={idx} />
            ))}
          </div>
        </div>
      </div>
    );
  }

  const renderDeploymentButton = (session) => {
    const sessionDeploys = sessionDeployments[session.id] || [];
    const isLoading = loadingDeployments[session.id];
    const deploymentCount = sessionDeploys.length;

    // Ensure we have a ref for this session
    if (!deploymentRefs.current[session.id]) {
      deploymentRefs.current[session.id] = createRef();
    }

    return (
      <div className="relative deployments-dropdown" ref={deploymentRefs.current[session.id]}>
        <button
          onClick={(e) => handleShowDeployments(session.id, e)}
          data-session-id={session.id}
          className="flex items-center gap-1 px-3 py-1.5 typography-caption font-weight-medium bg-orange-500 text-white hover:bg-orange-600 rounded-md shadow-sm transition-colors"
        >
          <span>Deployed Apps</span>
          {deploymentCount > 0 && (
            <span className="flex items-center justify-center bg-white text-orange-600 rounded-full h-4 w-4 typography-caption font-weight-semibold">
              {deploymentCount}
            </span>
          )}
        </button>

        {activeDeploymentModal === session.id && (
          <div
            className="fixed w-96 bg-white rounded-lg shadow-xl border border-gray-200 z-[100] max-h-[80vh] flex flex-col"
            style={{
              top: `${dropdownPosition.top}px`,
              left: `${dropdownPosition.left}px`,
              maxHeight: "80vh"
            }}
          >
            <div className="p-4 border-b border-gray-200 flex-shrink-0">
              <div className="flex items-center justify-between">
                <h3 className="typography-body font-weight-medium text-gray-900 flex items-center gap-2">
                  <span className="w-2 h-2 rounded-full bg-orange-500"></span>
                  Deployment History
                </h3>
                <button
                  onClick={() => setActiveDeploymentModal(null)}
                  className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100"
                >
                  <X size={16} />
                </button>
              </div>
              <p className="typography-caption text-gray-500 mt-1">{session.title}</p>
            </div>

            <div className="overflow-y-auto p-4 flex-grow">
              {isLoading ? (
                <div className="flex flex-col justify-center items-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-orange-500 mb-2" />
                  <p className="typography-body-sm text-gray-500">Loading deployments...</p>
                </div>
              ) : sessionDeploys.length === 0 ? (
                <div className="text-center py-8 px-4">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <ExternalLink className="h-8 w-8 text-gray-400" />
                  </div>
                  <p className="typography-body font-weight-medium text-gray-700">No deployments found</p>
                  <p className="typography-body-sm text-gray-500 mt-1">There are no deployments for this session yet.</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {sessionDeploys.map((deployment) => (
                    <div
                      key={deployment.id}
                      className="bg-white rounded-md p-2 border border-gray-200 hover:border-orange-200 transition-all duration-200"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0 mr-2">
                          <p className="typography-body-sm font-weight-medium text-gray-900 truncate">
                            {deployment.root_path
                              ? deployment.root_path.split("/").filter(Boolean).pop()
                              : "Unknown Folder"}
                          </p>
                          <div className="flex items-center justify-between">
                            <p className="typography-caption text-gray-500 flex items-center gap-1 truncate">
                              <span className="inline-block w-2 h-2 flex-shrink-0 rounded-full bg-gray-300"></span>
                              <span className="truncate">{deployment.branch_name || "Default Branch"}</span>
                            </p>
                            {deployment.app_url && (
                              <a
                                href={deployment.app_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-800 ml-2 flex-shrink-0"
                                onClick={(e) => e.stopPropagation()}
                                title={deployment.app_url}
                              >
                                <ExternalLink size={12} />
                              </a>
                            )}
                          </div>
                        </div>
                        <span className={`flex-shrink-0 px-2 py-0.5 typography-caption font-weight-medium rounded-full whitespace-nowrap ${
                          deployment.status === "success"
                            ? "bg-green-100 text-green-800"
                            : deployment.status === "processing"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-red-100 text-red-800"
                        }`}>
                          {deployment.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="flex flex-col flex-1 min-h-0 bg-gray-50">
      {renderFilters()}
      <div className="flex-1 overflow-auto p-6">
        {/* Sessions List */}
        <div className="grid grid-cols-1 gap-4">
          {sessions.map((session) => (
            <div
              key={session.id}
              className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow transition-all duration-200 overflow-hidden"
            >
              <div className="px-4 py-3">
                <div className="flex flex-col space-y-2">
                  {/* Main row with all essential information */}
                  <div className="flex items-center justify-between">
                    {/* Left side with icon, title and date */}
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      <div className="p-1.5 bg-orange-50 rounded-full text-orange-500 flex-shrink-0">
                        {getIcon(session.icon)}
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="flex items-center gap-2">
                          <h3 className="font-weight-medium text-gray-900 truncate">{session.title}</h3>
                          <div className={`px-2 py-0.5 rounded-full typography-caption font-weight-medium ${getStatusStyle(session.status)}`}>
                            {session.status}
                          </div>
                        </div>
                        <div className="flex items-center gap-2 typography-caption text-gray-500">
                          <span>{session.date}</span>
                          <span className="inline-flex items-center gap-1">
                            <Clock size={10} />
                            {session.duration}
                          </span>
                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full typography-caption font-weight-medium ${getTypeBadgeStyle(session.icon)}`}>
                            {getTypeLabel(session.icon)}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Right side with action buttons */}
                    <div className="flex items-center gap-2 flex-shrink-0">
                      <button
                        onClick={() => handleRowClick(session)}
                        disabled={!is_having_permission()}
                        className={`px-3 py-1.5 typography-caption font-weight-medium ${!is_having_permission()
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          : 'text-orange-600 hover:text-orange-700 bg-orange-50 hover:bg-orange-100'
                        } rounded-md transition-colors flex items-center gap-1`}
                      >
                        <span>View Details</span>
                      </button>
                      <button
                        onClick={() => handleResumeClick(session)}
                        disabled={!is_having_permission() || resumingSession === session.id}
                        className={`px-3 py-1.5 typography-caption font-weight-medium ${!is_having_permission() || resumingSession === session.id
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          : 'text-orange-600 hover:text-orange-700 bg-orange-50 hover:bg-orange-100'
                        } rounded-md transition-colors flex items-center gap-1`}
                      >
                        {resumingSession === session.id ? (
                          <>
                            <Loader2 className="h-3 w-3 animate-spin" />
                            <span>Resuming...</span>
                          </>
                        ) : (
                          <span>Resume</span>
                        )}
                      </button>
                      {renderDeploymentButton(session)}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}

          {sessions.length === 0 && (
            <div className="text-center py-16 bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Search className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="typography-body-lg font-weight-medium text-gray-900 mb-1">No sessions found</h3>
              <p className="typography-body-sm text-gray-500 max-w-md mx-auto">
                We couldn't find any sessions matching your current filters. Try adjusting your search criteria or create a new session.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Sessions;