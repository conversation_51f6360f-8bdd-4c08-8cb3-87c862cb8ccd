import React, {
  useEffect,
  useState,
  useContext,
} from "react";
import { useCodeGeneration } from "./Context/CodeGenerationContext";
import { useParams, useSearchParams } from "next/navigation";

import { AlertContext } from "./NotificationAlertService/AlertList";
import { retryTask } from "@/utils/batchAPI";

import ChatInterface from "./SocketIoChat";

const CodeDiscussionPanel = ({isPanelExpanded, isStopped}) => {
  const {
    isGenerating,
    wsStatus,
    reconnectWebSocket,
    wsUrl,
    messages,
    setMessages,
    wsConnection,
    llmModel,
    setLlmModel
  } = useCodeGeneration();
  
  // Project Id
  const { projectId } = useParams();
  const searchParams = useSearchParams();
  const { showAlert } = useContext(AlertContext);
  const [currentTaskDetailsId, setCurrentTaskDetailsId] = useState(
    searchParams.get("task_id")
  );
  const [isRetrying, setIsRetrying] = useState(false);
  const [deploymentStatus, setDeploymentStatus] = useState(null);
  const [availableModels, setAvailableModels] = useState([]);
  const [isLoadingModels, setIsLoadingModels] = useState(false);

  // Auto reconnect effect
  useEffect(() => {
    if (wsStatus === "disconnected" && currentTaskDetailsId) {
      const reconnectTimer = setTimeout(() => {
        reconnectWebSocket(currentTaskDetailsId);
      }, 3000);

      return () => clearTimeout(reconnectTimer);
    }
  }, [wsStatus, currentTaskDetailsId, reconnectWebSocket]);

  useEffect(() => {
    setCurrentTaskDetailsId(searchParams.get("task_id"));
  }, [searchParams]);

  // Listen for deployment status messages from the existing WebSocket connection
  useEffect(() => {
    if (!wsConnection) return;
    
    const handleWebSocketMessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        if (data.type === "deployment_status") {
          // Update local state to pass to ChatInterface
          setDeploymentStatus(data.data);
        }
        
        // Handle available models response
        if (data.type === "available_models" && data.data?.models) {
          setAvailableModels(data.data.models);
          setIsLoadingModels(false);
        }
        
        // Handle current model response
        if (data.type === "current_model" && data.data?.model) {
          if (setLlmModel && typeof setLlmModel === 'function') {
            setLlmModel(data.data.model);
          }
          setIsLoadingModels(false);
        }
      } catch (error) {
        
      }
    };

    // Only add the listener if it's not already in CodeGenerationContext
    wsConnection.addEventListener('message', handleWebSocketMessage);
    
    return () => {
      wsConnection.removeEventListener('message', handleWebSocketMessage);
    };
  }, [wsConnection, setLlmModel]);

  // Request available models when WebSocket is connected
  useEffect(() => {
    if (wsConnection?.readyState === WebSocket.OPEN && currentTaskDetailsId && 
        !currentTaskDetailsId.startsWith("deep-query-job")) {
      // Set loading state
      setIsLoadingModels(true);
      
      // Request available models
      wsConnection.send(
        JSON.stringify({
          type: "get_available_models",
          task_id: currentTaskDetailsId,
        })
      );
      
      // Request current model
      wsConnection.send(
        JSON.stringify({
          type: "get_current_model",
          task_id: currentTaskDetailsId,
        })
      );
    }
  }, [wsConnection, currentTaskDetailsId]);

  const handleRetry = async () => {
    try {
      setIsRetrying(true);
      const result = await retryTask(currentTaskDetailsId);
      
      if (result.status === "success") {
        showAlert("Task retried successfully", "success");
      } else {
        showAlert(result.message || "Failed to retry task", "error");
      }
    } catch (error) {
      
      showAlert("Failed to retry task. Please try again.", "error");
    } finally {
      setIsRetrying(false);
    }
  };

  // Handle model selection and send to server via WebSocket
  const handleModelSelect = (modelId) => {
    if (!modelId) return;

    // Update context state
    if (setLlmModel && typeof setLlmModel === 'function') {
      setLlmModel(modelId);
    }
    
    // Send model selection to WebSocket
    if (wsConnection?.readyState === WebSocket.OPEN && currentTaskDetailsId) {
      wsConnection.send(
        JSON.stringify({
          type: "set_model",
          task_id: currentTaskDetailsId,
          model: modelId
        })
      );
    }
  };

  return (
    <div
      className="chat-panel flex flex-col h-full overflow-hidden bg-white border p-1 rounded-[6px]"
    >
      <ChatInterface 
        wsUrl={wsUrl} 
        isPanelExpanded={isPanelExpanded} 
        deploymentStatus={deploymentStatus}
        availableModels={availableModels}
        isLoadingModels={isLoadingModels}
        onModelSelect={handleModelSelect}
        isStopped={isStopped}
      />
    </div>
  );
};

export default CodeDiscussionPanel;