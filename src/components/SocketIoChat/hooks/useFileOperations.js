import { useState, useRef, useCallback } from 'react';

export const useFileOperations = (messages, autoScroll, messagesContainerRef, isAtBottom) => {
  // New state for tracking file operations
  const [fileOperationsMap, setFileOperationsMap] = useState({});
  const [pendingFileOperations, setPendingFileOperations] = useState({});
  const [editOperationTimeouts, setEditOperationTimeouts] = useState({});
  const [fileOpsUpdateCounter, setFileOpsUpdateCounter] = useState(0);
  
  // Add refs for tracking updates
  const isMessageUpdateScheduled = useRef(false);

  // Update the file operations handling function to use the pending operations strategy
  const updateFileOperations = useCallback((messageId, operation, filePath, isEnd, operationId, forceUpdate = false) => {
    // Generate a consistent operation ID if not provided
    const opId = operationId || `${messageId}-${filePath.replace(/[^a-zA-Z0-9]/g, '-')}`;

    // Special handling for edit operations - they may never get an end event
    const isEditOperation = operation === 'edit';

    // For edit operations, treat as complete immediately
    const shouldMarkAsCompleted = isEnd || isEditOperation;

    // For non-end operations, store them in pending state
    if (!shouldMarkAsCompleted) {
      setPendingFileOperations(prev => ({
        ...prev,
        [opId]: { messageId, operation, filePath, operationId: opId, status: 'processing' }
      }));
    } else if (isEditOperation && !isEnd) {
      // For edit operations, remove from pending and clear any timeout
      setPendingFileOperations(prev => {
        const newPending = {...prev};
        delete newPending[opId];
        return newPending;
      });

      if (editOperationTimeouts[opId]) {
        clearTimeout(editOperationTimeouts[opId]);
        setEditOperationTimeouts(prev => {
          const newTimeouts = {...prev};
          delete newTimeouts[opId];
          return newTimeouts;
        });
      }
    }

    // Update the main file operations map for in-progress operations
    setFileOperationsMap(prevMap => {
      // Find the actual message ID to use
      const targetMessage = messages.find(m => m.id === messageId ||
        (typeof m.id === 'string' && typeof messageId === 'string' &&
        (m.id.includes(messageId) || messageId.includes(m.id))));

      const actualMessageId = targetMessage?.id || messageId;

      // Get existing operations or create new array
      const existingOps = prevMap[actualMessageId] || [];

      // Check if this operation already exists
      const existingOpIndex = existingOps.findIndex(op =>
        op.id === opId || op.path === filePath
      );

      // Create updated operations array
      let updatedOps;
      if (existingOpIndex >= 0) {
        // Update existing operation
        updatedOps = [...existingOps];
        updatedOps[existingOpIndex] = {
          ...updatedOps[existingOpIndex],
          status: shouldMarkAsCompleted ? 'completed' : 'processing',
          label: "",
          path: filePath,
          id: opId
        };
      } else {
        // Add new operation
        updatedOps = [
          ...existingOps,
          {
            id: opId,
            status: shouldMarkAsCompleted ? 'completed' : 'processing',
            label: "",
            path: filePath
          }
        ];
      }

      // Preserve previous operation ordering when possible to minimize DOM changes
      if (existingOps.length > 0 && updatedOps.length === existingOps.length) {
        // Just update the status without changing the order
        const newOps = [...existingOps];
        for (let i = 0; i < newOps.length; i++) {
          if (newOps[i].id === opId || newOps[i].path === filePath) {
            newOps[i] = {
              ...newOps[i],
              status: shouldMarkAsCompleted ? 'completed' : 'processing'
            };
          }
        }
        updatedOps = newOps;
      }

      // Create new map with updated operations
      return { ...prevMap, [actualMessageId]: updatedOps };
    });

    // For edit operations or forced updates, trigger UI update immediately
    if (isEditOperation || forceUpdate) {
      // Use setTimeout to batch these updates for better performance
      if (!isMessageUpdateScheduled.current) {
        isMessageUpdateScheduled.current = true;
        setTimeout(() => {
          setFileOpsUpdateCounter(prev => prev + 1);
          isMessageUpdateScheduled.current = false;

          // Minimal scrolling for immediate updates
          if (autoScroll && isAtBottom()) {
            setTimeout(() => {
              if (messagesContainerRef.current) {
                messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
              }
            }, 100);
          }
        }, 50);
      }
      return;
    }

    // For completed operations, proceed with full update
    if (shouldMarkAsCompleted) {
      // Remove from pending operations if this is a completion
      setPendingFileOperations(prev => {
        const newPending = {...prev};
        delete newPending[opId];
        return newPending;
      });

      // Batch update counter changes to reduce rerenders
      if (!isMessageUpdateScheduled.current) {
        isMessageUpdateScheduled.current = true;
        setTimeout(() => {
          setFileOpsUpdateCounter(prev => prev + 1);
          isMessageUpdateScheduled.current = false;

          // Minimal scrolling for file operations - just a single direct scroll
          if (autoScroll && isAtBottom()) {
            setTimeout(() => {
              if (messagesContainerRef.current) {
                messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
              }
            }, 100);
          }
        }, 50);
      }
    }
  }, [messages, autoScroll, messagesContainerRef, isAtBottom, editOperationTimeouts]);

  return {
    fileOperationsMap,
    pendingFileOperations,
    editOperationTimeouts,
    fileOpsUpdateCounter,
    updateFileOperations,
    setEditOperationTimeouts,
    setPendingFileOperations,
    setFileOpsUpdateCounter
  };
}; 