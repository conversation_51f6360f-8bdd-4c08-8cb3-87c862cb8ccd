import { useEffect, useCallback } from 'react';

export const useWebSocketMessages = ({
  wsConnection,
  handleInitialMessages,
  safeAddOrUpdateMessageInState,
  autoScroll,
  isAtBottom,
  scrollToBottom,
  isManualScrollingStillActive,
  setIsAiTyping,
  setIsReady,
  updateFileOperations,
  setMessages,
  messages,
  fileOperationsMap,
  setFileOperationsMap,
  openAccordions,
  setOpenAccordions,
  pendingFileOperations,
  handleRollbackStatusUpdate
}) => {

  useEffect(() => {
    if (!wsConnection) return;

    // Add connection error handling
    const handleConnectionError = (error) => {
      setIsAiTyping(false);
      // Show error message to user
      if (setMessages && typeof setMessages === 'function') {
        setMessages(prev => [
          ...prev,
          {
            id: Date.now(),
            content: "Connection error. Please refresh the page and try again.",
            msg_type: "error",
            timestamp: new Date().toISOString()
          }
        ]);
      }
    };

    const handleMessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        // Auto-scroll for important message events
        const shouldAutoScroll = ['message_received', 'message_chunk', 'message_resolved', 'agent_message'].includes(data.type);

        if (shouldAutoScroll && autoScroll && isAtBottom()) {
          // Use setTimeout to scroll after DOM updates
          setTimeout(() => scrollToBottom(), 50);
        }

        switch (data.type) {
          case "ready_check":
            setIsReady(true);
            break;

          case 'initial_messages':
            handleInitialMessages(data.data);
            break;

          case 'file_update':
            if (data.data) {
              // Extract file operation details
              const messageId = data.data.message_id;
              const message = data.data.message  || "write";
              const operation = data.data.operation || "write";
              const filePath = data.data.file_name;
              const isEnd = !!data.data.is_end;
              const operationId = data.data.id || `${messageId}-${filePath.replace(/[^a-zA-Z0-9]/g, '-')}`;

              // Set display text based on operation type and completion status
              let operationDisplayText;
              if (isEnd) {
                operationDisplayText = "Completed";
              } else {
                // When operation is in progress, show specific operation text
                if (operation === "write") {
                  operationDisplayText = "Updating";
                } else if (operation === "read") {
                  operationDisplayText = "Reading";
                } else {
                  // For any other operation, use the operation value as is
                  operationDisplayText = operation;
                }
              }

              if (messageId && filePath) {
                // Find messages to attach this operation to
                const possibleMessages = messages.filter(m => m.msg_type === 'llm');

                // Try to find matching messages
                const exactMatch = possibleMessages.find(m => m.id === messageId);
                const partialMatch = !exactMatch && possibleMessages.find(m =>
                  (typeof m.id === 'string' && typeof messageId === 'string' &&
                  (m.id.includes(messageId) || messageId.includes(m.id)))
                );

                // Use the best match or most recent message
                const targetMessage = exactMatch || partialMatch ||
                  (possibleMessages.length > 0 ? possibleMessages[possibleMessages.length - 1] : null);

                if (targetMessage) {
                  // Create operation ID for tracking
                  const trackingId = `${targetMessage.id}-${filePath.replace(/[^a-zA-Z0-9]/g, '-')}`;

                  // For first file operation in a message, auto-open the accordion
                  const existingOperations = fileOperationsMap[targetMessage.id] || [];
                  if (existingOperations.length === 0) {
                    // Auto-open the accordion for the first file operation
                    // Force open by explicitly setting rather than using the setter function
                    const updatedAccordions = {...openAccordions};
                    updatedAccordions[targetMessage.id] = true;
                    setOpenAccordions(updatedAccordions);
                  }

                  // Check if this is the first operation for this file or the end operation
                  const isFirstOperation = !existingOperations.some(op => op.path === filePath);

                  // Only call updateFileOperations for the first event or the end event
                  if (isFirstOperation || isEnd) {
                    updateFileOperations(targetMessage.id, operationDisplayText, filePath, isEnd, trackingId, true);
                  }
                } else if (messageId) {
                  // If no matching message but we have an ID, use it directly
                  // Check if this is a new operation or an end operation
                  const existingOpKey = Object.keys(pendingFileOperations).find(
                    key => pendingFileOperations[key].messageId === messageId &&
                          pendingFileOperations[key].filePath === filePath
                  );
                  const isFirstOperation = !existingOpKey;

                  // Only call updateFileOperations for the first event or the end event
                  if (isFirstOperation || isEnd) {
                    updateFileOperations(messageId, operationDisplayText, filePath, isEnd, operationId);
                  }

                  // For completion messages with no match, create a debug entry
                  if (isEnd) {
                    const debugMessage = {
                      id: `debug-${Date.now()}`,
                      content: `Debug: File operation completed.\nOperation: ${operationDisplayText}\nFile: ${filePath}`,
                      msg_type: 'llm',
                      status: 'completed',
                      timestamp: new Date().toISOString()
                    };

                    safeAddOrUpdateMessageInState(debugMessage, 'debug');

                    // Attach the completed operation to this debug message
                    setTimeout(() => {
                      updateFileOperations(debugMessage.id, operationDisplayText, filePath, true, operationId, true);
                    }, 500);
                  }
                }
              }
            }
            break;

          case 'deployment_status':
            if (data.data && data.data.id) {
              const deploymentId = data.data.id;

              // Create a content message
              let contentMessage = `🚀 Deployment Status: ${data.data.deployment_type || 'Application'} - ${data.data.status}`;
              if (data.data.app_url && data.data.status === 'success') {
                contentMessage += `\nApplication URL: ${data.data.app_url}`;
              }
              if (data.data.branch) {
                contentMessage += `\nBranch: ${data.data.branch}`;
              }

              // Create and add/update the deployment status message
              const deploymentStatusMessage = {
                id: Date.now(),
                type: "deployment_status",
                content: contentMessage,
                sender: "System",
                deployment_data: data.data,
                timestamp: new Date().toISOString(),
                msg_type: "system_message"
              };

              // Add message without forcing scroll
              safeAddOrUpdateMessageInState(deploymentStatusMessage, 'deployment_status');

              // Only auto-scroll if the user hasn't manually scrolled recently
              if (autoScroll && !isManualScrollingStillActive()) {
                // Use a longer delay for deployment messages
                setTimeout(() => {
                  // Double-check that user hasn't scrolled during the delay
                  if (!isManualScrollingStillActive()) {
                    scrollToBottom();
                  }
                }, 300);
              }
            }
            break;

          case 'message_received':
            // When a user message is received, prepare for AI typing
            if (data.data.msg_type === 'user') {
              setIsAiTyping(true);
            }

            // Add message without forcing scroll
            safeAddOrUpdateMessageInState(data.data, data.type);

            // Only auto-scroll for important messages when user hasn't manually scrolled
            if (autoScroll && !isManualScrollingStillActive() && ['user', 'system_message'].includes(data.data.msg_type)) {
              // Use a slight delay to ensure message is rendered
              setTimeout(() => {
                // Double-check user hasn't scrolled during the delay
                if (!isManualScrollingStillActive()) {
                  scrollToBottom();
                }
              }, 100);
            }
            break;

          case 'message_chunk':
            // AI is typing when receiving chunks
            if (data.data.msg_type === 'llm') {
              setIsAiTyping(true);
            }

            // Always use the batched update for chunks to reduce flickering
            safeAddOrUpdateMessageInState(data.data, data.type);
            break;

          case 'message_resolved':
            // AI finished typing
            if (data.data.msg_type === 'llm') {
              setIsAiTyping(false);
            }
            safeAddOrUpdateMessageInState(data.data, data.type);
            break;

          case 'agent_message':
            // Handle agent messages with streaming status
            if (data.data && data.data.status === "streaming") {
              setIsAiTyping(true);

              // Look for an existing streaming message to update
              const streamingMessage = messages.find(m =>
                m.msg_type === 'llm' && m.status === 'streaming'
              );

              if (streamingMessage) {
                // Update existing streaming message with REPLACEMENT (not append)
                safeAddOrUpdateMessageInState({
                  ...streamingMessage,
                  ...data.data,
                  content: data.data.content, // Replace content for streaming agent messages
                  status: 'streaming'
                }, 'agent_message_streaming'); // Special type to indicate content replacement

                // Auto-scroll if at bottom
                if (autoScroll && isAtBottom()) {
                  setTimeout(() => scrollToBottom(), 50);
                }
              } else {
                // Create a new streaming message
                safeAddOrUpdateMessageInState({
                  ...data.data,
                  id: data.data.id || Date.now(),
                  type: "agent_message",
                  status: "streaming",
                  timestamp: data.data.timestamp || new Date().toISOString()
                }, 'agent_message_streaming');

                // Auto-scroll if at bottom
                if (autoScroll && isAtBottom()) {
                  setTimeout(() => scrollToBottom(), 50);
                }
              }
            } else if (data.data) {
              // Regular agent message handling
              safeAddOrUpdateMessageInState(data.data, data.type);

              // If this completes a message, stop AI typing indicator
              if (data.data.status === 'completed' || data.data.status === 'done') {
                setIsAiTyping(false);
                // Final scroll to bottom when message completes
                if (autoScroll) {
                  setTimeout(() => scrollToBottom(), 150);
                }
              }
            }
            break;

          case 'message_status':
          case 'message_added':
          case 'command_response':
            safeAddOrUpdateMessageInState(data.data, data.type);
            if (data.data.msg_type === 'user') {
              setIsAiTyping(true);
            }
            break;

          case 'needs_response':
            if (data.data.requires_resolution == true) {
              setIsAiTyping(false);
            }
            break;

          case 'switch_to_checkpoints_status':
            // Handle rollback status response
            if (handleRollbackStatusUpdate) {
              handleRollbackStatusUpdate(data);
            }
            break;

          case 'error':
            setIsAiTyping(false); // Stop typing animation on error
            break;

          default:
            break;
        }
      } catch (error) {
        console.error('WebSocket message parsing error:', error);
      }
    };

    // Create stable close handler to prevent memory leaks from anonymous functions
    const handleClose = () => {
      setIsAiTyping(false);
    };

    // Register the event listeners
    wsConnection.addEventListener('message', handleMessage);
    wsConnection.addEventListener('error', handleConnectionError);
    wsConnection.addEventListener('close', handleClose);

    return () => {
      // Clean up event listeners for this specific effect
      if (wsConnection) {
        wsConnection.removeEventListener('message', handleMessage);
        wsConnection.removeEventListener('error', handleConnectionError);
        wsConnection.removeEventListener('close', handleClose);
      }
    };
  }, [
    wsConnection,
    handleInitialMessages,
    safeAddOrUpdateMessageInState,
    autoScroll,
    scrollToBottom,
    setIsAiTyping,
    setIsReady,
    updateFileOperations,
    setMessages,
    messages,
    isAtBottom,
    isManualScrollingStillActive,
    fileOperationsMap,
    openAccordions,
    setOpenAccordions,
    pendingFileOperations,
    handleRollbackStatusUpdate
  ]);
}; 