import { useEffect, useState, useContext } from "react";
import { X } from "lucide-react";
import { useRouter } from "next/navigation";
import { TopBarContext } from "../Context/TopBarContext";
import { AlertContext } from "../NotificationAlertService/AlertList";

import EmptyStateView from "../Modal/EmptyStateModal";
import DeleteProjectModal from "../Modal/DeleteProjectModal";
import { deleteNotification } from "@/utils/api";
import { parseISO, isToday, isYesterday } from "date-fns";
import { useNotifications } from "@/utils/useNotifications";
import { NotificationGroup } from "./NotificationGroup";
import { UiButton } from '@/components/UIComponents/Buttons/UiButton';
import en from "../../en.json";
import { NotificationLoadingSkeleton } from "@/components/UIComponents/Loaders/LoaderGroup"

interface NotificationListProps {
  handleValueChange: (value: number) => void;
  handleDrawerToggle: (drawerName: string) => void;
  handleClearAll: () => void;
  theme?: 'light' | 'dark';
}

const NotificationList = ({
  handleValueChange,
  handleDrawerToggle,
  handleClearAll,
  theme = 'light',
}: NotificationListProps) => {
  const {
    notifications,
    loading,
    error,
    markRead,
    markAllRead,
    setNotifications,
    notificationTooltips,
    fetchProject,
  } = useNotifications();

  const router = useRouter();
  const { tabs, addTab, setActiveTab } = useContext(TopBarContext);
  const { showAlert } = useContext(AlertContext);


  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [selectedNotificationId, setSelectedNotificationId] = useState<string | null>(null);
  const [userId, setIsUserId] = useState<string | null>(null);

  useEffect(() => {
    const cachedData = sessionStorage.getItem("notifications");
    if (cachedData) {
      setNotifications(JSON.parse(cachedData));
    }
  }, [setNotifications]);

  useEffect(() => {
    const unreadCount = notifications.filter(
      (notification) => !notification.is_read
    ).length;
    handleValueChange(unreadCount);
  }, [notifications, handleValueChange]);

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await deleteNotification(selectedNotificationId, userId);
      setIsDeleting(false);
      setIsDeleteModalOpen(false);
      showAlert("Notification Deleted Successfully", "success");
      setNotifications((prev: any) =>
        prev.filter((notif: any) => notif.notification_id !== selectedNotificationId)
      );
    } catch (error) {

      setIsDeleting(false);
      showAlert("Failed to delete notification", "danger");
    }
  };

  const openDeleteModal = (notificationId: string, receiverId: string) => {
    setSelectedNotificationId(notificationId);
    setIsUserId(receiverId);
    setIsDeleteModalOpen(true);
  };

  const handleNotificationClick = async (item: any) => {
    markRead(item.notification_id, handleValueChange);
    handleDrawerToggle("Notifications");
    const projectDetails = await fetchProject(item.data.project_id);
    const tabTitle = projectDetails?.properties?.Title || "";

    if (item.type === "discussion") {
      const newSearchParams = new URLSearchParams();
      newSearchParams.set("discussion_id", item.data.discussion_id);
      newSearchParams.set("discussion", "existing");
      router.push(
        `/project/${item.data.project_id}/overview?${newSearchParams.toString()}`
      );
    } else if (item.type === "task") {
      const finalURL = `/project/${item.data.project_id}/requirements/${item.data.task_type}/${item.data.task_id}`;
      handleTabNavigation(finalURL, tabTitle);

    } else if (item.type === "project") {
      const finalURL = `/project/${item.data.project_id}/overview`;
      handleTabNavigation(finalURL, tabTitle);
    } else if (item.type === "code_generation") {
      if (
        item.data.message.includes("cg")
      ) {
        const finalURL = `/project/${item.data.project_id}/architecture/design?task_id=${item.data.task_id}`;
        handleTabNavigation(finalURL, tabTitle);
      } else if (
        item.data.message.includes("cm")
      ) {
        const finalURL = `/project/${item.data.project_id}/code/maintenance?task_id=${item.data.task_id}`;
        handleTabNavigation(finalURL, tabTitle);
      }
      // const finalURL = `/project/${item.data.project_id}/architecture/design?task_id=${item.data.task_id}`;
      // handleTabNavigation(finalURL, tabTitle);
    }

  };

  const handleTabNavigation = (finalURL: string, tabTitle: string) => {
    const existingTab = tabs.find((tab: any) => tab.href.includes(finalURL));
    if (existingTab) {
      setActiveTab(existingTab.id);
    } else {
      addTab(tabTitle, finalURL);
    }
    router.push(finalURL);
  };

  const themeClasses = {
    light: {
      container: "bg-white",
      scrollArea: "bg-white",
      footer: "bg-white border-gray-200",
      markAllButton: "text-blue-600 hover:text-blue-800",
      clearButton: "bg-red-50 hover:bg-red-100 text-red-600 border-red-200",
      clearButtonIcon: "text-red-500",
      errorText: "text-gray-700",
    },
    dark: {
      container: "bg-[#231f20]",
      scrollArea: "bg-[#231f20]",
      footer: "bg-[#231f20] border-gray-700",
      markAllButton: "text-primary-400 hover:text-primary-300",
      clearButton: "bg-red-900/30 hover:bg-red-900/50 text-red-400 border-red-800",
      clearButtonIcon: "text-red-400",
      errorText: "text-gray-300",
    }
  };

  if (loading) {
    return <NotificationLoadingSkeleton theme={theme} />;
  }
  if (error) return <div className={themeClasses[theme].errorText}>{en.Notification_FailedToLoadNotifications}</div>;

  const groupNotificationsByDate = () => {
    const groupedNotifications: { [key: string]: any[] } = {};
    notifications.forEach((item: any) => {
      const date = parseISO(item.created_at);
      const key = isToday(date)
        ? "Today"
        : isYesterday(date)
          ? "Yesterday"
          : "Older";
      if (!groupedNotifications[key]) {
        groupedNotifications[key] = [];
      }
      groupedNotifications[key].push(item);
    });
    return groupedNotifications;
  };

  const groupedNotifications = groupNotificationsByDate();

  return (
    <div className={`notification-container ${themeClasses[theme].container}`}>
      <div className={`notification-scroll-area custom-scrollbar ${themeClasses[theme].scrollArea}`}>
        {notifications.length === 0 ? (
          <EmptyStateView type="notifications" onClick={() => { }} theme={theme} />
        ) : (
          <>
            {Object.entries(groupedNotifications).map(([date, items]) => (
              <NotificationGroup
                key={date}
                date={date}
                items={items}
                notificationTooltips={notificationTooltips}
                markRead={markRead}
                handleValueChange={handleValueChange}
                handleDrawerToggle={handleDrawerToggle}
                openDeleteModal={openDeleteModal}
                onNotificationClick={handleNotificationClick}
                theme={theme}
              />
            ))}
          </>
        )}
      </div>
      <div className={`notification-footer ${themeClasses[theme].footer}`}>
        {notifications.length > 0 && (
          <span
            className={`mark-all-read-button ${themeClasses[theme].markAllButton}`}
            onClick={() => markAllRead(handleValueChange)}
          >
            Mark all as read
          </span>
        )}
        {isDeleteModalOpen && (
          <DeleteProjectModal
            isOpen={isDeleteModalOpen}
            onClose={() => setIsDeleteModalOpen(false)}
            onDelete={handleDelete}
            isDeleting={isDeleting}
            type="notification"
          />
        )}
      </div>
      {notifications.length > 0 && (
        <>
          <UiButton
            className={`clear-all-button group ${themeClasses[theme].clearButton}`}
            iconStyle={`clear-all-icon ${themeClasses[theme].clearButtonIcon}`}
            onClick={handleClearAll}
            icon={X}
            variant="danger"
            tooltip="Clear all notifications"
            ariaLabel="Clear all notifications"
          />
        </>
      )}
    </div>
  );
};

export default NotificationList;
