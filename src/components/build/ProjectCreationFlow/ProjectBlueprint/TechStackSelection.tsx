import React from 'react';
import { Code, Server, Laptop } from 'lucide-react';

/**
 * TO SUPPORT BOTH FRONTEND AND BACKEND SIMULTANEOUSLY:
 * 
 * 1. Remove or modify the warning message at the bottom to support both
 *    selections being active at once. For example, only show the warning
 *    when BOTH are set to "None".
 * 
 * 2. Adjust the visual styling (border colors, background colors) to properly
 *    reflect that both frontend and backend can be selected at the same time.
 * 
 * 3. Consider adding an informational UI element to show the user that both
 *    selections are allowed and explain how they will work together.
 */

interface TechStackSelectionProps {
  techStack?: {
    frontend?: string[];
    backend?: string[];
    language?: string[];
  };
  frontendOptions: string[];
  backendOptions: string[];
  languageOptions: string[];
  onChange: (category: string, value: string) => void;
  isDarkMode?: boolean;
}

const TechStackSelection: React.FC<TechStackSelectionProps> = ({
  techStack = { frontend: ["None"], backend: ["None"], language: ["JavaScript (ES6+)"] },
  frontendOptions,
  backendOptions,
  languageOptions,
  onChange,
  isDarkMode = false // This prop is now ignored, always using light mode
}) => {
  // Extract values safely with defaults if undefined
  const frontendValue = techStack?.frontend?.[0] || "None";
  const backendValue = techStack?.backend?.[0] || "None";
  const languageValue = techStack?.language?.[0] || languageOptions[0];

  return (
    <div className="mb-6">
      <h3 className="typography-body-lg font-weight-medium mb-4 text-gray-800">Tech Stack</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Frontend Selection */}
        <div 
          className={`border rounded-lg p-4 transition-colors duration-200 ${
            frontendValue === "None" 
              ? "border-gray-200 bg-gray-50"
              : "border-[#f26a1b]/20 bg-orange-50"
          }`}
        >
          <div className="flex items-center mb-3">
            <Laptop 
              size={18} 
              className={`mr-2 transition-colors duration-200 ${
                frontendValue === "None" 
                  ? "text-gray-400"
                  : "text-[#f26a1b]"
              }`} 
            />
            <h4 
              className={`font-weight-medium transition-colors duration-200 ${
                frontendValue === "None" 
                  ? "text-gray-500"
                  : "text-gray-800"
              }`}
            >
              Frontend
            </h4>
          </div>
          <select
            value={frontendValue}
            onChange={(e) => onChange('frontend', e.target.value)}
            className={`w-full p-2 border rounded-md focus:ring-1 focus:ring-[#f26a1b] transition-colors duration-200 ${
              frontendValue === "None" 
                ? "border-gray-300 bg-gray-100 text-gray-500"
                : "border-[#f26a1b]/20 bg-white text-gray-800"
            }`}
          >
            {frontendOptions.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
        </div>
        
        {/* Backend Selection */}
        <div 
          className={`border rounded-lg p-4 transition-colors duration-200 ${
            backendValue === "None" 
              ? "border-gray-200 bg-gray-50"
              : "border-[#f26a1b]/20 bg-orange-50"
          }`}
        >
          <div className="flex items-center mb-3">
            <Server 
              size={18} 
              className={`mr-2 transition-colors duration-200 ${
                backendValue === "None" 
                  ? "text-gray-400"
                  : "text-[#f26a1b]"
              }`} 
            />
            <h4 
              className={`font-weight-medium transition-colors duration-200 ${
                backendValue === "None" 
                  ? "text-gray-500"
                  : "text-gray-800"
              }`}
            >
              Backend
            </h4>
          </div>
          <select
            value={backendValue}
            onChange={(e) => onChange('backend', e.target.value)}
            className={`w-full p-2 border rounded-md focus:ring-1 focus:ring-[#f26a1b] transition-colors duration-200 ${
              backendValue === "None" 
                ? "border-gray-300 bg-gray-100 text-gray-500"
                : "border-[#f26a1b]/20 bg-white text-gray-800"
            }`}
          >
            {backendOptions.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
        </div>
        
        {/* Language Selection */}
        <div className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-center mb-3">
            <Code size={18} className="mr-2 text-[#f26a1b]" />
            <h4 className="font-weight-medium text-gray-800">Language</h4>
          </div>
          <select
            value={languageValue}
            onChange={(e) => onChange('language', e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-[#f26a1b] bg-white text-gray-800"
          >
            {languageOptions.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
        </div>
      </div>
      
      {/* Warning shown only when both frontend and backend are None */}
      {frontendValue === "None" && backendValue === "None" && (
        <div className="mt-3 px-4 py-2 rounded-md typography-body-sm inline-block bg-yellow-50 border border-yellow-200 text-yellow-700">
          Please select at least one framework type
        </div>
      )}
    </div>
  );
};

export default TechStackSelection; 