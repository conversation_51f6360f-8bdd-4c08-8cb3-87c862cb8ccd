/**
 * Utility function to handle tech stack updates without UI flickering
 * 
 * This function creates a complete tech stack update that maintains exclusivity
 * between frontend and backend frameworks (when one is selected, the other is set to "None")
 * 
 * @param currentTechStack The current tech stack state
 * @param category The category being updated ('frontend', 'backend', 'language')
 * @param value The new value for the category
 * @returns A complete updated tech stack object
 */
export function createTechStackUpdate(
  currentTechStack: { 
    frontend: string[], 
    backend: string[], 
    language: string[] 
  },
  category: 'frontend' | 'backend' | 'language',
  value: string
): { frontend: string[], backend: string[], language: string[] } {
  // Create a complete copy of the current tech stack
  const newTechStack = {
    ...currentTechStack
  };
  
  // Type-safe property assignment
  if (category === 'frontend') {
    newTechStack.frontend = [value];
    
    // When selecting a frontend, set backend to None
    if (value !== "None") {
      newTechStack.backend = ["None"];
    }
  } 
  else if (category === 'backend') {
    newTechStack.backend = [value];
    
    // When selecting a backend, set frontend to None
    if (value !== "None") {
      newTechStack.frontend = ["None"];
    }
  }
  else if (category === 'language') {
    newTechStack.language = [value];
  }
  
  return newTechStack;
}

/**
 * TO SUPPORT BOTH FRONTEND AND BACKEND SIMULTANEOUSLY:
 * 
 * Remove the mutual exclusivity logic by modifying the above function as follows:
 * 
 * export function createTechStackUpdate(
 *   currentTechStack: { 
 *     frontend: string[], 
 *     backend: string[], 
 *     language: string[] 
 *   },
 *   category: 'frontend' | 'backend' | 'language',
 *   value: string
 * ): { frontend: string[], backend: string[], language: string[] } {
 *   // Create a complete copy of the current tech stack
 *   const newTechStack = {
 *     ...currentTechStack
 *   };
 *   
 *   // Type-safe property assignment without mutual exclusivity
 *   if (category === 'frontend') {
 *     newTechStack.frontend = [value];
 *     // No longer forces backend to "None"
 *   } 
 *   else if (category === 'backend') {
 *     newTechStack.backend = [value];
 *     // No longer forces frontend to "None"
 *   }
 *   else if (category === 'language') {
 *     newTechStack.language = [value];
 *   }
 *   
 *   return newTechStack;
 * }
 */

/**
 * Updates the blueprint state with a new tech stack in a single operation
 * to avoid flickering from multiple state updates
 */
export function updateBlueprintWithTechStack<T extends { techStack: { frontend: string[], backend: string[], language: string[] } }>(
  setBlueprint: React.Dispatch<React.SetStateAction<T>>,
  category: 'frontend' | 'backend' | 'language',
  value: string
): void {
  setBlueprint(prevBlueprint => {
    const newTechStack = createTechStackUpdate(
      prevBlueprint.techStack,
      category,
      value
    );
    
    return {
      ...prevBlueprint,
      techStack: newTechStack
    } as T;
  });
}
