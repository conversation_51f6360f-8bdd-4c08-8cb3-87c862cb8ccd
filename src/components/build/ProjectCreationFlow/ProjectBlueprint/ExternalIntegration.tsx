import React, { useEffect, useState } from 'react';
import { <PERSON>2, Chevron<PERSON><PERSON><PERSON>, ChevronRight } from 'lucide-react';
import StripeConnectionModal from './StripeConnectionModal';
import SupabaseConnectionModal from './SupabaseConnectionModal';
import { connectToSupabase } from '@/utils/api';

interface Integration {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  status: 'connected' | 'not_connected';
  color: {
    bg?: string;
    text: string;
    border: string;
    lightBg?: string;
  };
}

// Supabase Logo Component
const SupabaseLogo = () => (
  <svg width="24" height="24" viewBox="0 0 109 113" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0627L99.1935 40.0627C107.384 40.0627 111.952 49.5228 106.859 55.9374L63.7076 110.284Z" fill="url(#paint0_linear_supabase)" />
    <path d="M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0627L99.1935 40.0627C107.384 40.0627 111.952 49.5228 106.859 55.9374L63.7076 110.284Z" fill="url(#paint1_linear_supabase)" fillOpacity="0.2" />
    <path d="M45.317 2.07103C48.1765 -1.53037 53.9745 0.442937 54.0434 5.041L54.4849 72.2922H9.83113C1.64038 72.2922 -2.92775 62.8321 2.1655 56.4175L45.317 2.07103Z" fill="#3ECF8E" />
    <defs>
      <linearGradient id="paint0_linear_supabase" x1="53.9738" y1="54.974" x2="94.1635" y2="71.8295" gradientUnits="userSpaceOnUse">
        <stop stopColor="#249361" />
        <stop offset="1" stopColor="#3ECF8E" />
      </linearGradient>
      <linearGradient id="paint1_linear_supabase" x1="36.1558" y1="30.578" x2="54.4844" y2="65.0806" gradientUnits="userSpaceOnUse">
        <stop stopColor="white" />
        <stop offset="1" stopColor="white" stopOpacity="0" />
      </linearGradient>
    </defs>
  </svg>
);

// Stripe Logo Component
const StripeLogo = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M13.976 9.15c-2.172-.806-3.356-1.426-3.356-2.409 0-.831.683-1.305 1.901-1.305 2.227 0 4.515.858 6.09 1.631l.89-5.494C18.252.274 15.697 0 12.165 0 9.667 0 7.589.654 6.104 1.872 4.56 3.147 3.757 4.992 3.757 7.218c0 4.039 2.467 5.76 6.476 7.219 2.585.92 3.445 1.574 3.445 2.583 0 .98-.84 1.545-2.354 1.545-1.875 0-4.965-.921-6.99-2.109l-.9 5.555C5.175 22.99 8.385 24 11.714 24c2.641 0 4.843-.624 6.328-1.813 1.664-1.305 2.525-3.236 2.525-5.732 0-4.128-2.524-5.851-6.591-7.305z" fill="white" />
  </svg>
);

const ExternalIntegrations: React.FC = () => {
  const [stripeModalOpen, setStripeModalOpen] = useState(false);
  const [supabaseModalOpen, setSupabaseModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [integrationStatus, setIntegrationStatus] = useState<Record<string, 'connected' | 'not_connected'>>({
    supabase: 'not_connected',
    stripe: 'not_connected'
  });


   const checkSupabaseConnection = async () => {
    try {
      const projectId = sessionStorage.getItem('generated_project_id');
      if (!projectId) return;
      
      const response = await connectToSupabase(projectId.toString());
      if (response && response.status === "already_connected") {
        setIntegrationStatus(prev => ({
          ...prev,
          supabase: 'connected'
        }));
      }
    } catch (error) {
      console.error("Error checking supabase connection:", error);
    }
  };



  const integrations: Integration[] = [
    {
      id: 'supabase',
      name: 'Supabase',
      description: 'Database & Authentication',
      icon: <SupabaseLogo />,
      status: integrationStatus.supabase,
      color: {
        // bg: 'bg-gray-500',
        text: 'text-green-500',
        border: 'border-green-500',
        lightBg: 'bg-green-50'
      }
    },
    {
      id: 'stripe',
      name: 'Stripe',
      description: 'Payments & Subscriptions',
      icon: <StripeLogo />,
      status: integrationStatus.stripe,
      color: {
        bg: 'bg-purple-600',
        text: 'text-purple-600',
        border: 'border-purple-600',
        lightBg: 'bg-purple-50'
      }
    }
  ];

  // Pagination settings
  const itemsPerPage = 2;
  const totalPages = Math.ceil(integrations.length / itemsPerPage);
  const showCarouselControls = integrations.length > itemsPerPage;

  // Get current page items
  const startIndex = currentPage * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentIntegrations = integrations.slice(startIndex, endIndex);

  const handleConnect = (integrationId: string) => {
    if (integrationId === 'stripe') {
      setStripeModalOpen(true);
    } else if (integrationId === 'supabase') {
      setSupabaseModalOpen(true);
    }
  };

  const handleDisconnect = (integrationId: string) => {
    setIntegrationStatus(prev => ({
      ...prev,
      [integrationId]: 'not_connected'
    }));
  };

  const handleStripeConnectionComplete = () => {
    setIntegrationStatus(prev => ({
      ...prev,
      stripe: 'connected'
    }));
  };

  const handleSupabaseConnectionComplete = () => {
    setIntegrationStatus(prev => ({
      ...prev,
      supabase: 'connected'
    }));
     setSupabaseModalOpen(false);
  };

  const goToPreviousPage = () => {
    setCurrentPage(prev => Math.max(0, prev - 1));
  };

  const goToNextPage = () => {
    setCurrentPage(prev => Math.min(totalPages - 1, prev + 1));
  };

  const handleSupabaseModalClose = () => {
    setSupabaseModalOpen(false);
    setTimeout(() => {
      checkSupabaseConnection();
    }, 100);
  };


  useEffect(() => {

    checkSupabaseConnection();
}, []);

  return (
    <>
      <div className="mb-6">
        <h3 className="typography-body-lg font-weight-medium mb-4 text-gray-800">
          Third party Integrations
        </h3>

        <div className="relative">
          {/* Carousel Container */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {currentIntegrations.map((integration) => {
              const isConnected = integration.status === 'connected';

              return (
                <div
                  key={integration.id}
                  className={`border rounded-lg p-4 bg-white ${isConnected ? `border-${integration.color.text} ${integration.color.lightBg}` : 'border-gray-200'
                    }`}
                >
                  <div className="flex items-start space-x-3">
                    {/* Icon */}
                    <div
                      className={`w-10 h-10 rounded-full ${integration.color.bg} flex items-center justify-center flex-shrink-0`}
                    >
                      {integration.icon}
                    </div>

                    {/* Content */}
                    <div className="flex-1">
                      <h4 className="typography-body-sm font-weight-medium text-gray-900">
                        {integration.name}
                      </h4>
                      <p className="typography-caption text-gray-500 mt-0.5">
                        {integration.description}
                      </p>

                      {/* Status */}
                      <div className="flex items-center mt-2">
                        <span className={`w-2 h-2 rounded-full mr-2 ${isConnected ? 'bg-green-500' : 'bg-gray-300'
                          }`}></span>
                        <span className={`typography-caption ${isConnected ? 'text-green-600' : 'text-gray-500'
                          }`}>
                          {isConnected ? 'Connected' : 'Not Connected'}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Connect/Disconnect Button */}
                  {isConnected ? (
                    <button
                      onClick={() => handleDisconnect(integration.id)}
                      className="mt-4 w-full px-4 py-2 border border-gray-300 text-gray-600 bg-white rounded-md hover:bg-gray-50 flex items-center justify-center transition-colors duration-200"
                    >
                      <span className="typography-body-sm font-weight-medium">
                        Disconnect
                      </span>
                    </button>
                  ) : (
                    <button
                      onClick={() => handleConnect(integration.id)}
                      className={`mt-4 w-full px-4 py-2 border ${integration.color.border
                        } ${integration.color.text} bg-white rounded-md hover:bg-gray-50 flex items-center justify-center transition-colors duration-200`}
                    >
                      <Link2 size={16} className="mr-2" />
                      <span className="typography-body-sm font-weight-medium">
                        Connect
                      </span>
                    </button>
                  )}
                </div>
              );
            })}
          </div>

          {/* Floating Arrow Controls */}
          {showCarouselControls && (
            <>
              {/* Left Arrow */}
              <button
                onClick={goToPreviousPage}
                disabled={currentPage === 0}
                className={`absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 bg-white rounded-full shadow-lg p-2 transition-all duration-200 ${currentPage === 0
                  ? 'opacity-50 cursor-not-allowed'
                  : 'hover:shadow-xl hover:scale-110 cursor-pointer'
                  }`}
                aria-label="Previous page"
              >
                <ChevronLeft size={20} className="text-gray-600" />
              </button>

              {/* Right Arrow */}
              <button
                onClick={goToNextPage}
                disabled={currentPage === totalPages - 1}
                className={`absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 bg-white rounded-full shadow-lg p-2 transition-all duration-200 ${currentPage === totalPages - 1
                  ? 'opacity-50 cursor-not-allowed'
                  : 'hover:shadow-xl hover:scale-110 cursor-pointer'
                  }`}
                aria-label="Next page"
              >
                <ChevronRight size={20} className="text-gray-600" />
              </button>
            </>
          )}
        </div>

        {/* Dot Indicators */}
        {showCarouselControls && (
          <div className="flex justify-center items-center space-x-2 mt-4">
            {Array.from({ length: totalPages }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentPage(index)}
                className={`w-2 h-2 rounded-full transition-all duration-200 ${index === currentPage
                  ? 'bg-gray-800 w-6'
                  : 'bg-gray-300 hover:bg-gray-400'
                  }`}
                aria-label={`Go to page ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>

      {/* Stripe Connection Modal */}
      <StripeConnectionModal
        isOpen={stripeModalOpen}
        onClose={() => setStripeModalOpen(false)}
        onConnectionComplete={handleStripeConnectionComplete}
      />

      {/* Supabase Connection Modal */}
      <SupabaseConnectionModal
        isOpen={supabaseModalOpen}
        onClose={handleSupabaseModalClose}
        onConnectionComplete={handleSupabaseConnectionComplete}
      />
    </>
  );
};

export default ExternalIntegrations;