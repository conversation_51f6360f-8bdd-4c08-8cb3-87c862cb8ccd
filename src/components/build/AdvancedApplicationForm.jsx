import { useState, useRef } from 'react';
import { Upload } from 'lucide-react';

const AdvancedApplicationForm = ({ handleProjectSubmit, submitting, isLight }) => {
  const [formData, setFormData] = useState({
    projectName: '',
    projectGoal: '',
    files: []
  });

  const fileInputRef = useRef(null);

  const handleChange = (e) => {
    const { name, value, files } = e.target;

    if (files) {
      setFormData(prevState => ({
        ...prevState,
        files: Array.from(files)
      }));
    } else {
      setFormData(prevState => ({
        ...prevState,
        [name]: value
      }));
    }
  };

  const handleUploadClick = () => {
    if (submitting) return;
    fileInputRef.current.click();
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    handleProjectSubmit(formData);
  };

  return (
    <div className={`flex flex-col bg-none ${isLight ? "text-gray-800" : "text-gray-200"} mt-8`}>
      <form onSubmit={handleSubmit} className="flex flex-col flex-1">
        <div className="h-full">
          <div className={`mb-4 ${submitting ? 'opacity-75 cursor-not-allowed' : ''}`}>
            <label className={`block mb-2 typography-body-sm font-weight-light ${isLight ? "text-gray-700" : "text-gray-300"}`}>
              Project Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              name="projectName"
              value={formData.projectName}
              onChange={handleChange}
              placeholder="e.g. Movie App"
              className={`w-full p-3 ${isLight ? "bg-white border-gray-200 text-gray-800 placeholder-gray-400" : "bg-white/5 border-zinc-700 text-gray-300 placeholder-gray-500"} border rounded typography-body-sm focus:outline-none focus:ring-1 focus:ring-orange-500`}
              disabled={submitting}
            />
          </div>

          <div className={`mb-4 ${submitting ? 'opacity-75 cursor-not-allowed' : ''}`}>
            <label className={`block mb-2 typography-body-sm font-weight-light ${isLight ? "text-gray-700" : "text-gray-300"}`}>
              Project Goal <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              name="projectGoal"
              value={formData.projectGoal}
              onChange={handleChange}
              placeholder="e.g. Create Banking Application"
              className={`w-full p-3 ${isLight ? "bg-white border-gray-200 text-gray-800 placeholder-gray-400" : "bg-white/5 border-zinc-700 text-gray-300 placeholder-gray-500"} border rounded typography-body-sm focus:outline-none focus:ring-1 focus:ring-orange-500`}
              disabled={submitting}
            />
          </div>

          <div className={`mb-4 ${submitting ? 'opacity-75 cursor-not-allowed' : ''}`}>
            <p className={`block mb-2 typography-body-sm font-weight-light ${isLight ? "text-gray-700" : "text-gray-300"}`}>Document Upload</p>
            <div
              className={`border border-dashed ${isLight ? "border-gray-200" : "border-zinc-700"} rounded p-4 text-center cursor-pointer ${isLight ? "hover:bg-gray-50" : "hover:bg-white/5"} transition-colors`}
              onClick={handleUploadClick}
            >
              <div className="flex flex-col items-center justify-center">
                <Upload className={`w-6 h-6 ${isLight ? "text-gray-500" : "text-gray-400"} mb-2`} />
                <p className={`typography-body-sm ${isLight ? "text-gray-500" : "text-gray-400"}`}>
                  {formData.files.length > 0
                    ? `${formData.files.length} file(s) selected`
                    : "Upload projects Assets"}
                </p>
              </div>
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleChange}
                className="hidden"
                disabled={submitting}
                multiple
              />
            </div>
          </div>
        </div>

        <button
          type="submit"
          className={`mt-4 absolute bottom-4 left-4 right-4 ${isLight ? "bg-orange-500 hover:bg-orange-600" : "bg-orange-500 hover:bg-orange-600"} text-white py-3 px-4 rounded font-weight-medium transition-colors ${submitting ? 'animate-pulse' : ''}`}
        >
          {submitting ? "Building project..." : "Start Building"}
        </button>
      </form>
    </div>
  );
};

export default AdvancedApplicationForm; 