import React from 'react';
import Image from 'next/image';
import { Generic } from '@/constants/code_gen/platforms';
import { BootstrapTooltip } from '@/components/UIComponents/ToolTip/Tooltip-material-ui';
import { buildOptions } from './BuildOptionsButtons';

const StackOptions = ({ frameworks, activeFramework, setActiveFramework, isStreaming, isLight, buildOption }) => {

  const currentBuildOption = buildOptions[buildOption] ? buildOption : 0;
  const selectedBuildType = buildOptions[currentBuildOption].id;
  // Filter frameworks based on build option
  const filteredFrameworks = frameworks.filter(framework => {
    if (Array.isArray(framework.type)) {
      return framework.type.includes(selectedBuildType);
    }
    return framework.type === selectedBuildType;
  });

  // Find the index of the default framework in the filtered list
  const defaultFrameworkIndex = filteredFrameworks.findIndex(f => f.isDefault);

  // Get the currently selected framework
  const selectedFramework = frameworks[activeFramework];
  const isSelectedFrameworkValid = selectedFramework &&
    (Array.isArray(selectedFramework.type)
      ? selectedFramework.type.includes(selectedBuildType)
      : selectedFramework.type === selectedBuildType);

  // When build option changes, select the default framework for that type
  React.useEffect(() => {
    // Only set default if current selection is not valid for the new type
    if (!isSelectedFrameworkValid && defaultFrameworkIndex !== -1) {
      const originalIndex = frameworks.findIndex(f => f.key === filteredFrameworks[defaultFrameworkIndex].key);
      setActiveFramework(originalIndex);
    }
  }, [buildOption, defaultFrameworkIndex, frameworks, filteredFrameworks, setActiveFramework, isSelectedFrameworkValid]);

  // Handle framework selection
  const handleFrameworkSelect = (index) => {
    if (isStreaming) return;
    setActiveFramework(index);
  };

  return (
    <div className="text-center mt-8 group">
      {selectedBuildType != 'generic' && <p className={`${isLight ? "text-gray-500" : "text-gray-500"} italic typography-body-sm mb-4`}>
        Select the framework to start building
      </p>}

      <div className="flex justify-center relative">
        <div className="flex -space-x-1 group-hover:space-x-1 transition-all duration-300">
          {frameworks.map((framework, index) => {
            // Skip rendering if framework type doesn't match the build option
            const isTypeMatch = Array.isArray(framework.type)
              ? framework.type.includes(selectedBuildType)
              : framework.type === selectedBuildType;

            if (!isTypeMatch) {
              return null;
            }

            const isSelected = activeFramework === index;
            const logoColor = isLight ? "#1F2937" : "#DAD9D9";

            return (
              <BootstrapTooltip
                key={index}
                title={framework.label}
                placement="top"
              >
                <div
                  className={`${isStreaming ? 'cursor-not-allowed' : 'cursor-pointer'}
                    ${isStreaming && "opacity-75"}
                    w-12 h-12 shadow-sm
                    ${isSelected ? "border-primary-500" : `${isLight ? "border-gray-300" : "border-gray-700"}`}
                    ${isLight ? "hover:border-primary-400" : "hover:border-primary-800"}
                    ${isLight ? "bg-gray-100 backdrop-blur-sm" : "bg-[#231F20]"}
                    rounded-lg flex items-center justify-center border-[1px]
                    transition-all duration-300
                    ${isSelected ? "scale-125 z-20" : "hover:z-10 hover:scale-110"}
                    ${isSelected ? "group-hover:translate-x-0" : "group-hover:translate-x-0"}`}
                  onClick={() => handleFrameworkSelect(index)}
                >
                  <div className={`p-1 rounded-md ${isLight ? "bg-gray-800/10" : ""}`}>
                    {framework.icon ? (
                      <div
                        className={`size-6 ${isSelected ? "opacity-100" : "opacity-50"}`}
                        style={{
                          filter: `${isLight ? "invert(0) brightness(0)" : ""}`,
                          color: logoColor,
                          fill: logoColor
                        }}
                      >
                        <Image
                          src={framework.icon}
                          alt={framework.label}
                          className="size-6"
                          style={{
                            filter: `brightness(0) ${isLight ? "invert(0)" : "invert(1)"}`,
                            fill: logoColor
                          }}
                        />
                      </div>
                    ) : (
                      <Generic color={logoColor}
                        className={`${isSelected ? "opacity-100" : "opacity-50 hover:opacity-75"}`}
                      />
                    )}
                  </div>
                </div>
              </BootstrapTooltip>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default StackOptions;