import { PLATFORMS } from '../../constants/code_gen/platforms';

// Map PLATFORMS to the format expected by the component
const buildOptions = PLATFORMS.map(platform => ({
  id: platform.key === 'web' ? 'web' :
      platform.key === 'mobile' ? 'mobile' :
      platform.key === 'backend' ? 'backend' :
      platform.key === 'generic' ? 'generic' : platform.key,
  label: platform.label,
  isDefault: platform.key === 'web',
  icon: platform.icon
}));

const BuildOptionsButtons = ({ disabled, buildOption, setBuildOption, isLight }) => {

  const currentBuildOption = typeof buildOption === 'number' ? buildOption : 0;

  return (
    <div className="w-full mt-8 flex gap-2 justify-center items-center">
      {buildOptions.map((option, index) => (
        <div
          key={option.id}
          className={`${disabled ? 'opacity-75 cursor-not-allowed' : 'cursor-pointer'}
          flex-1 relative rounded-lg
          ${currentBuildOption === index
              ? `border border-[#F26A1B] bg-gradient-to-r from-[#F26A1B]/15 via-[#F26A1B]/8 to-[#F26A1B]/5 backdrop-blur-[17px]`
              : `border ${isLight ? "hover:border-orange-600 border-gray-200 hover:bg-gradient-to-r hover:from-amber-100/60 hover:from-10% hover:via-amber-100/40 hover:via-30% hover:to-amber-100/40 hover:to-90%" : "hover:border-orange-700 border-transparent hover:bg-gradient-to-r hover:from-amber-900/40 hover:from-10% hover:via-amber-700/10 hover:via-30% hover:to-amber-700/10 hover:to-90%"}`
            }`}
          onClick={() => { !disabled && setBuildOption(index) }}
        >
          <div className={`${currentBuildOption === index
              ? `${isLight ? "bg-white/70" : "bg-white/10"} backdrop-blur-sm rounded-lg py-1.5 px-2 w-full flex flex-row justify-center items-center transition duration-300`
              : `${isLight ? "bg-white/70" : "bg-white/10"} backdrop-blur-sm rounded-lg py-1.5 px-2 w-full flex flex-row justify-center items-center transition duration-300`
            }`}>
            <span className="mr-1">
              {option.icon}
            </span>
            <p className={`${currentBuildOption === index ? (isLight ? "text-black typography-body-sm font-weight-normal leading-[21px]" : "text-white typography-body-sm font-weight-normal leading-[21px]") : `text-md whitespace-nowrap ${currentBuildOption === index ? (isLight ? "text-gray-800" : "text-gray-200") : (isLight ? "text-gray-600" : "text-gray-400")}`}`}>
              {option.label}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
};

export { buildOptions };
export default BuildOptionsButtons;