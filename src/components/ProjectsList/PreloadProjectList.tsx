"use client";
import React, { useState, useEffect, useContext } from 'react';
import ProjectList from './ProjectList';
import { fetchProjectsList, fetchNodeById } from '../../utils/api';
import { fetchPublicProjects } from '../../utils/projectApi';
import { SideBarContext } from '../Context/SideBarContext';
import { TopBarContext } from '../Context/TopBarContext';

import { useRouter, usePathname } from 'next/navigation';
import {ProjectListSkeleton} from "@/components/UIComponents/Loaders/LoaderGroup"
import CreateProjectModal from '../Modal/CreateProjectModal';
import ErrorView from '../Modal/ErrorViewModal';
import Cookies from 'js-cookie';
import { createPortal } from 'react-dom';

import en from "../../en.json";

import '@/styles/chatlist.css';
import { useUser } from '../Context/UserContext';

const getProjectIdFromPath = (pathname: any) => {
  const pathParts = pathname.split('/');
  return pathParts.length > 2 ? pathParts[2] : null;
};

interface PreloadProjectListProps {
  handleDrawerToggle: () => void;
  theme?: 'light' | 'dark';
}

const PreloadProjectList: React.FC<PreloadProjectListProps> = ({ 
  handleDrawerToggle, 
  theme = 'light' 
}) => {
  const { prevProjects, setPrevProjects, closeSidebar } = useContext(SideBarContext);
  const { tabs, addTab, setActiveTab: setTopBarActiveTab, updateTabTitle, updateProjectInfo } = useContext(TopBarContext);

  
  const [projects, setProjects] = useState(prevProjects);
  const [publicProjects, setPublicProjects] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState<'private' | 'public'>('private');
  const [isLoading, setIsLoading] = useState(true);
  const [isPublicLoading, setIsPublicLoading] = useState(true);
  const [error, setError] = useState(null);
  const pathname = usePathname();
  const router = useRouter();
  const [currentProjectId, setCurrentProjectId] = useState(getProjectIdFromPath(pathname));
  const [isProjectModalOpen, setIsProjectModalOpen] = useState(false);
  const { is_public_project_selected } = useUser();
  
  const fetchPrivateProjects = async () => {
    setError(null);
    setIsLoading(true);
    try {
      const data = await fetchProjectsList();
      setProjects(data);
      setPrevProjects(data);
    } catch (err: any) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Function to refresh projects after clone
  const refreshProjectsList = () => {
    if (activeTab === 'private') {
      fetchPrivateProjects();
    } else {
      fetchAllPublicProjects();
    }
  };

  const fetchAllPublicProjects = async () => {
    setError(null);
    setIsPublicLoading(true);
    try {
      const data = await fetchPublicProjects();
      // Transform public projects to match the format expected by ProjectList
      if (data && typeof data === 'object' && 'public_projects' in data && Array.isArray(data.public_projects)) {
        const formattedPublicProjects = data.public_projects.map((project: any) => ({
          id: project.project_id || project.id,
          Title: project.title || project.Title,
          Description: project.description,
          CreatedAt: project.created_at,
          CreatedBy: project.created_by,
          creator_name: project.creator_name || 'Unknown',
          creator_email: project.creator_email,
          creator_picture: project.creator_picture,
          created_at: project.created_at,
          tenant_id: project.tenant_id,
          isPublic: true,
        }));
        
        // Sort by creation date in descending order (newest first)
        const sortedPublicProjects = formattedPublicProjects.sort((a, b) => {
          const dateA = new Date(a.created_at || a.CreatedAt).getTime();
          const dateB = new Date(b.created_at || b.CreatedAt).getTime();
          return dateB - dateA; // descending order
        });
        
        setPublicProjects(sortedPublicProjects);
        
      } else {
        setPublicProjects([]);
      }
    } catch (err: any) {
      setError(err);
    } finally {
      setIsPublicLoading(false);
    }
  };

  useEffect(() => {
    if (activeTab === 'private') {
      fetchPrivateProjects();
    } else {
      fetchAllPublicProjects();
    }
  }, [activeTab, setPrevProjects]);

  useEffect(() => {
    const newProjectId = getProjectIdFromPath(pathname);
    if (newProjectId !== currentProjectId) {
      setCurrentProjectId(newProjectId);
    }
  }, [pathname, currentProjectId]);

  const handleProjectClick = async (project: any) => {
    // Debug log to check project data
    
    
    
    
    
    // Store project information
    const projectInfo = {
      selected_project_id: project.id.toString(),
      selected_project_creator_email: project.creator_email || '',
      is_public_selected: is_public_project_selected(project.creator_email),
      selected_tenant_id: (activeTab === 'public' || project.isPublic) ? project.tenant_id : Cookies.get('tenant_id') || ''
    };
    
    // Keep cookies for backward compatibility
    Cookies.set('selected_project_id', projectInfo.selected_project_id);
    Cookies.set('selected_project_creator_email', projectInfo.selected_project_creator_email);
    Cookies.set('is_public_selected', projectInfo.is_public_selected);
    Cookies.set('selected_tenant_id', projectInfo.selected_tenant_id);
    
    const projectRes = await fetchNodeById(project.id, "Project");
    let title = projectRes.properties?.Title || projectRes.properties?.Name || "Project"

    const projectUrl = `/project/${project.id}/overview`;

    const existingTab = tabs.find((tab: any) => {
      const tabProjectId = tab.href.split('/')[2]; // Extract project ID from href
      return tabProjectId === project.id.toString(); // Compare with current project ID
    });

    if (existingTab) {
      if (existingTab.href !== projectUrl) {
        updateTabTitle(project.id, title, projectInfo);
        setTopBarActiveTab(existingTab.id);
        router.push(existingTab.href);
      }
      else {
        // Update project info even if the href is the same
        updateProjectInfo(project.id, projectInfo);
        setTopBarActiveTab(existingTab.id);
        router.push(projectUrl);
      }
    }
    else {
      addTab(title, projectUrl, projectInfo);
      router.push(projectUrl);
    }
    closeSidebar();
    handleDrawerToggle();
  };

  const createNewProjectAndCloseSidebar = () => {
    setIsProjectModalOpen(true);
    handleDrawerToggle();
  };

  const openProjectModal = () => {
    setIsProjectModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsProjectModalOpen(false);
  };
  
  const handleUpdateResponse = (response: any) => {
    // Create project info object
    const projectInfo = {
      selected_project_id: response.id.toString(),
      selected_project_creator_email: '',
      is_public_selected: 'false', // New projects are private by default
      selected_tenant_id: Cookies.get('tenant_id') || ''
    };
    
    // Store in cookies for backward compatibility
    Cookies.set('selected_project_id', projectInfo.selected_project_id);
    Cookies.set('is_public_selected', projectInfo.is_public_selected);
    Cookies.set('selected_tenant_id', projectInfo.selected_tenant_id);
    
    // Update projects state
    setProjects([...projects, response]);
    setPrevProjects([...projects, response]);
    setIsProjectModalOpen(false); // Close the modal
    closeSidebar(); // Close the sidebar
    handleDrawerToggle(); // Toggle the drawer state

    // Navigate to the new project
    const newProjectUrl = `/project/${response.id}/overview`;
    const projectName = response.properties.Title || response.properties.Name;
    
    const existingTab = tabs.find((tab: any) => {
      const tabProjectId = tab.href.split('/')[2]; // Extract project ID from href
      return tabProjectId === response.id.toString(); // Compare with current project ID
    });
    
    if (existingTab) {
      if (existingTab.href !== newProjectUrl) {
        updateTabTitle(response.id, projectName, projectInfo);
        setTopBarActiveTab(existingTab.id);
        router.push(existingTab.href);
      }
      else {
        updateProjectInfo(response.id, projectInfo);
        setTopBarActiveTab(existingTab.id);
        router.push(newProjectUrl);
      }
    }
    else {
      addTab(projectName, newProjectUrl, projectInfo);
      router.push(newProjectUrl);
    }
  };

  const handleTabChange = (tab: 'private' | 'public') => {
    setActiveTab(tab);
  };

  // Theme classes
  const themeClasses = {
    light: {
      container: "bg-white text-gray-900",
      tabContainer: "project-tabs sticky top-0 z-10 bg-white border-b border-gray-200",
      tab: "project-tab",
      activeTab: "project-tab active",
      contentWrapper: "w-full h-full flex-grow bg-white",
      errorContainer: "preload-chat-list bg-white"
    },
    dark: {
      container: "bg-[#231f20] text-gray-100",
      tabContainer: "project-tabs sticky top-0 z-10 bg-[#231f20] border-b border-gray-700",
      tab: "project-tab text-gray-100 hover:text-white",
      activeTab: "project-tab active text-white",
      contentWrapper: "w-full h-full flex-grow bg-[#231f20]",
      errorContainer: "preload-chat-list bg-[#231f20]"
    }
  };

  if (error) {
    return (
      <div className={themeClasses[theme].errorContainer}>
        <ErrorView
          title="Unable to Load Projects"
          message={en.ChatList_ErrorLoadingChats}
          showRetryButton={true}
          onRetry={() => activeTab === 'private' ? fetchPrivateProjects : fetchAllPublicProjects}
        />
      </div>
    );
  }

  return (
    <>
      <div className={themeClasses[theme].tabContainer}>
        <button 
          className={activeTab === 'private' ? themeClasses[theme].activeTab : themeClasses[theme].tab}
          onClick={() => handleTabChange('private')}
 
        >
          My Projects
        </button>
        <button 
          className={activeTab === 'public' ? themeClasses[theme].activeTab : themeClasses[theme].tab}
          onClick={() => handleTabChange('public')}

        >
          Shared
        </button>
      </div>
      <div className={themeClasses[theme].contentWrapper}>
        {isLoading && activeTab === 'private' ? (
          <ProjectListSkeleton theme={theme} />
        ) : isPublicLoading && activeTab === 'public' ? (
          <ProjectListSkeleton theme={theme} />
        ) : (
          <ProjectList
            projects={activeTab === 'private' ? projects : publicProjects}
            onProjectClick={handleProjectClick}
            selectedProjectId={currentProjectId}
            closeSidebar={closeSidebar}
            openProjectModal={openProjectModal}
            isSharedTab={activeTab === 'public'}
            refreshProjectsList={refreshProjectsList}
            theme={theme}
          />
        )}
      </div>
      {isProjectModalOpen && (
        typeof window !== 'undefined' && createPortal(
          <CreateProjectModal
            isOpen={isProjectModalOpen}
            onClose={handleCloseModal}
            onUpdateResponse={handleUpdateResponse}
            type="Project"
          />,
          document.body
        )
      )}
    </>
  );
};

PreloadProjectList.displayName = 'PreloadProjectList';
export default PreloadProjectList;