import React, { useState, useRef, useEffect, useCallback } from "react";
import { 
  Loader2, 
  Refresh<PERSON><PERSON>,
  ExternalLink, 
  BookOpen,
  CheckCircle,
  AlertCircle,
  Clock,
  Link2
} from "lucide-react";
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";

const PreviewPanel = ({
  currentTaskId,
  currentIp,
  formatWorkspaceUrl,
}) => {
  const { wsConnection } = useCodeGeneration();
  
  // Consolidated state management
  const [preview, setPreview] = useState({
    status: 'not_started',
    url: null,
    message: null,
    timestamp: null,
    isRestarting: false // Track restart state
  });
  const [isSwaggerView, setIsSwaggerView] = useState(false);
  const [iframeKey, setIframeKey] = useState(Date.now());
  
  const isComponentMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isComponentMountedRef.current = false;
    };
  }, []);

  // Enhanced WebSocket handler with restart feedback
  useEffect(() => {
    if (!wsConnection) return;

    const handleMessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        // Handle preview status/response
        if (data.type === 'preview_status' || data.type === 'preview_response') {
          const responseData = data.data || data;
          
          setPreview(prev => ({
            ...prev,
            status: responseData.status || 'not_started',
            url: responseData.url || null,
            message: responseData.metadata?.error || responseData.metadata?.result || responseData.metadata?.state || null,
            timestamp: new Date().toISOString(),
            isRestarting: false // Clear restart flag on status update
          }));
        }
        
        // Handle restart responses with immediate feedback
        else if (data.type === 'preview_restart_response') {
          if (data.status === 'starting') {
            setPreview(prev => ({ 
              ...prev, 
              status: 'building',
              message: data.message || 'Restart initiated...',
              isRestarting: true
            }));
          } else if (data.status === 'completed') {
            setPreview(prev => ({
              ...prev,
              isRestarting: false
            }));
            // Request fresh status after restart
            requestStatus();
          }
        }
        
        // Handle preview errors
        else if (data.type === 'preview_error') {
          setPreview(prev => ({
            ...prev,
            status: 'failed',
            message: data.message || 'Preview error',
            isRestarting: false
          }));
        }

      } catch (error) {
        console.error('Preview WebSocket error:', error);
      }
    };

    wsConnection.addEventListener('message', handleMessage);
    return () => wsConnection.removeEventListener('message', handleMessage);
  }, [wsConnection]);

  // Handle iframe refresh
  const refreshIframe = useCallback(() => {
    setIframeKey(Date.now());
  }, []);

  // Auto-fetch status on mount
  useEffect(() => {
    if (wsConnection?.readyState === WebSocket.OPEN && currentTaskId) {
      requestStatus();
    }
  }, [wsConnection, currentTaskId]);

  // Optimized backend communication
  const requestStatus = useCallback(() => {
    if (wsConnection?.readyState === WebSocket.OPEN && currentTaskId) {
      wsConnection.send(JSON.stringify({
        type: "get_preview_url",
        task_id: currentTaskId,
        input_data: {}
      }));
    }
  }, [wsConnection, currentTaskId]);

  const restart = useCallback(() => {
    if (wsConnection?.readyState === WebSocket.OPEN && currentTaskId) {
      // Immediately show restart feedback
      setPreview(prev => ({
        ...prev,
        status: 'building',
        message: 'Initiating restart...',
        isRestarting: true
      }));
      
      // Send restart command
      wsConnection.send(JSON.stringify({
        type: "restart_preview",
        task_id: currentTaskId,
        input_data: {}
      }));
      
      // Generate new iframe key for fresh load
      setIframeKey(Date.now());
    }
  }, [wsConnection, currentTaskId]);

  // Render Swagger view
  if (isSwaggerView) {
    return (
      <iframe
        src={`${formatWorkspaceUrl(currentIp, 9876, currentTaskId)}/?basePath=/home/<USER>/workspace/${currentTaskId}`}
        className="w-full h-full border-0"
        title="Swagger View"
      />
    );
  }

  // Enhanced building state with restart indication
  if (preview.status === 'building' || preview.isRestarting) {
    return (
      <div className="w-full h-full flex items-center justify-center flex-col space-y-4">
        <Loader2 className="h-8 w-8 animate-spin text-orange-500" />
        <p className="text-gray-600 typography-body-sm">
          {preview.isRestarting ? 'Restarting preview...' : 'Building preview...'}
        </p>
        {preview.message && (
          <p className="text-gray-500 typography-caption max-w-md text-center">{preview.message}</p>
        )}
        <div className="typography-caption text-gray-400">
          {preview.isRestarting ? 'Please wait while we restart your application' : 'This may take a few moments'}
        </div>
      </div>
    );
  }

  // Failed state
  if (preview.status === 'failed') {
    return (
      <div className="w-full h-full flex items-center justify-center flex-col space-y-4">
        <AlertCircle className="h-8 w-8 text-red-500" />
        <p className="text-gray-600 typography-body-sm">Preview failed</p>
        {preview.message && (
          <p className="text-gray-500 typography-caption max-w-md text-center">{preview.message}</p>
        )}
        <button
          onClick={restart}
          disabled={preview.isRestarting}
          className="px-4 py-2 bg-orange-500 text-white rounded-md typography-body-sm hover:bg-orange-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
        >
          {preview.isRestarting ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              Restarting...
            </>
          ) : (
            'Try Again'
          )}
        </button>
      </div>
    );
  }

  // Not started state
  if (preview.status === 'not_started' || !preview.url) {
    return (
      <div className="w-full h-full flex items-center justify-center flex-col space-y-4">
        <Clock className="h-8 w-8 text-gray-400" />
        <p className="text-gray-600 typography-body-sm">Preview not started</p>
        <button
          onClick={restart}
          disabled={preview.isRestarting}
          className="px-4 py-2 bg-orange-500 text-white rounded-md typography-body-sm hover:bg-orange-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
        >
          {preview.isRestarting ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              Starting...
            </>
          ) : (
            'Start Preview'
          )}
        </button>
      </div>
    );
  }

  // Running state - show iframe
  return (
    <iframe
      key={`preview-${iframeKey}`}
      src={preview.url}
      className="w-full h-full border-none"
      title="Preview"
      sandbox="allow-same-origin allow-scripts allow-forms allow-popups"
      allow="microphone; camera; midi; encrypted-media;"
    />
  );
};

// Enhanced Action Buttons with better restart feedback
export const PreviewActionButtons = ({ currentTaskId, activeTab, activeView, setActiveView }) => {
  const { wsConnection } = useCodeGeneration();
  const [state, setState] = useState({
    url: null,
    status: 'not_started',
    message: null,
    isRestarting: false
  });
  const [isSwaggerView, setIsSwaggerView] = useState(false);

  useEffect(() => {
    if (!wsConnection || activeTab !== "Preview") return;

    const handleMessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        if (data.type === 'preview_status' || data.type === 'preview_response') {
          const responseData = data.data || data;
          setState(prev => ({
            ...prev,
            url: responseData.url || null,
            status: responseData.status || 'not_started',
            message: responseData.metadata?.error || responseData.metadata?.result || responseData.metadata?.state || null,
            isRestarting: false
          }));
        }
        
        else if (data.type === 'preview_restart_response') {
          if (data.status === 'starting') {
            setState(prev => ({
              ...prev,
              status: 'building',
              message: data.message || 'Restarting...',
              isRestarting: true
            }));
          } else if (data.status === 'completed') {
            setState(prev => ({
              ...prev,
              isRestarting: false
            }));
          }
        }
        
        else if (data.type === 'preview_error') {
          setState(prev => ({
            ...prev,
            status: 'failed',
            message: data.message || 'Preview error',
            isRestarting: false
          }));
        }
        
      } catch (error) {
        console.error('Action buttons WebSocket error:', error);
      }
    };

    wsConnection.addEventListener('message', handleMessage);
    return () => wsConnection.removeEventListener('message', handleMessage);
  }, [wsConnection, activeTab]);

  const restart = useCallback(() => {
    if (wsConnection?.readyState === WebSocket.OPEN && currentTaskId) {
      setState(prev => ({
        ...prev,
        status: 'building',
        message: 'Initiating restart...',
        isRestarting: true
      }));
      
      wsConnection.send(JSON.stringify({
        type: "restart_preview",
        task_id: currentTaskId,
        input_data: {}
      }));
    }
  }, [wsConnection, currentTaskId]);

  if (activeTab !== "Preview") return null;

  const StatusIcon = () => {
    if (state.isRestarting) {
      return <Loader2 className="w-4 h-4 text-orange-500 animate-spin" />;
    }
    
    switch (state.status) {
      case 'running':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'building':
        return <Loader2 className="w-4 h-4 text-orange-500 animate-spin" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusMessage = () => {
    if (state.isRestarting) return 'Restarting preview...';
    return state.message || `Preview ${state.status.replace('_', ' ')}`;
  };

  return (
    <div className="flex items-center gap-1">
      {/* Enhanced Status Indicator */}
      <BootstrapTooltip title={getStatusMessage()} placement="bottom">
        <div className="flex items-center">
          <StatusIcon />
        </div>
      </BootstrapTooltip>

      {/* Enhanced Restart Button */}
      <BootstrapTooltip 
        title={state.isRestarting ? "Rebuilding..." : "Rebuild Preview"} 
        placement="bottom"
      >
        <button
          className={`p-0.5 transition-colors ${
            state.isRestarting 
              ? 'text-orange-500 cursor-not-allowed' 
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={restart}
          disabled={state.isRestarting}
        >
          <RefreshCw className={`w-4 h-4 ${state.isRestarting ? 'animate-spin' : ''}`} />
        </button>
      </BootstrapTooltip>

      {/* External Link Button */}
      {state.url && !state.isRestarting && (
        <BootstrapTooltip title="Open in New Tab" placement="bottom">
          <button
            className="p-0.5 text-gray-500 hover:text-gray-700"
            onClick={() => window.open(state.url, "_blank")}
          >
            <ExternalLink className="w-4 h-4" />
          </button>
        </BootstrapTooltip>
      )}

      {/* Swagger View Toggle */}
      <BootstrapTooltip title="Swagger View" placement="bottom">
        <button
          className={`p-0.5 ${isSwaggerView ? "text-orange-500" : "text-gray-500 hover:text-gray-700"}`}
          onClick={() => setIsSwaggerView(!isSwaggerView)}
        >
          <BookOpen className="w-4 h-4" />
        </button>
      </BootstrapTooltip>
    </div>
  );
};

// Enhanced URL Display Component with refresh functionality
export const PreviewUrlDisplay = ({ currentTaskId, activeTab, onRefresh }) => {
  const { wsConnection } = useCodeGeneration();
  const [url, setUrl] = useState(null);
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    if (!wsConnection || activeTab !== "Preview") return;

    const handleMessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.type === 'preview_status' || data.type === 'preview_response') {
          const responseData = data.data || data;
          if (responseData.url) setUrl(responseData.url);
        }
      } catch (error) {
        console.error('URL WebSocket error:', error);
      }
    };

    wsConnection.addEventListener('message', handleMessage);
    return () => wsConnection.removeEventListener('message', handleMessage);
  }, [wsConnection, activeTab]);

  const handleCopy = useCallback(async () => {
    if (!url) return;
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Copy failed:', error);
    }
  }, [url]);

  const handleRefresh = useCallback(() => {
    if (onRefresh) {
      onRefresh();
    }
  }, [onRefresh]);

  if (!url || activeTab !== "Preview") return null;

  return (
    <div className="flex items-center bg-gray-100 rounded-md px-2 py-1 min-w-[196px] max-w-[35%] justify-between shadow-sm">
      <BootstrapTooltip title="Refresh Preview" placement="bottom">
        <button
          className="p-0.5 text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded transition-colors"
          onClick={handleRefresh}
        >
          <RefreshCw className="h-3 w-3" />
        </button>
      </BootstrapTooltip>
      <span className="text-gray-400 typography-caption mx-2 truncate flex-1 text-center">{url}</span>
      <BootstrapTooltip title={copied ? "Copied!" : "Copy URL"} placement="bottom">
        <button
          className={`p-0.5 rounded transition-colors ${
            copied 
              ? "text-green-600 hover:bg-green-50" 
              : "text-gray-500 hover:bg-gray-200 hover:text-gray-700"
          }`}
          onClick={handleCopy}
        >
          <Link2 className="h-3 w-3" />
        </button>
      </BootstrapTooltip>
    </div>
  );
};

export default PreviewPanel;