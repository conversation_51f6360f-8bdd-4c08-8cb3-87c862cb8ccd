"use client";
import React, { useState, useEffect, useContext } from "react";
import { getCurrentDiscussions } from "@/utils/api";
import { formatUTCToLocal } from "@/utils/datetime";
import { useRouter } from "next/navigation";
import { decrypt, getCookie } from "@/utils/auth";
import { CircularLoader } from "@/components/Loaders/Loading";
import { TopBarContext } from "@/components/Context/TopBarContext";
import ErrorView from "@/components/Modal/ErrorViewModal";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import en from "../../../en.json";

const DiscussionCard = ({
  discussionId,
  projectId,
  projectTitle,
  username,
  description,
  taskTitle,
  profileLogo,
  date,
  index,
  total,
  onProjectNavigate,
  onDiscussionNavigate,
}) => {
  const cardClassName = `my-discussion-card ${index === 0
    ? "card-rounded-top"
    : index === total - 1
      ? "card-rounded-bottom"
      : "card-rounded-none"
    }`;

  return (
    <div className={cardClassName}>
      <div className="discussion-card-content">
        <span>
          @ Mention in{" "}
          <span
            className="project-title"
            onClick={() => onProjectNavigate(projectId, projectTitle)}
          >
            #{projectTitle}
          </span>
        </span>
        <div className="my-discussion-card-date">{date}</div>
      </div>
      <div className="discussion-card-left">
        {/* Uncomment and update src with actual user profile logo if available */}
        {/* <Image
            src={profileLogo}
            alt={`${username} logo`}
            width={48}
            height={48}
            className="user-profile-image"
          /> */}
        <div
          className="discussion-card-info"
          onClick={() => onDiscussionNavigate(discussionId, projectId, "existing")}
        >
          <div className="my-discussion-card-username">{taskTitle}</div>
          <div className="my-discussion-card-description">{description}</div>
        </div>
      </div>
    </div>
  );
};

const DiscussionsList = () => {
  const [discussions, setDiscussions] = useState([]);
  const [user, setUser] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const router = useRouter();
  const { tabs, addTab, setActiveTab } = useContext(TopBarContext);

  const initializeComponent = async () => {
    setError(null);
    setIsLoading(true);
    try {
      await setCurrentUser();
      await fetchDiscussions();
    } catch (error) {
      
      setError(error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    initializeComponent();
  }, []);

  const setCurrentUser = async () => {
    const token = await getCookie("idToken");
    const decryptedUser = await decrypt(token);
    setUser(decryptedUser);
  };

  const fetchDiscussions = async () => {
    try {
      const response = await getCurrentDiscussions();
      setDiscussions(response);
    } catch (error) {
      
    } finally {
      setIsLoading(false);
    }
  };

  const handleProjectNavigation = (projectId, projectTitle = "") => {
    const discussionUrl = `/project/${projectId}/overview`;
    const existingTab = tabs.find((tab) => tab.href === discussionUrl);

    if (existingTab) {
      setActiveTab(existingTab.id);
      router.push(existingTab.href);
    } else {
      addTab(projectTitle, discussionUrl);
      router.push(discussionUrl);
    }
  };

  const handleDiscussionNavigation = (discussionId, projectId, type = "existing") => {
    const currentParams = new URLSearchParams(location.search);
    currentParams.set("discussion_id", discussionId);
    currentParams.set('discussion', type);

    router.push(`${location.pathname}?${currentParams.toString()}`);
  };

  if (isLoading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner">
          <CircularLoader />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <ErrorView
        title="Unable to Load Disscussions"
        message={en.DiscussionList_NoDiscussionsFound}
        onRetry={() => initializeComponent()}
        panelType='main'
      />
    );
  }

  if (discussions.length === 0) {
    return (
      <EmptyStateView type="discussions" />
    );
  }

  const sortedDiscussions = discussions.sort(
    (a, b) => new Date(b.date) - new Date(a.date)
  );

  return (
    <div className="discussion-list-container">
      {sortedDiscussions.map((discussion, index) => (
        <DiscussionCard
          key={discussion.discussion_id}
          discussionId={discussion.discussion_id}
          projectId={discussion.project_id}
          projectTitle={discussion.project_title}
          taskTitle={discussion.task_title}
          username={user.email}
          description={discussion.description}
          profileLogo={user}
          date={formatUTCToLocal(discussion.date)}
          index={index}
          total={discussions.length}
          onProjectNavigate={handleProjectNavigation}
          onDiscussionNavigate={handleDiscussionNavigation}
        />
      ))}
    </div>
  );
};

export default DiscussionsList;
