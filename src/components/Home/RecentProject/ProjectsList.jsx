import React, { useContext, useState } from "react";
import Image from "next/image";
import { formatUTCToLocal } from "@/utils/datetime";
import { useRouter } from "next/navigation";
import { TopBarContext } from "../../Context/TopBarContext";
import projectImage from "../../../../public/images/projects.svg";
import routeImage from "../../../../public/images/route.svg";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import CreateProjectModal from "@/components/Modal/CreateProjectModal";

const ProjectCard = ({ logo, name, date, projectId, onProjectClick }) => {
  const isRemoteImage = typeof logo === "string" && logo.startsWith("http");

  return (
    <div
      className="project-card group"
      onClick={() => onProjectClick(projectId, name)}
    >
      <div className="project-card-content">
        <Image
          src={logo}
          alt={`${name} logo`}
          width={48}
          height={48}
          className={`project-card-logo ${!isRemoteImage ? "local-image" : ""}`}
        />
        <div className="project-card-info">
          <div className="project-card-name custom-text-ellipsis">
            {name}
          </div>
          <div className="project-card-date custom-text-ellipsis">{date}</div>
        </div>
      </div>
      <div className="project-card-route">
        <Image
          src={routeImage}
          alt={`${name} route`}
          width={24}
          height={24}
          className="project-card-route-icon"
          placeholder="empty"
        />
      </div>
    </div>
  );
};

const ProjectsList = ({ activities }) => {
  const router = useRouter();
  const { tabs, addTab, setActiveTab } = useContext(TopBarContext);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  const handleProjectClick = (projectId, name) => {
    const taskUrl = `/project/${projectId}/overview`;
    const existingTab = tabs.find((tab) => tab.href.includes(projectId));

    if (existingTab) {
      setActiveTab(existingTab.id);
      router.push(existingTab.href !== taskUrl ? existingTab.href : taskUrl);
    } else {
      addTab(name, taskUrl);
      router.push(taskUrl);
    }
  };
  

  if (activities.length === 0) {
    return (
      <div className="projects-list-empty">
        <p className="projects-list-empty-text">
          <EmptyStateView
            type="noRecentProjectsFound"
            onClick={() => setIsCreateModalOpen(true)}
          />
        </p>
        <CreateProjectModal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          type="Project"
        />
      </div>
    );
  }

  return (
    <div className="projects-list">
      {activities.slice(0, 3).map((project) => (
        project.project_name && (
          <ProjectCard
            key={project.project_id}
            logo={projectImage}
            name={project.project_name}
            date={formatUTCToLocal(project.timestamp)}
            projectId={project.project_id}
            onProjectClick={handleProjectClick}
          />
        )
      ))}
    </div>
  );
};

export default ProjectsList;
