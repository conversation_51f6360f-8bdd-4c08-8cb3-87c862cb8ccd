import React, { useState, useEffect, useContext } from "react";
import { Square } from "lucide-react";
import { getAssignedTasks } from "@/utils/api";
import { formatUTCToLocal } from "@/utils/datetime";
import { useRouter } from "next/navigation";
import { CircularLoader } from "@/components/Loaders/Loading";
import { TopBarContext } from "@/components/Context/TopBarContext";
import ErrorView from "@/components/Modal/ErrorViewModal";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import en from "../../../en.json";

const TaskTable = () => {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { tabs, addTab, setActiveTab } = useContext(TopBarContext);
  const router = useRouter();

  const fetchAssignedTasks = async () => {
    setError(null);
    setLoading(true);
    try {
      const response = await getAssignedTasks();
      setTasks(response);
    } catch (error) {
      
      setError(error);
      setTasks([]); // Handle error by resetting tasks
    } finally {
      setLoading(false); // Set loading to false regardless of success or error
    }
  };

  useEffect(() => {
    fetchAssignedTasks();
  }, []);

  // Get priority class based on the task's priority level
  const getPriorityClass = (priority) => {
    const priorityClasses = {
      Low: "low-priority",
      Medium: "medium-priority",
      High: "high-priority",
      Critical: "critical-priority",
    };
    return priorityClasses[priority] || "low-priority";
  };

  // Handle task click to navigate to the task details
  const handleTaskClick = (task) => {
    const taskUrl = `/project/${task.project_id}/requirements/${task.type.toLowerCase()}/${task.task_id}`;
    const existingTab = tabs.find((tab) => tab.href === taskUrl);

    if (existingTab) {
      setActiveTab(existingTab.id);
    } else {
      addTab(`${task.project_title} - ${task.title}`, taskUrl);
    }

    router.push(taskUrl);
  };

  // Render the table content conditionally
  if (loading) {
    return (
      <div className="content-loading-overlay">
        <CircularLoader />
      </div>
    );
  }

  if (error) {
    return (
      <ErrorView
        title="Unable to Load Tasks"
        message={en.AssignedTasks_NoTasksFound}
        onRetry={() => fetchAssignedTasks()}
        panelType='main'
      />
    );
  }

  if (tasks.length === 0) {
    return (
      <EmptyStateView type="tasks" onClick={() => {}} />
    );
  }



  return (
    <div className="w-full table-container">
      <table className="assigned-tasks-table">
        <thead className="assigned-tasks-table-head-container">
          <tr>
            <th className="assigned-tasks-table-head">Project Title</th>
            <th className="assigned-tasks-table-head">Task Description</th>
            <th className="assigned-tasks-table-head">Assignee</th>
            <th className="assigned-tasks-table-head">Assigned On</th>
            <th className="assigned-tasks-table-head">Priority</th>
          </tr>
        </thead>
        <tbody>
          {tasks.map((task, index) => (
            <tr key={index}>
              <td className="assigned-tasks-table-data">
                <div className="custom-text-ellipsis">
                  {task.project_title || "PROJECTS"}
                </div>
              </td>
              <td
                className="assigned-tasks-table-data table-tooltip-trigger"
                title={task.title}
                onClick={() => handleTaskClick(task)}
              >
                <div className="custom-text-ellipsis">
                  {task.title.length > 30 ? task.title.substring(0, 30) + "..." : task.title}
                </div>
              </td>
              <td className="assigned-tasks-table-data">
                <div className="custom-text-ellipsis">
                  {task.assigner_name || task.assigner_email}
                </div>
              </td>
              <td className="assigned-tasks-table-data">
                <div className="custom-text-ellipsis">
                  {formatUTCToLocal(task.assigned_at)}
                </div>
              </td>
              <td className="assigned-tasks-table-data">
                <span className={`assigned-tasks-priority ${getPriorityClass(task.priority)}`}>
                  <Square fill="#687181" className="w-2 h-2 ml-1" />
                  <span className="ml-1">
                    {typeof task.priority === "string" ? task.priority.toUpperCase() : "LOW"}
                  </span>
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default TaskTable;
