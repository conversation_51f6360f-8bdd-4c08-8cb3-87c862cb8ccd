"use client";

import React, { useEffect, useState, useContext } from 'react';
import Image from 'next/image';
import useLocalStorage from "@/hooks/useLocalStorage";
import { useUser } from '@/components/Context/UserContext';
import { TopBarContext } from '@/components/Context/TopBarContext';
import Cookies from 'js-cookie';
import kavia<PERSON>ogo from "@/../public/logo/kavia_logo.svg";
import NextJSImage from "@/../public/images/nextjs.svg";
import ReactJSImage from "@/../public/images/react.svg";
import VueImage from "@/../public/images/vue.svg";
import AngularImage from "@/../public/images/angular_logo.svg"
import NuxtImage from "@/../public/images/nuxt_logo.svg"
import RemixImage from "@/../public/images/remix_logo.svg"
import RemotionImage from "@/../public/images/remotion_logo.png"
import SlidevImage from "@/../public/images/slidev_logo.svg"
import SvelteImage from "@/../public/images/svelte_logo.svg"
import TypescriptImage from "@/../public/images/ts_logo.svg"
import ViteImage from "@/../public/images/vite_logo.svg"
import QwikImage from "@/../public/images/qwik_logo.svg"
import AstroImage from "@/../public/images/astro_logo.svg"
import AndroidImage from "@/../public/images/android_logo.svg"
import KotlinImage from "@/../public/images/kotlin_logo.svg"

import FlutterImage from "@/../public/images/flutter_logo.svg"
import FlaskImage from "@/../public/images/flask_logo.svg"
import FastAPIImage from "@/../public/images/fastapi.svg"
import DjangoImage from "@/../public/images/django_logo.svg"
import ExpressImage from "@/../public/images/expressjs_logo.svg"
import AppTypeSwitch from './build/SimpleEnterpriceSwitch';
import TextInput from './build/TextInput';
import BuildOptionsButtons from './build/BuildOptionsButtons';
import ProjectSelectionComponent from './build/ProjectSelectionComponent';
import StackOptions from './build/StackOptions';
import ProjectCreationModal from './build/ProjectCreationFlow';

import { fetchProjectBlueprint } from '@/services/projectService';
import { useRouter } from 'next/navigation';
import { AlertContext } from './NotificationAlertService/AlertList';


export const frameworks = [
  { key: "angular", label: 'Angular', icon: AngularImage, type: 'web' },
  { key: "astro", label: 'Astro', icon: AstroImage, type: 'web' },
  { key: "nextjs", label: 'Next JS', icon: NextJSImage, type: 'web' },
  { key: "qwik", label: 'Qwik', icon: QwikImage, type: 'web' },
  { key: "nuxt", label: 'Nuxt', icon: NuxtImage, type: 'web' },
  { key: "react", label: 'React JS', icon: ReactJSImage, type: 'web', isDefault: true },
  { key: "remix", label: 'Remix', icon: RemixImage, type: 'web' },
  { key: "remotion", label: 'Remotion', icon: RemotionImage, type: 'web' },
  { key: "slidev", label: 'Slidev', icon: SlidevImage, type: 'web' },
  { key: "svelte", label: 'Svelte', icon: SvelteImage, type: 'web' },
  { key: "typescript", label: 'Typescript', icon: TypescriptImage, type: 'web' },
  { key: "vite", label: 'Vite', icon: ViteImage, type: 'web' },
  { key: "vue", label: 'Vue', icon: VueImage, type: 'web' },
  { key: "flutter", label: 'Flutter', icon: FlutterImage, type: 'mobile', isDefault: true },
  { key: "android", label: 'Android', icon: AndroidImage, type: ['mobile', 'native-app']},
  { key: "ios", label: 'iOS', icon: AndroidImage, type: ['mobile', 'native-app'] },
  { key: "kotlin", label: 'Kotlin', icon: KotlinImage, type: 'mobile' },
  { key: "flask", label: 'Flask', icon: FlaskImage, type: 'backend' },
  { key: "fastapi", label: 'FastAPI', icon: FastAPIImage, type: 'backend', isDefault: true },
  { key: "django", label: 'Django', icon: DjangoImage, type: 'backend' },
  { key: "express", label: 'Express.js', icon: ExpressImage, type: 'backend' },
];

const appTypeOptions = [
  { label: 'Apps' },
  { label: 'Projects' }
];

const getReactDefaultIndex = () => {
  return frameworks.findIndex(framework => framework.key === 'react');
};

const BuildContent = ({
  loggedInState,
  selectedType,
  setSelectedType,
  selectedBuildOption,
  setBuildOption,
  activeFramework,
  setActiveFramework,
  createProject: parentCreateProject,
  handleComplexProjectSubmit,
  isComplexProjectSubmitting,
  setIsModalOpen,
  isStreaming,
  loadingText,
  inputText,
  setInputText,
}) => {
  const [prompt, setPrompt] = useLocalStorage("prompt", "");
  const { tabs, addTab, setActiveTab } = useContext(TopBarContext);
  const [isProjectCreationModalOpen, setIsProjectCreationModalOpen] = useState(false);


  const [blueprintData, setBlueprintData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isImplementing, setIsImplementing] = useState(false); // New state for implementation loading
  const router = useRouter();
  const idToken = Cookies.get('idToken');
  const userId = Cookies.get('userId');
  const {showAlert} = useContext(AlertContext)
   const reactDefaultIndex = getReactDefaultIndex();

  // Enhanced state persistence using localStorage
  const [persistedInputText, setPersistedInputText] = useLocalStorage("user_input_text", "");
  const [persistedFramework, setPersistedFramework] = useLocalStorage("selected_framework", reactDefaultIndex);
  const [persistedBuildOption, setPersistedBuildOption] = useLocalStorage("selected_build_option", 0);
  const [persistedAppType, setPersistedAppType] = useLocalStorage("selected_app_type", 0);


// Determine if light theme should be used
  const isLight = false; // Always dark mode: !loggedInState would give light when not logged in

  // Sync with localStorage on mount
  useEffect(() => {
    if (prompt) {
      try {
        const promptObj = JSON.parse(prompt);
        setInputText(promptObj['requirement']);
        // Find framework index by key instead of hardcoded values
        const frameworkIndex = frameworks.findIndex(f => f.label === promptObj['framework']);
        setActiveFramework(frameworkIndex !== -1 ? frameworkIndex : frameworks.findIndex(f => f.key === 'react'));
      } catch (error) {

      }
    }
  }, []);

  // State for greeting to avoid hydration errors
  const [greeting, setGreeting] = useState("Welcome");
  useEffect(() => {
    if (persistedInputText) {
      setInputText(persistedInputText);

    }

    if (persistedFramework !== null && persistedFramework !== undefined) {
      setActiveFramework(persistedFramework);

    }

    if (persistedBuildOption !== null && persistedBuildOption !== undefined) {
      setBuildOption(persistedBuildOption);

    }
    if (persistedAppType !== null && persistedAppType !== undefined) {
      setSelectedType(persistedAppType);

    }

    if (prompt && !persistedInputText) {

      try {

        const promptObj = JSON.parse(prompt);
        if (promptObj['requirement']) {
          setInputText(promptObj['requirement']);
          setPersistedInputText(promptObj['requirement']);

        }
        if (promptObj['framework']) {
          // Find framework index by key or label instead of hardcoded values
          let frameworkIndex = frameworks.findIndex(f => f.key === promptObj['framework']);
          if (frameworkIndex === -1) {
            frameworkIndex = frameworks.findIndex(f => f.label === promptObj['framework']);
          }
          if (frameworkIndex === -1) {
            frameworkIndex = frameworks.findIndex(f => f.key === 'react'); // Default to React
          }

          setActiveFramework(frameworkIndex);
          setPersistedFramework(frameworkIndex);

        }
      } catch (error) {
        console.warn('Error parsing legacy prompt:', error);

      }
    }

  }, []);

  useEffect(() => {

    if (inputText !== persistedInputText) {
      setPersistedInputText(inputText);
    }

  }, [inputText]);

  useEffect(() => {
    if (activeFramework !== persistedFramework) {
      setPersistedFramework(activeFramework);

    }

  }, [activeFramework]);

  useEffect(() => {

    if (selectedBuildOption !== persistedBuildOption) {
      setPersistedBuildOption(selectedBuildOption);
    }
  }, [selectedBuildOption]);


  useEffect(() => {
    if (selectedType !== persistedAppType) {
      setPersistedAppType(selectedType);
    }
  }, [selectedType]);

  // Update greeting on client-side only
  useEffect(() => {
    const hour = new Date().getHours();
    let timeGreeting = "Welcome";
    if (hour < 12) timeGreeting = "Good morning";
    else if (hour < 18) timeGreeting = "Good afternoon";
    else timeGreeting = "Good evening";

    setGreeting(timeGreeting);
  }, []);

  const { name } = useUser();

  // Check if user is logged in
  const isLoggedIn = () => {
    const idToken = Cookies.get('idToken');
    return !!idToken;
  };

 const handleSubmitAndFetchBlueprint = async () => {
    
    setIsLoading(true);
    if (!idToken || !userId) {
   
      setPersistedInputText(inputText);
      setPersistedFramework(activeFramework);
      setPersistedBuildOption(selectedBuildOption);
      setPersistedAppType(selectedType);

    showAlert("Access denied. Please create an account or log in to proceed. ","info")
    router.push('/users/sign_up')
    return;
  }
    
    try {
      // Get the selected framework from state
      const selectedFrameworkObj = frameworks[activeFramework] || frameworks.find(f => f.isDefault);
      const selectedFrameworkName = selectedFrameworkObj?.label || "React JS";
      const selectedFrameworkType = selectedFrameworkObj?.type || "web";
      
      // Debug logging







      // Get the mock data from our service, passing the selected framework
      const blueprintData = await fetchProjectBlueprint(inputText, selectedFrameworkName);
       sessionStorage.setItem('generated_project_id',blueprintData?.projectInfo?.id );
      
      
      // Update the blueprint based on the selected framework type
      if (blueprintData && blueprintData.techStack) {
        // Check if the selected framework is a frontend or backend type
        const isBackendType = Array.isArray(selectedFrameworkType)
          ? selectedFrameworkType.includes('backend')
          : selectedFrameworkType === 'backend';

        // Set appropriate techStack values
        if (isBackendType) {

          blueprintData.techStack.backend = [selectedFrameworkName];
          blueprintData.techStack.frontend = ["None"];
        } else {

          blueprintData.techStack.frontend = [selectedFrameworkName];
          blueprintData.techStack.backend = ["None"];
        }

        // Debug logging for final blueprint


      }

      // Save it to state
      setBlueprintData(blueprintData);
      clearPersistedState();

      // Open the modal with the blueprint data
      setIsProjectCreationModalOpen(true);
    
    } catch (error) {
      console.error("Error in handleSubmitAndFetchBlueprint:", error);
      alert("Failed to generate project blueprint. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const clearPersistedState = () => {
    setPersistedInputText("");
    // Reset to React framework index instead of hardcoded 0
    const reactIndex = frameworks.findIndex(f => f.key === 'react');
    setPersistedFramework(reactIndex !== -1 ? reactIndex : 0);
    setPersistedBuildOption(0);
    setPersistedAppType(0);
  };


  const handleStartImplementation = async (state) => {

    setIsImplementing(true);
    if (parentCreateProject) {
      try {
        // Get the most up-to-date blueprint data from the state object
        const updatedBlueprint = state?.projectBlueprint || blueprintData;

        if (!updatedBlueprint) {
          console.error('No blueprint data available');
          return;
        }



        // Call parent function with the complete updated blueprint (not just projectInfo)
        const projectResponse = await parentCreateProject(updatedBlueprint);

        // If we have a project response with an ID, create a tab for it
        if (projectResponse && projectResponse.id) {
          const projectId = projectResponse.id.toString();
          const projectTitle = projectResponse.properties?.Title ||
            projectResponse.properties?.Name ||
            "New Project";
          const projectUrl = `/project/${projectId}/overview`;

          // Create project info object for the tab
          const projectInfo = {
            selected_project_id: projectId,
            selected_project_creator_email: '',
            is_public_selected: 'false',
            selected_tenant_id: Cookies.get('tenant_id') || ''
          };

          // Store in cookies for backward compatibility
          Cookies.set('selected_project_id', projectInfo.selected_project_id);
          Cookies.set('is_public_selected', projectInfo.is_public_selected);
          Cookies.set('selected_tenant_id', projectInfo.selected_tenant_id);

          // Check if a tab for this project already exists
          const existingTab = tabs.find((tab) => {
            const tabProjectId = tab.href.split('/')[2]; // Extract project ID from href
            return tabProjectId === projectId;
          });

          if (existingTab) {
            // Update existing tab
            setActiveTab(existingTab.id);
          } else {
            // Add new tab
            addTab(projectTitle, projectUrl, projectInfo);
          }
        }

        // Close the modal
        setIsProjectCreationModalOpen(false);
      } catch (error) {
        console.error('Error creating project:', error);
      } finally {
        setIsImplementing(false);
      }
    }
  };


  return (
    <div className={`max-w-2xl mx-auto flex flex-col items-center justify-center my-4 text-white transition-colors duration-500 ease-in-out`}>
      <div className={`w-full flex justify-center mb-6 transition-all duration-500 ease-in-out ${loggedInState ? 'opacity-0 h-0 overflow-hidden' : 'opacity-100'}`}>
        <Image
          src={kaviaLogo}
          alt={"Kavia AI"}
          className={`transition-transform duration-500 ease-in-out ${
            !loggedInState ? 'w-16 h-16 max-w-16 max-h-16' : 'size-22'
          }`}
        />
      </div>

      <div className={`text-center mb-2 transition-opacity duration-500 ease-in-out ${loggedInState ? 'opacity-100' : 'opacity-0 h-0 overflow-hidden'}`}>
        <p className={`text-gray-300 typography-heading-2 font-weight-medium transition-colors duration-500 ease-in-out`}>
          {`${greeting}, ${name || 'welcome back'}`}
        </p>
      </div>

      <h1 className={`typography-heading-1 font-weight-light text-center text-white mb-8 transition-colors duration-500 ease-in-out`}>
        What do you want to build today?
      </h1>

      <AppTypeSwitch selectedType={selectedType} setSelectedType={setSelectedType} isStreaming={isStreaming} isLight={isLight} />

      <div className="w-full min-h-[400px] flex flex-col items-center">
        {appTypeOptions[selectedType].label === "Apps" ? (
          <>
            <TextInput
              disabled={isStreaming || isLoading}
              loadingText={isLoading ? "Generating blueprint..." : loadingText}
              inputText={inputText}
              setInputText={setInputText}
              handleSubmit={handleSubmitAndFetchBlueprint}
              isLight={isLight}
            />

            <BuildOptionsButtons disabled={isStreaming} buildOption={selectedBuildOption} setBuildOption={setBuildOption} isLight={isLight} />
            <StackOptions
              frameworks={frameworks}
              activeFramework={activeFramework}
              setActiveFramework={setActiveFramework}
              isStreaming={isStreaming}
              isLight={isLight}
              buildOption={selectedBuildOption}
            />
          </>
        ) : (
          <ProjectSelectionComponent
            handleComplexProjectSubmit={handleComplexProjectSubmit}
            isComplexProjectSubmitting={isComplexProjectSubmitting}
            isLight={isLight}
            setIsModalOpen={setIsModalOpen}
          />
        )}
      </div>

      <ProjectCreationModal
        isOpen={isProjectCreationModalOpen}
        onClose={() => {
          setIsProjectCreationModalOpen(false);
          setBlueprintData(null); // Clear blueprint data when closing
        }}
        onStartImplementation={handleStartImplementation}
        initialBlueprint={blueprintData}
        frameworkOptions={frameworks}
        isImplementing={isImplementing}
      />
    </div>
  );
};

export default BuildContent;