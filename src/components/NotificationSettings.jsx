// components/NotificationSettings.tsx
"use client"
import { useState } from 'react';

export const NotificationSettings = () => {
  const [settings, setSettings] = useState({
    pushEnabled: true,
    emailEnabled: true,
  });

  const updateSettings = async (newSettings) => {
    setSettings(newSettings);
    // Add API call to update user preferences
  };

  return (
    <div className="p-4 bg-white rounded-lg shadow">
      <h2 className="typography-heading-4 font-weight-bold mb-4">Notification Settings</h2>
      
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <span>Push Notifications</span>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={settings.pushEnabled}
              onChange={(e) => updateSettings({ ...settings, pushEnabled: e.target.checked })}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
          </label>
        </div>

      </div>
      
    </div>
  );
};