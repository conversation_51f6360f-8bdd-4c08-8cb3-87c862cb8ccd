import React, { useEffect, useRef, useState, useContext, useCallback } from "react";
import { useRouter, usePathname, useSearchParams, useParams } from "next/navigation";
import Graph2d from './Graph2D';
// import Graph2d from './Graph2d'; 
// import Graph3d from './Graph3d';  // Import your new 3D graph component
import { fetchdemoGraphDiagram , fetchNodeById , fetchsearchDiagram} from "@/utils/graphAPI";

// import { fetchNodeById , fetchGraphNodeById } from "@/utils/api";
import { CircularLoader } from "../Loaders/Loading";
import { FaSpinner } from "react-icons/fa";
import GraphFilterModal from "../Modal/GraphFilterModal";
import { AlertContext } from "../NotificationAlertService/AlertList";
import en from "../../en.json";
import { <PERSON>P<PERSON>, Fi<PERSON>inus, FiX } from "react-icons/fi";
import { StateContext } from "../Context/StateContext";

const GraphComponent = () => {
  // const [nodes, setNodes] = useState([]);
  // const [edges, setEdges] = useState([]);
  const [data, setData] = useState({ nodes: [], links: [] });
  const [selectedNode, setSelectedNode] = useState(null);
  const [loading, setLoading] = useState(true);
  const networkRef = useRef(null);
  const [] = useState('graphView'); // Default graph view
  const searchParams = useSearchParams();
  const params = useParams();
  const projectId = params.projectId
  const pathname = usePathname();
  const id = pathname.split("/")[2];
  const router = useRouter();
  const [] = useState(false);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [, setIsLoadingDetails] = useState(false);
  const [selectedNodeDetails, setSelectedNodeDetails] = useState(null);
  const { showAlert } = useContext(AlertContext);
  const [] = useState(false);
  const [isCodeGraph, setIsCodeGraph] = useState(() => {
    const storedGraphType = localStorage.getItem('isCodeGraph');
    return storedGraphType !== null ? JSON.parse(storedGraphType) : false;
  });
  const [loadTime, setLoadTime] = useState(null);
  const filterModalRef = useRef(null);
  const [levels, setLevels] = useState(2); 
  const [selectedNodeTypes, setSelectedNodeTypes] = useState([]);
  const {setIsCollapsed} = useContext(StateContext);
  const [searchTerm, setSearchTerm] = useState("");
  const [isSearchResult, setIsSearchResult] = useState(false);


  const handleClickOutside = useCallback((event) => {
    if (filterModalRef.current && !filterModalRef.current.contains(event.target)) {
      setIsFilterModalOpen(false);
    }
  }, []);

  const handleSearch = async (event) => {
    const searchText = event.target.value;
    setSearchTerm(searchText);
  
    if (searchText.length >= 1) {
      const graph = isCodeGraph ? "code" : "project";
      try {
        const samplesearchresponse = await fetchsearchDiagram(projectId, graph, searchText);
        const graphData = samplesearchresponse;
  
        setIsSearchResult(true);
        setData(graphData);
      } catch (error) {
        
        showAlert("Error searching nodes", "error");
      }
    } else {
      setIsSearchResult(false);
      fetchData();
    }
  };
  

  
  useEffect(() => {
    // entering the Graph page
    setIsCollapsed(true);

    // leaving the Graph page 
    return () => {
      setIsCollapsed(false);
    };
  }, [setIsCollapsed]);

  
  useEffect(() => {
    if (isFilterModalOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isFilterModalOpen, handleClickOutside]);


  const handleNodeClick = (node) => {
    setSelectedNode(node);
    setSelectedNodeDetails(null);
    if (networkRef.current) {
      if (typeof networkRef.current.setSelection === 'function') {
        networkRef.current.setSelection({ nodes: [node.id] });
      }
    }
  };

  useEffect(() => {
    if (selectedNode && !selectedNodeDetails) {
      // Fetch details if they are not already loaded
      handleViewMoreDetails(selectedNode);
    }
  }, [selectedNode]);

  const handleViewMoreDetails = async (node) => {
    if (node) {
      setIsLoadingDetails(true);
      try {
        let details = null
        
        
        const graph = isCodeGraph ? "code" : "project";

        if (isCodeGraph) details = await fetchNodeById(node.id , graph);
        else details = await fetchNodeById(node.id,  graph );

        setSelectedNodeDetails(details);
      } catch (error) {
        
      } finally {
        setIsLoadingDetails(false);
      }
    }
  };

  const fetchData = async () => {
    setLoading(true); // Show loading indicator initially
    setIsUpdating(true); // Mark as updating (if there were previous nodes/edges)
    const startTime = performance.now();
  
    try {
      const graph = isCodeGraph ? "code" : "project";
  
      let updatedNodeTypes = selectedNodeTypes;
  
      if (updatedNodeTypes.length === 0) {
        if (graph === "project") {
          updatedNodeTypes = [];
          localStorage.setItem('selectedNodeTypes', JSON.stringify(updatedNodeTypes));
        } else if (graph === "code") {
          updatedNodeTypes = ["ProjectRoot"];
          localStorage.setItem('selectedNodeTypes', JSON.stringify(updatedNodeTypes));
        }
      }
  
      const demoresponse = await fetchdemoGraphDiagram(projectId, graph, levels, updatedNodeTypes);
  
      const graphData = demoresponse;
  
      graphData.links.forEach(link => { 
        const a = graphData.nodes.find(node => node.id === link.source);
        const b = graphData.nodes.find(node => node.id === link.target);
        if (a && b) {
          a.neighbors.push(b);
          b.neighbors.push(a);
          a.links.push(link);
          b.links.push(link);
        }
      });
  
      setData(graphData);
  
      const endTime = performance.now();
      const timeElapsed = ((endTime - startTime) / 1000).toFixed(2); // Convert to seconds and round to 2 decimal places
      setLoadTime(timeElapsed);
  
    } catch (err) {
      
      showAlert(`Error ${err}`);
    } finally {
      setLoading(false);
      setIsUpdating(false);
    }
  };

  useEffect(() => {
    fetchData(); // Call fetchData when the component mounts or when certain dependencies change
  }, [id, searchParams, isCodeGraph, selectedNodeTypes]);
  
  const handleZoomIn = () => {
    if (networkRef.current && networkRef.current.zoomIn) {
      networkRef.current.zoomIn();
    }
  };
  
  const handleZoomOut = () => {
    if (networkRef.current && networkRef.current.zoomOut) {
      networkRef.current.zoomOut();
    }
  };
  
  const handleReset = () => {
    if (networkRef.current && networkRef.current.reset) {
      networkRef.current.reset();
    }
  };



  const handleFilterApplied = (selectedTypeData) => {
    
    setLevels(selectedTypeData.levels);
    setSelectedNodeTypes(selectedTypeData.selectedNodeTypes);
    // // Update your component's state with the new graph data

  };

  const handleFilter = () => {
    setIsFilterModalOpen(true); // Open the FilterModal when Filter button is clicked
  };
  const handleCloseFilterModal = () => {
    setIsFilterModalOpen(false);
  };


  const formatKey = (key) => {
    // Replace underscores with spaces
    let formattedKey = key.replace(/_/g, ' ');

    // Add space before capital letters (except the first character)
    formattedKey = formattedKey.replace(/([A-Z])/g, ' $1').trim();

    // Capitalize the first letter of each word
    return formattedKey.replace(/\b\w/g, c => c.toUpperCase());
  };

  const renderNodeDetails = () => {
    if (!selectedNode) return null;

    return (
      <>
        <div className="h-[67vh] bg-white shadow-lg border rounded-lg p-6 w-full ">
          <div className="flex justify-between items-center mb-4 max-h-[80vh]">
            <h2 className="typography-heading-4 font-weight-bold text-gray-800 break-words"> 
              {selectedNode.label}
            </h2>
            <button
              onClick={() => {
                setSelectedNode(null);
                setSelectedNodeDetails(null);
              }}
              className="text-gray-500 hover:text-gray-700 flex-shrink-0 ml-2"
            >
              <FiX size={20} />
            </button>
          </div>


          {selectedNodeDetails ? (
            <div className="mt-4 space-y-4 overflow-y-auto custom-scrollbar h-[45vh]">
              <div key="Label" className="flex flex-col space-y-1">
                <span className="font-weight-bold text-gray-600 text-left">
                  Label:
                </span>
                <span className="text-gray-800 break-words text-left">
                  {selectedNodeDetails['labels'] && selectedNodeDetails['labels'].length > 0
                    ? selectedNodeDetails['labels'][0]
                    : "No labels found"}
                </span>
              </div>

              {Object.entries(selectedNodeDetails.properties || {}).map(
                ([key, value]) => (
                  <div key={key} className="flex flex-col space-y-1">
                    <span className="font-weight-bold text-gray-600 text-left">
                      {formatKey(key)}:
                    </span>
                    <span className="text-gray-800 break-words text-left">
                      {value}
                    </span>
                  </div>
                )
              )}
            </div>
          ) : (
            <div className="mt-10 text-gray-500 min-h-[60vh] items-center   justify-center ">Loading...</div>
          )}
        </div>
      </>
    );
  };

  return (
    <div className="flex flex-col h-screen bg-gray-100">
    <div className="flex items-center justify-between p-2 bg-white shadow-sm">
    <h1 className="typography-heading-4 font-weight-bold">{isCodeGraph ? "Code Graph" : "Project Graph"}</h1>
      <div className="flex items-center space-x-4">
        <input
          type="text"
          placeholder="Search Nodes"
          className="px-4 py-1 border rounded-md"
          value={searchTerm}
          onChange={handleSearch}
        />
        <button
          onClick={handleFilter}
          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center"
        >
          <svg
                  width="18"
                  height="18"
                  viewBox="0 0 13 12"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M1.04721 0.861585L1.04721 0.86158L1.04549 0.859335C1.0195 0.825447 1.00451 0.786305 1.00087 0.746459C0.997243 0.706641 1.00494 0.666154 1.0239 0.629265C1.04291 0.592268 1.07294 0.559604 1.11198 0.536167C1.15112 0.512675 1.19715 0.499815 1.24479 0.500002V0.500006H1.24675L11.7532 0.50001L11.7552 0.500002C11.8028 0.499815 11.8489 0.512676 11.888 0.536166C11.9271 0.559603 11.9571 0.592267 11.9761 0.629265C11.9951 0.666157 12.0028 0.706644 11.9991 0.746456L12.4971 0.791866L11.9991 0.74646C11.9955 0.786304 11.9805 0.825445 11.9545 0.85933L11.9528 0.861585L7.59727 6.60704L7.49572 6.74099V6.90909V11.0165L5.50428 9.564V6.90909V6.74099L5.40273 6.60704L1.04721 0.861585Z"
                    fill="white"
                    stroke="white"
                  />
                </svg>
        </button>
 
      </div>
    </div>
    <div className="flex flex-1 overflow-hidden">
      
      <div className="flex-1 relative">
        {loading ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <CircularLoader />
          </div>
        ) : (
          <>        
            {data.nodes.length > 0  ? (
                 <Graph2d 
                 graphData={{ data }}
                 isSearchResult={isSearchResult}
                 style={{ height: "100%", width: "100%" }}
                 onNodeClick={handleNodeClick}  // Pass the callback function here
                 networkRef={networkRef}
               />
              ) : (
                <p className="text-center mt-64 text-gray-500">{en.Graph_NoNodesOrEdgesFound}</p>
              )}
            {isUpdating && (
              <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-white px-4 py-2 rounded-full shadow-md flex items-center">
                <FaSpinner className="animate-spin mr-2" />
                <span>Updating...</span>
              </div>
            )}
          </>
        )}
        <div className="absolute top-4 left-4 flex space-x-2 bg-white rounded-md shadow-md">
          <button onClick={handleZoomIn} className="p-2 hover:bg-gray-100">
            <FiPlus />
          </button>
          <button onClick={handleReset} className="p-2 hover:bg-gray-100">
            Reset
          </button>
          <button onClick={handleZoomOut} className="p-2 hover:bg-gray-100">
            <FiMinus />
          </button>
        </div>
      </div>
      <div className="w-[30%] mb-4 z-50 bg-white b-2">
        {isFilterModalOpen ? (
          <div ref={filterModalRef}>
            <GraphFilterModal
              onClose={handleCloseFilterModal}
              onFilterApplied={handleFilterApplied}
              isCodeGraph={isCodeGraph}
            />
          </div>
        ) : (
          <div className="flex flex-col items-start p-2 w-full">
          <div className="flex flex-col w-full gap-2">
          <select
                className="p-2 border border-gray-300 rounded-md"
                onChange={(e) => {
                  const selectedValue = e.target.value;
                  if (selectedValue === 'old') {
                    router.push(`/project/${projectId}/graph`)

                  } else if (selectedValue === 'new3d') {
                    router.push(`/project/${projectId}/newgraph3d`)
                  }
                }}
              >
                <option value="old" disabled={!pathname.includes('/newgraph2d')}>
                  {pathname.includes('/newgraph2d') ? '2D Graph' : 'Select Graph Type'}
                </option>
                <option value="old">Old Graph</option>
                <option value="new3d">3D Graph</option>
              </select>
            <div className="mt-2 flex items-center justify-between">
              <div className="flex items-center w-full mr-2">
             
                <div
                  className="flex items-center cursor-pointer"
                  onClick={() => {
                    setIsCodeGraph(false);
                    setSelectedNodeTypes([]);
                    setLevels(2);
                    localStorage.setItem('isCodeGraph', JSON.stringify(false)); // Store in local storage
                    setSearchTerm("")
                    setIsSearchResult(false);
                  }}
                >
                  <input
                    id="project-graph-radio"
                    checked={!isCodeGraph}
                    type="radio"
                    name="graph-type"
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 cursor-pointer"
                  />
                  <label htmlFor="project-graph-radio" className="ml-2 typography-body-sm font-weight-medium text-gray-900">
                    Project Graph
                  </label>
                </div>
                  
                {/* <div
                  className="ml-4 flex items-center cursor-pointer"
                  onClick={() => {
                    setIsCodeGraph(true);
                    setSelectedNodeTypes([]);
                    setLevels(2);
                    localStorage.setItem('isCodeGraph', JSON.stringify(true)); // Store in local storage
                    setSearchTerm("")
                    setIsSearchResult(false);
                  }}
                >
                  <input
                    id="code-graph-radio"
                    checked={isCodeGraph}
                    type="radio"
                    name="graph-type"
                    onChange={() => setIsCodeGraph(!isCodeGraph)}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 cursor-pointer"
                  />
                  <label htmlFor="code-graph-radio" className="ml-2 typography-body-sm font-weight-medium text-gray-900">
                    Code Graph
                  </label>
                </div> */}
              </div>
            </div>

            <div className="ml-1 typography-body-sm text-gray-600">
              <div> {data.nodes.length} Nodes in {loadTime} seconds</div>
            </div>

            {selectedNode ? (
            <div className="pb-4">
              {renderNodeDetails()}
            </div>
          ) : (
            <div className="flex bg-gray-100 items-center justify-center h-[65vh] text-gray-500 pb-4 overflow-y-auto">
              Click on a node to see details
            </div>
          )}
          </div>
            
       
        </div>
        )}
      </div>

    </div>
 
  </div>
  );
};

export default GraphComponent;