/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useRef, useState, useContext } from "react";
import dynamic from "next/dynamic";
import { ArchitectureContext } from "../Context/ArchitectureContext";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { fetchArchitecturalElementByProperty } from "@/utils/api";
import { FiPlus, FiMinus, FiMaximize, FiMinimize } from 'react-icons/fi';
import * as Dialog from '@radix-ui/react-dialog';

const mermaid = dynamic(() => import("mermaid"), {ssr: false});
// Fixed svgPanZoom import to ensure the default export is used
const svgPanZoom = dynamic(() => import("svg-pan-zoom").then(mod => mod.default), {ssr: false});

const MermaidChart = ({ chartDefinition }) => {
  const id = `mermaid-${Math.floor(1000 + Math.random() * 9000)}`;
  const mermaidRef = useRef(null);
  const modalMermaidRef = useRef(null);
  const panZoomRef = useRef(null);
  const modalPanZoomRef = useRef(null);
  const modalContentRef = useRef(null);
  const [_isFullscreen, setIsFullscreen] = useState(false);
  const [diagramHeight, setDiagramHeight] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const { getArchitecture, selectArchitecture, getArchitectureVal } = useContext(ArchitectureContext);
  const router = useRouter();
  const params = useParams();
  const [isZoomEnabled, setIsZoomEnabled] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    import("mermaid").then((mermaid) => {
      mermaid.default.initialize({
        startOnLoad: false,
        theme: "default",
        securityLevel: "loose",
        class: {
          titleTopMargin: 20,
          marginX: 50,
          marginY: 40,
          useMaxWidth: true,
          defaultDiagramDirection: 'TB',
          htmlLabels: true,
          diagramPadding: 20,
          curve: 'basis',
          arrowMarkerAbsolute: false,
          fontSize: 'var(--font-size-code)',
          labelBackground: '#f8f9fa',
          fontFamily: 'Inter, sans-serif'
        },
        flowchart: {
          useMaxWidth: true,
          htmlLabels: true,
          curve: 'basis',
          direction: 'LR',
          rankSpacing: 50,
          nodeSpacing: 8,
          padding: 15,
          ranker: 'tight-tree'
        },
        themeVariables: {
          primaryColor: '#326CE5',
          primaryTextColor: '#333333',
          primaryBorderColor: '#666666',
          lineColor: '#666666',
          secondaryColor: '#F5F5F5',
          tertiaryColor: '#fff',
          nodeBorder: '#326CE5',
          clusterBkg: '#F8F9FA',
          clusterBorder: '#DFE1E6',
          defaultLinkColor: '#666666',
          classText: '#333333',
          titleColor: '#333333',
          edgeLabel: '#666666',
          edgeColor: '#666666',
          fontSize: 'var(--font-size-code)',
          nodePadding: 8
        }
      })
    });
  }, []);

  let projectId = params.projectId;

  const renderMermaidDiagram = async (container, content) => {
    try {
      setIsLoading(true);
      setError(null);

      // First try parsing to catch syntax errors
      await import("mermaid").then((mermaid) => mermaid.default.parse(content)); //avoiding ssr issue as importing mermaid in the beginning will want to use window, that is only client side

      if (container.current) {
        // Clean up previous rendering
        container.current.removeAttribute('data-processed');

        // Render the diagram
        const diagram = await import("mermaid").then((mermaid) => mermaid.default.render(id, content));
        container.current.innerHTML = diagram.svg;

        const svgElement = container.current.querySelector('svg');
        if (svgElement) {
          svgElement.style.width = '100%';
          svgElement.style.height = '100%';
          svgElement.style.maxWidth = '100%';
          svgElement.style.maxHeight = '100%';

          // Calculate natural height for non-expanded view
          const naturalHeight = svgElement.getBoundingClientRect().height;
          if (!isExpanded && typeof window !== "undefined") {
            setDiagramHeight(Math.min(window.innerHeight * 0.45, Math.max(350, naturalHeight)));
          }

          attachNodeClickHandlers(svgElement);
        }
      }
      return true;
    } catch (err) {

      setError(err.message || 'Failed to render diagram');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Fixed to properly initialize pan-zoom functionality
  const initializePanZoom = async () => {
    if (mermaidRef.current) {
      const svgElement = mermaidRef.current.querySelector('svg');
      if (svgElement) {
        if (panZoomRef.current) {
          panZoomRef.current.destroy();
        }

        try {
          // Ensure the module is loaded
          const panZoomModule = await import('svg-pan-zoom');
          panZoomRef.current = panZoomModule.default(svgElement, {
            zoomEnabled: true,
            controlIconsEnabled: false,
            mouseWheelZoomEnabled: true,
            fit: true,
            center: true,
            minZoom: 0.1,
            maxZoom: 10,
            beforePan: function (_oldPan, newPan) {
              return newPan;
            }
          });
        } catch (err) {

        }
      }
    }
  };

  // Fixed to properly initialize modal pan-zoom functionality
  const initializeModalPanZoom = async () => {
    if (modalMermaidRef.current) {
      const svgElement = modalMermaidRef.current.querySelector('svg');
      if (svgElement) {
        if (modalPanZoomRef.current) {
          modalPanZoomRef.current.destroy();
        }

        try {
          // Ensure the module is loaded
          const panZoomModule = await import('svg-pan-zoom');
          modalPanZoomRef.current = panZoomModule.default(svgElement, {
            zoomEnabled: true,
            controlIconsEnabled: false,
            mouseWheelZoomEnabled: true,
            fit: true,
            center: true,
          });
        } catch (err) {

        }
      }
    }
  };

  const handleNodeClick = (e) => {
    if (!isZoomEnabled) {
      e.stopPropagation();
      return;
    }
    const node = e.currentTarget;

    if (getArchitecture(node.textContent)) {
      const id = getArchitecture(node.textContent).id;
      selectArchitecture(getArchitectureVal(id, projectId));
      router.push(`/project/${projectId}/architecture/high-level/${getArchitecture(node.textContent).id}`);
    } else {
      if (params.architectureId) {
        fetchArchitecturalElementByProperty(params.architectureId, "Title", node.textContent).then((res) => {
          if (res.length > 0) {
            selectArchitecture({ ...res[0].properties, id: res[0].id });
            router.push(`/project/${projectId}/architecture/high-level/${res[0].id}`);
          }
        });
      }
    }
  };

  const attachNodeClickHandlers = (svgElement) => {
    const nodes = svgElement.querySelectorAll('.node');
    nodes.forEach(node => {
      node.style.cursor = 'pointer';
      node.addEventListener('click', handleNodeClick);
    });
  };

  // Improved zoom handlers with better error handling
  const handleZoomIn = (e, pzRef) => {
    e.stopPropagation();
    if (pzRef.current) {
      try {
        pzRef.current.zoomIn();
      } catch (err) {

      }
    }
  };

  const handleZoomOut = (e, pzRef) => {
    e.stopPropagation();
    if (pzRef.current) {
      try {
        pzRef.current.zoomOut();
      } catch (err) {

      }
    }
  };

  const handleReset = (e, pzRef) => {
    e.stopPropagation();
    if (pzRef.current) {
      try {
        pzRef.current.reset();
      } catch (err) {

      }
    }
  };

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
    if (!isExpanded) {
      setTimeout(() => {
        if (modalPanZoomRef.current) {
          modalPanZoomRef.current.reset();
          modalPanZoomRef.current.fit();
          modalPanZoomRef.current.center();
        }
      }, 600);
    }
  };

  // Fixed useEffect for better rendering and zoom initialization
  useEffect(() => {
    if (chartDefinition) {
      const timer = setTimeout(() => {
        renderMermaidDiagram(mermaidRef, chartDefinition).then((success) => {
          if (success && isZoomEnabled) {
            initializePanZoom();
          }
        });
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [chartDefinition, params.architectureId, projectId, isZoomEnabled]);

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  // Fixed useEffect for modal rendering and zoom initialization
  useEffect(() => {
    if (isExpanded && chartDefinition) {
      const timer = setTimeout(() => {
        renderMermaidDiagram(modalMermaidRef, chartDefinition).then((success) => {
          if (success) {
            initializeModalPanZoom();
          }
        });
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [isExpanded, chartDefinition]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalContentRef.current && !modalContentRef.current.contains(event.target)) {
        setIsExpanded(false);
      }
    };

    if (isExpanded) {
      document.addEventListener("mousedown", handleClickOutside, true);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside, true);
    };
  }, [isExpanded]);

  return (
    <div className="relative">
      <div className="bg-white rounded-md w-full">
        <div className="p-4 flex justify-between items-center border-b" style={{ placeContent: "flex-end" }}>
          <div className="flex justify-between w-full">
            <button onClick={() => setIsZoomEnabled(!isZoomEnabled)} className="bg-gray-200 px-2 py-1 typography-body-sm rounded">
              {isZoomEnabled ? 'Disable zoom' : 'Enable zoom'}
            </button>
            <div className="flex space-x-2">
              {isZoomEnabled && (
                <>
                  <button onClick={(e) => handleZoomIn(e, panZoomRef)} className="p-1 rounded hover:bg-gray-100">
                    <FiPlus />
                  </button>
                  <button onClick={(e) => handleReset(e, panZoomRef)} className="p-1 rounded hover:bg-gray-100">
                    Reset
                  </button>
                  <button onClick={(e) => handleZoomOut(e, panZoomRef)} className="p-1 rounded hover:bg-gray-100">
                    <FiMinus />
                  </button>
                </>
              )}
              <button onClick={toggleExpand} className="p-1 rounded hover:bg-gray-100">
                {isExpanded ? <FiMinimize /> : <FiMaximize />}
              </button>
            </div>

            <Dialog.Root open={isExpanded} onOpenChange={setIsExpanded}>
              <Dialog.Portal>
                <Dialog.Overlay className="fixed inset-0 bg-black bg-opacity-50 z-50 radix-dialog-overlay" />
                <Dialog.Content className="fixed inset-0 flex items-center justify-center p-4 z-50 cursor-grab active:cursor-grabbing">
                  <div className="bg-white rounded-lg shadow-lg w-[90%] h-[90%] p-4 flex flex-col" ref={modalContentRef}>
                    <div className="flex justify-end items-center border-b mb-4 space-x-2">
                      <button onClick={(e) => handleZoomIn(e, modalPanZoomRef)} className="p-1 rounded hover:bg-gray-100">
                        <FiPlus />
                      </button>
                      <button onClick={(e) => handleReset(e, modalPanZoomRef)} className="p-1 rounded hover:bg-gray-100">
                        Reset
                      </button>
                      <button onClick={(e) => handleZoomOut(e, modalPanZoomRef)} className="p-1 rounded hover:bg-gray-100">
                        <FiMinus />
                      </button>
                      <Dialog.Close asChild>
                        <button onClick={(e) => { e.stopPropagation(); toggleExpand(); }} className="p-1 rounded hover:bg-gray-100">
                          <FiMinimize />
                        </button>
                      </Dialog.Close>
                    </div>
                    <div className="flex-grow h-full w-full overflow-auto pt-4">
                      {isLoading && (
                        <div className="flex gap-2 items-center justify-center h-full">
                          <div className="animate-spin h-5 w-5 border-2 border-gray-900 rounded-full border-t-transparent" />
                          <p className="typography-body-sm text-gray-700">Rendering diagram...</p>
                        </div>
                      )}
                      {error && (
                        <p className="typography-body-sm text-red-600 text-center">
                          Unable to render diagram. Please check the syntax or try again.
                        </p>
                      )}
                      <div className="mermaid h-full w-full" ref={modalMermaidRef} id={`mermaid-modal-${id}`} />
                    </div>
                  </div>
                </Dialog.Content>
              </Dialog.Portal>
            </Dialog.Root>
          </div>
        </div>

        <div className="p-4">
          <div
            className={`flex-grow overflow-auto relative ${isZoomEnabled ? 'cursor-grab' : ''} active:${isZoomEnabled ? 'cursor-grabbing' : ''}`}
            style={{
              height: `${diagramHeight}px`,
              minHeight: '100px',
              transition: 'height 0.3s ease',
            }}
          >
            {isLoading && (
              <div className="flex gap-2 items-center justify-center h-full">
                <div className="animate-spin h-5 w-5 border-2 border-gray-900 rounded-full border-t-transparent" />
                <p className="typography-body-sm text-gray-700">Rendering diagram...</p>
              </div>
            )}
            {error && (
              <p className="typography-body-sm text-red-600 text-center">
                Unable to render diagram. Please check the syntax or try again.
              </p>
            )}
            <div className="mermaid h-full w-full" ref={mermaidRef} id={`mermaid-${id}`} style={{
              position: 'relative',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default MermaidChart;