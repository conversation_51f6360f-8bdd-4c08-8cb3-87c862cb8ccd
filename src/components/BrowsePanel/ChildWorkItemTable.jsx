import React, { useState, useEffect, useRef, useContext } from "react";
import {
  CircleChevronDown,
  CircleChevronRight,
  MoreVertical,
  Square,
  Trash,
} from "lucide-react";

import DeleteProjectModal from "../Modal/DeleteProjectModal";
import {
  fetchChildRequirements,
  deleteNodeById,
  getAvailableStatus,
  getAvailablePriorities,
  updateNodeByPriority,
  deleteMultipleNodes
} from "@/utils/api";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import Search from "./TableComponents/Search";
import { Loading2 } from "../Loaders/Loading";
import EmptyStateView from "../Modal/EmptyStateModal";
import ErrorView from "@/components/Modal/ErrorViewModal";
import en from "@/en.json"
import WorkItemTable from "../SimpleTable/WorkItemTable";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";

const WorkItemsTable = ({ type, id, isAutoConfig = false, itemsPerPage = 5, childRequirements }) => {

  const [data, setData] = useState([]);
  const [expandedRows, setExpandedRows] = useState([]);
  const [downChevronEnabled, setDownChevronEnabled] = useState(-1);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [childLoading, setChildLoading] = useState(null);
  const [selectedItems, setSelectedItems] = useState([]);
  const [checkAll, setCheckAll] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [sortConfig, setSortConfig] = useState({
    key: 'id',
    direction: 'ascending',
  });
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleteMultipleModal, setIsDeleteMultipleModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteParams, setDeleteParams] = useState({
    nodeId: null,
    nodeType: null,
  });
  const [status, setStatus] = useState([]);
  const [priority, setPriority] = useState([]);
  const [statusMap, setStatusMap] = useState({});
  const [priorityMap, setPriorityMap] = useState({});
  const [openDropdownIndex, setOpenDropdownIndex] = useState(null);
  const [dropdownStyle, setDropdownStyle] = useState('');
  const dropdownRefs = useRef([]);
  const { showAlert } = useContext(AlertContext);
  const [isOpenDropdownOn, setIsOpenDropdownOn] = useState(null);
  const router = useRouter();
  const pathname = usePathname();
  const projectId = pathname.split('/')[5];
  const currentType = pathname.split('/')[4];
  const splitedPath = pathname.split('/');
  const dropdownRef = useRef(null);
  const searchParams = useSearchParams();
  const [error, setError] = useState(null)

  const priorityBadgeColors = {
    Low: "bg-green-200 text-green-800",
    Medium: "bg-yellow-200 text-yellow-800",
    High: "bg-primary-200 text-primary-800",
    Critical: "bg-red-200 text-red-800",
  };

  const statusClasses = {
    "To Do": "bg-gray-100 text-gray-700",
    "In Progress": "bg-yellow-100 text-yellow-700",
    Review: "bg-primary-100 text-primary-700",
    Done: "bg-green-100 text-green-700",
    Blocked: "bg-red-100 text-red-700",
    default: "bg-gray-100 text-gray-700",
  };

  const typeBadgeColors = {
    Epic: "bg-purple-100 text-purple-700 max-w-14",
    UserStory: "bg-green-100 text-green-700 max-w-24",
    Task: "bg-primary-100 text-primary-700 max-w-14",
    default: "bg-white max-w-20",
  };



  const getPositionStyle = (index, level) => {
    const dropdown = dropdownRefs.current[parseInt(`${index}${level}`)];
    if (!dropdown) {
      return '';
    }

    const rect = dropdown.getBoundingClientRect();


    const isBottomVisible = (window.innerHeight - rect.bottom) < 400;

    return isBottomVisible ? `bottom-0.5 ${window.innerHeight - rect.bottom} ` : 'top-3';
  };

  const getNextChildName = () => {
    if (currentType.toLowerCase() === 'epic') {
      return 'UserStory'
    } else if (currentType.toLowerCase() === 'userstory') {
      return 'Task'
    } else if (currentType.toLowerCase() === 'task') {
      return 'Task'
    }
  }

  const childName = getNextChildName();
  let contentMessage;

  if (childName === "UserStory") {
    contentMessage = "userStory";
  } else if (childName === "Task") {
    contentMessage = "task";
  }

  const fetchRequirements = async () => {
    setLoading(true);
    try {
      const topLevelRequirements = await fetchChildRequirements(projectId, "Requirement");

      if (childRequirements && childRequirements.length > 0) {

        const existingIds = new Set(data.map(item => item.id));


        const newRequirements = childRequirements.filter(req => !existingIds.has(req.id));


        setData(prevData => [...prevData, ...newRequirements]);
      } else {

        setData(topLevelRequirements);
      }
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };


  useEffect(() => {
    fetchRequirements();
  }, [projectId, id, childRequirements])


  useEffect(() => {
    const fetchStatus = async () => {
      const response = await getAvailableStatus();
      setStatus(response);
    };

    fetchStatus();
  }, []);

  useEffect(() => {
    const fetchPriority = async () => {
      const response = await getAvailablePriorities();
      setPriority(response);
    };

    fetchPriority();
  }, []);

  const clearSearchQuery = () => {
    setSearchTerm('');
  };


  if (error) {
    return (
      <ErrorView
        title="Unable to Load Requirements"
        message={en.UnableToLoadRequirements}
        onRetry={() => fetchRequirementOnUpdate()}
        panelType='main'
      />
    );
  }


  const level0Table = (item, level) => {
    return (
      <>
        <td
          onClick={(e) => {
            e.stopPropagation();
          }}
          className="px-5 pt-2.5"
        >
          {(item.id != null) && (

            <button className={item.has_child ? "" : "opacity-50 cursor-not-allowed"} disabled={!item.has_child} onClick={() => handleExpandRow(item.id, level)}>
              {expandedRows.some(
                (row) => row.id === item.id && row.level === level
              ) ? (
                <CircleChevronDown width={18} height={18} />
              ) : (
                <CircleChevronRight width={18} height={18} />
              )}
            </button>
          )}
        </td>
        <td className="px-5 pt-1 " onClick={(e) => e.stopPropagation()}>
          <input
            id="default-checkbox"
            onClick={(e) => {
              e.stopPropagation();
            }}
            type="checkbox"
            checked={selectedItems.includes(item.id)}
            onChange={() => handleCheckboxChange(item.id)}
            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-0 cursor-pointer"
          />
        </td>

        <td scope="row" className="px-5 pt-1  table-val">
          {item.id}
        </td>
        <td
          className="pl-5 pt-1 w-full overflow-hidden whitespace-nowrap hover:underline cursor-pointer project-panel-content"
          onClick={() => {
            if (item.id != null)
              handleItemClick(item.type, item.id, true, {
                items: data,
                current_index: level,
              });
          }}
          onMouseEnter={() => setDownChevronEnabled(level)}
          onMouseLeave={() => setDownChevronEnabled(-1)}
        >
          <div className="table-val overflow-hidden whitespace-nowrap overflow-ellipsis w-[60vh]" title={item.title}>
            {item.title}
          </div>
        </td>
      </>
    );
  };

  const level1Table = (item, level = 0) => {
    return (
      <>
        <td className="px-5 pt-1" onClick={(e) => e.stopPropagation()}></td>
        <td
          onClick={(e) => {
            e.stopPropagation();
          }}
          className="px-5 pt-1"
        >

          <div className="flex items-center">
            {item.id != null && (
              <button
                className={item.has_child ? "" : "opacity-50 cursor-not-allowed"} // Style when disabled
                onClick={() => handleExpandRow(item.id, level)}
                disabled={!item.has_child} // Disable if no child
              >
                {expandedRows.some(row => row.id === item.id && row.level === level) ? (
                  <CircleChevronDown width={18} height={18} />
                ) : (
                  <CircleChevronRight width={18} height={18} />
                )}
              </button>
            )}
            <input
              id="default-checkbox"
              // style={{ marginTop: "-2px" }}
              onClick={(e) => {
                e.stopPropagation();
              }}
              type="checkbox"
              checked={selectedItems.includes(item.id)}
              onChange={() => handleCheckboxChange(item.id)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-0 cursor-pointer ml-2" // Adjusted spacing (ml-2 for margin-left)
            />
          </div>

        </td>
        <td scope="row" className="px-5 pt-1 table-val">
          {item.id}
        </td>
        <td
          className="pl-10 pt-1  overflow-hidden whitespace-nowrap hover:underline cursor-pointer"
          onClick={() => {
            if (item.id != null)
              handleItemClick(item.type, item.id, true, {
                items: data,
                current_index: level,
              });
          }}

          onMouseEnter={() => setDownChevronEnabled(level)}
          onMouseLeave={() => setDownChevronEnabled(-1)}
        >
          <div className="table-val overflow-hidden whitespace-nowrap overflow-ellipsis w-[60vh]" title={item.title}>
            {item.title}
          </div>
        </td>
      </>
    );
  };


  const level2Table = (item, level = 0) => {
    return (
      <>
        <td className="px-5 pt-1 "></td>
        <td className="px-5 pt-1 "></td>
        <td
          onClick={(e) => {
            e.stopPropagation();
          }}
          className="px-5 pt-1 "
        >
          <div className="flex items-center">
            {item.id != null && (
              <button
                className={item.has_child ? "" : "opacity-50 cursor-not-allowed"} // Style when disabled
                onClick={() => handleExpandRow(item.id, level)}
                disabled={!item.has_child}  // Disable if no child
              >
                {expandedRows.some(row => row.id === item.id && row.level === level) ? (
                  <CircleChevronDown width={18} height={18} />
                ) : (
                  <CircleChevronRight width={18} height={18} />
                )}
              </button>
            )}
            <input
              id="default-checkbox"
              style={{ marginTop: "-2px" }}
              onClick={(e) => {
                e.stopPropagation();
              }}
              type="checkbox"
              checked={selectedItems.includes(item.id)}
              onChange={() => handleCheckboxChange(item.id)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-0 cursor-pointer ml-2" // Adjusted spacing (ml-2 for margin-left)
            />
          </div>
        </td>
        <td
          className="px-20 pt-1 overflow-hidden whitespace-nowrap hover:underline cursor-pointer"
          onClick={() => {
            if (item.id != null)
              handleItemClick(item.type, item.id, true, {
                items: data,
                current_index: level,
              });
          }}

          onMouseEnter={() => setDownChevronEnabled(level)}
          onMouseLeave={() => setDownChevronEnabled(-1)}
        >
          <div className="overflow-hidden whitespace-nowrap overflow-ellipsis w-[60vh] table-val" title={item.title}>
            {item.title}
          </div>
        </td>
      </>
    );
  };


  const levelTable = (item, level) => {
    if (level === 0) return level0Table(item, level);
    else if (level === 1) return level1Table(item, level);
    else return level2Table(item, level);
  };

  const handleExpandRow = async (itemId, level) => {
    const isExpanded = expandedRows.some(
      (row) => row.id === itemId && row.level === level
    );
    if (isExpanded) {
      setExpandedRows(
        expandedRows.filter(
          (row) => !(row.id === itemId && row.level === level)
        )
      );
    } else {
      setExpandedRows([...expandedRows, { id: itemId, level }]);
      setChildLoading(itemId);
      try {
        const childData = await fetchChildRequirements(itemId, "Requirement");
        const addChildData = (items) => {
          return items.map((item) => {
            if (item.id === itemId) {
              return {
                ...item,
                subItems: childData.length
                  ? childData
                  : [
                    {
                      id: null,
                      title: "No child work items available",
                      status: "",
                      assignee_name: "",
                      subItems: [],
                    },
                  ],
              };
            }
            if (item.subItems) {
              return { ...item, subItems: addChildData(item.subItems) };
            }
            return item;
          });
        };
        setData(addChildData(data));
      } catch (error) {

      } finally {
        setChildLoading(null);
      }
    }
  };

  const handleSelectAll = () => {
    setCheckAll(!checkAll);
    if (!checkAll) {
      const allItemIds = filteredData.map((item) => item.id);
      setSelectedItems(allItemIds);
    } else {
      setSelectedItems([]);
    }
  };

  const handleChevronClick = (index, level, e) => {
    e.stopPropagation();
    e.preventDefault();

    // Toggle the dropdown for the clicked row
    if (openDropdownIndex === `${index}-${level}`) {
      setOpenDropdownIndex(null); // Close if already open
    } else {
      setOpenDropdownIndex(`${index}-${level}`); // Open the clicked row's dropdown
    }
  };


  const handleCheckboxChange = (itemId) => {
    if (selectedItems.includes(itemId)) {
      setSelectedItems(selectedItems.filter((id) => id !== itemId));
    } else {
      setSelectedItems([...selectedItems, itemId]);
    }
  };

  const handleDeleteMultipleNodes = async () => {
    setIsDeleting(true);
    try {
      let nodeType = 'Epic';
      await deleteMultipleNodes(selectedItems, nodeType);
      const topLevelRequirements = await fetchChildRequirements(projectId, "Requirement");
      //   const topLevelRequirements = await getTopLevelRequirements(projectId);
      setData(topLevelRequirements);
      setSelectedItems([]);
      showAlert("All the Nodes are Deleted Successfully", "success");
      setIsDeleteMultipleModal(false);
    } catch (error) {
      showAlert("Failed to Delete All Nodes", "danger");
    } finally {
      setIsDeleting(false);
    }
  };

  const updateStatus = async (status, nodeId, e) => {
    e.stopPropagation();
    const propertyName = "Status";
    try {
      await updateNodeByPriority(nodeId, propertyName, status);
      setStatusMap((prevStatusMap) => ({
        ...prevStatusMap,
        [nodeId]: status,
      }));
      setOpenDropdownIndex(null);
      showAlert("Status Updated successfully", "success");
    } catch (error) {
      showAlert("Updating Status Failed", "danger");
    }
  };

  const updatePriority = async (priority, nodeId, e) => {
    e.stopPropagation();
    const propertyName = "Priority";
    try {
      await updateNodeByPriority(nodeId, propertyName, priority);
      setPriorityMap((prevPriorityMap) => ({
        ...prevPriorityMap,
        [nodeId]: priority,
      }));
      showAlert("Priority Updated Successfully", "success");
    } catch (error) {
      showAlert("Updating Priority Failed", "danger");
    }
  };

  const confirmAndDelete = (id, type, e) => {
    e.preventDefault();
    e.stopPropagation();
    setDeleteParams({ id, type });
    setIsDeleteModalOpen(true);
  };

  const handleDelete = async () => {
    const { id, type } = deleteParams;
    setIsDeleting(true);
    try {
      await deleteNodeById(id, type);
      const topLevelRequirements = await fetchChildRequirements(projectId, "Requirement");
      //   const topLevelRequirements = await getTopLevelRequirements(projectId);
      setData(topLevelRequirements);
      showAlert("Requirement deleted successfully", "success");
    } catch (error) {
      showAlert("Failed to delete the Requirement!", "danger");
    } finally {
      setIsDeleting(false);
      setIsDeleteModalOpen(false);
    }
  };
  const handleBtn = () => {
    showAlert("Functionality was not implemented", "info");
  };

  const handleItemClick = async (type, id, updateQuery = true, logs = null) => {
    if (updateQuery) {
      window.location.href = `/${splitedPath[1]}/${splitedPath[2]}/${splitedPath[3]}/${type}/${id}`;
    }
  };

  const renderRows = (items, level = 0) => {
    return items.map((item, index) => (
      <React.Fragment key={item.id || `no-child-${level}`}>
        <tr
          className="bg-white border-b hover:bg-gray-50 cursor-pointer relative"
        >
          {levelTable(item, level)}
          <td className="table-content px-5 pt-1">
            <span
              className={`py-0.5 typography-body-sm font-weight-semibold rounded-md ${typeBadgeColors[item.type] || typeBadgeColors.default
                } items-center w-fit flex justify-center max-w-14`}
            >
              <Square fill="currentColor" className="w-2 h-2 ml-2" />
              <span className="pl-1 mr-3 whitespace-normal break-words ">{item.type || "Empty"}</span>
            </span>
          </td>
          <td scope="col" className=" table-content px-5 pt-1">
            <button ref={(el) => (dropdownRefs.current[parseInt(`${item.id}${level}`)] = el)}

              onClick={(e) => {

                e.stopPropagation();
                e.preventDefault();
                setIsOpenDropdownOn(isOpenDropdownOn === `${item.id}-${level}` ? null : `${item.id}-${level}`);
                setOpenDropdownIndex(null);
                setDropdownStyle(getPositionStyle(item.id, level));

              }}
            >
              <MoreVertical />
            </button>
            {isOpenDropdownOn === `${item.id}-${level}` && (
              <div
                ref={dropdownRef}
                className={`absolute right-10 bg-white shadow-lg rounded-lg my-4 z-10 border border-blue-500 ${dropdownStyle}`}
              >
                <button
                  onClick={handleBtn}
                  className="block px-4 py-2 text-left text-gray-800 hover:bg-gray-100 w-full"
                >
                  Edit
                </button>
                <button
                  type="button"
                  onClick={(e) => confirmAndDelete(item.id, item.type, e)}
                  className="block px-4 py-2 text-left text-gray-800 hover:bg-gray-100 w-full"
                >
                  Delete
                </button>

                <button
                  onClick={handleBtn}
                  className="block px-4 py-2 text-left text-gray-800 hover:bg-gray-100 w-full"
                >
                  History
                </button>
              </div>
            )}

          </td>

        </tr>



        {expandedRows.some(
          (row) => row.id === item.id && row.level === level
        ) &&
          childLoading === item.id && (
            <tr key={`loading-${item.id}`}>
              <td colSpan="7" className="py-2 px-4 border-b text-center">
                Loading...
              </td>
            </tr>
          )}
        {expandedRows.some(
          (row) => row.id === item.id && row.level === level
        ) &&
          item.subItems &&
          renderRows(item.subItems, level + 1)}
      </React.Fragment>
    ));
  };

  let filteredData = data.filter((item) =>
    (item.id && item.id.toString().includes(searchTerm)) ||
    (item.title && item.title.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (item.type && item.type.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (item.status && item.status.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (item.assignee_name && item.assignee_name.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const totalPages = Math.ceil(filteredData.length / itemsPerPage);

  // const itemsPerPage = 20;

  // Slice data based on pagination
  // filteredData = filteredData.slice(
  //   (currentPage - 1) * itemsPerPage,
  //   currentPage * itemsPerPage
  // );

  // Sort the sliced filteredData
  filteredData.sort((a, b) => {
    if (a[sortConfig.key] < b[sortConfig.key]) {
      return sortConfig.direction === 'ascending' ? -1 : 1;
    }
    if (a[sortConfig.key] > b[sortConfig.key]) {
      return sortConfig.direction === 'ascending' ? 1 : -1;
    }
    return 0;
  });

  const requestSort = (key) => {
    let direction = "ascending";
    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    }
    setSortConfig({ key, direction });
  };
  return (
    <div className="w-full h-full flex flex-col bg-white"
      onMouseLeave={(e) => {
        if (isOpenDropdownOn) setIsOpenDropdownOn(null);
        if (openDropdownIndex) setOpenDropdownIndex(null);
      }}
      onClick={(e) => {
        if (isOpenDropdownOn) setIsOpenDropdownOn(null);
        if (openDropdownIndex) setOpenDropdownIndex(null);
      }}>
      <div className="w-full flex justify-between my-2 items-center ">
        <div className="project-panel-heading mr-3">{getNextChildName()}</div>
        <div className="flex">

          {selectedItems.length > 0 && (
            <DynamicButton
              variant="square"
              size="sqDefault"
              tooltip="Delete All Nodes"
              placement="bottom-end"
              icon={Trash}
              onClick={() => {
                setIsDeleteMultipleModal(true)
              }}
            />
          )}
          {data.length > 0 && (
            <Search
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
            />
          )}
        </div>
      </div>
      {loading ? (
        <>
          <div><Loading2 /></div>
        </>
      ) : (
        filteredData.length > 0 ? <>
          <div className="mt-1" >
            <WorkItemTable
              data={filteredData}
              checkAll={checkAll}
              totalItems={filteredData.length}
              currentPage={currentPage}
              pageSize={pageSize}
              onPageChange={setCurrentPage}
              onPageSizeChange={(newPageSize) => {
                  setPageSize(newPageSize);
                  setCurrentPage(1);
                }}
              handleSelectAll={handleSelectAll}
              requestSort={requestSort}
              sortConfig={sortConfig}
              handleExpandRow={handleExpandRow}
              expandedRows={expandedRows}
              handleItemClick={handleItemClick}
              handleCheckboxChange={handleCheckboxChange}
              selectedItems={selectedItems}
              typeBadgeColors={typeBadgeColors}
              handleChevronClick={handleChevronClick}
              openDropdownIndex={openDropdownIndex}
              isOpenDropdownOn={isOpenDropdownOn}
              setIsOpenDropdownOn={setIsOpenDropdownOn}
              confirmAndDelete={confirmAndDelete}
              StoryLevel={1}
            />
          </div>

        </> : (
          <div className='flex justify-center items-center text-center'>
            {searchTerm ? (
              <EmptyStateView type="noSearchResult" onClick={clearSearchQuery} />
            ) : (
              <EmptyStateView type={contentMessage} />
            )}
          </div>
        )
      )}
      {isDeleteModalOpen && (
        <DeleteProjectModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          onDelete={handleDelete}
          isDeleting={isDeleting}
          type="requirements"
        />
      )}
      {isDeleteMultipleModal && (
        <DeleteProjectModal
          isOpen={isDeleteMultipleModal}
          onClose={() => setIsDeleteMultipleModal(false)}
          onDelete={handleDeleteMultipleNodes}
          isDeleting={isDeleting}
          type="Multiple Requirements"
        />
      )}
    </div>
  );
};

export default WorkItemsTable;
