"use client";
import React, {
  useState,
  useContext,
  useEffect,
  useRef,
  useCallback,
} from "react";
import Accordion from "./Accordion";
import CreateProjectModal from "../Modal/CreateProjectModal";
import { StateContext } from "../Context/StateContext";
import {
  updateNodeByPriority,
  deleteTask,
  getReconfigNodeSectionFlag,
} from "@/utils/api";
import {
  fetchNodeBasedOnDataModelById,
  deleteNodeById,
  fetchTaskCounts,
  getProjectLLMCosts,
  getProjectCounts,
} from "@/utils/api";
import DeleteProjectModal from "../Modal/DeleteProjectModal";
import { AlertContext } from "../NotificationAlertService/AlertList";
import { PanelContext } from "@/components/Context/PanelContext";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import ConfigureModal from "../Modal/ConfigureModel";
import { TopBarContext } from "../Context/TopBarContext";
import PropertiesRenderer from "../UiMetadata/PropertiesRenderer";
import en from "../../en.json";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import ErrorView from "@/components/Modal/ErrorViewModal";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import CustomDropdown from "@/components/UIComponents/Dropdowns/CustomDropdown";
import { Settings, BookOpen, Trash2, Edit2 } from "lucide-react";
import NavigationTree from "@/components/Modal/NavigationTreeComponent";
import { TwoColumnSkeletonLoader } from "@/components/UIComponents/Loaders/LoaderGroup";
import { useResponsiveDimensions } from "@/utils/responsiveness";
import { ArrowUpRight, RefreshCw, AlertCircle, X } from "lucide-react";
import { configureNodeWithAgent } from "@/utils/api";
import { ExecutionContext } from "../Context/ExecutionContext";
import Cookies from "js-cookie";
import { TOOLTIP_CONTENT } from "@/utils/constant/tooltip";
import { BootstrapTooltip } from "../UIComponents/ToolTip/Tooltip-material-ui";
import ReConfigureModal from "../Modal/ReconfigureModel";
import { DynamicReconfigButton } from "@/components/UIComponents/Buttons/DynamicReconfigButton";
import { DriverContext } from "../Context/DriverContext";
import { useUser } from "@/components/Context/UserContext";
import LockedTabIndicator from "../UIComponents/LockedTabIndicator";

const OverviewTabContent = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [projectCost, setProjectCost] = useState(null);
  const [costLoading, setCostLoading] = useState(true);
  const [isLoadingCounts, setIsLoadingCounts] = useState(true);
  const [totalNodes, setTotalNodes] = useState(0);
  const [completedNodes, setCompletedNodes] = useState(0);
  const [progressSteps, setProgressSteps] = useState([]);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [error, setError] = useState(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [modalType, setModalType] = useState("Create");
  const [configureModel, setConfigureModel] = useState(false);
  const [isNodeType, setNodeType] = useState(null);
  const [configureNodeId, setConfigureNodeId] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isLoadingTasks, setIsLoadingTasks] = useState(true);
  const [taskCounts, setTaskCounts] = useState(null);
  const { setIsVertCollapse } = useContext(StateContext);
  const [loadingAutoConfigure, setLoadingAutoConfigure] = useState(false);
  const [reconfigureModel, setReconfigureModel] = useState(false);
  const [loadingReConfigure, setLoadingReconfigure] = useState(false);
  const { showAlert } = useContext(AlertContext);
  const { deleteTab, updateTabTitle, tabs } = useContext(TopBarContext);
  const [projectData, setProjectData] = useState(null);
  // Add new state for progress stepper
  const [selectedStepDetails, setSelectedStepDetails] = useState(null);
  const { updateProjectTitle } = useContext(PanelContext);
  const dropdownRef = useRef(null);
  const [confirmReconfigModal, setConfirmReconfigModal] = useState(false);
  // const [isContentVisible, setIsContentVisible] = useState(true);
  const { mainContentWidth, contentHeight, calculateDimensions } =
    useResponsiveDimensions();
  const {
    setCurrentTaskId,
    setConfiglabel,
    currentTaskId,
    enableReconfig,
    configStatus,
    reconfigStatus,
    setEnableReconfig,
  } = useContext(ExecutionContext);

  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();
  const type = pathname.split("/")[1];
  const id = parseInt(pathname.split("/")[2], 10);
  const detailsRef = useRef(null);
  const { is_free_user, is_having_permission } = useUser();

  const { prepareDriver, handleDriveTour } = useContext(DriverContext);

  const handleStepClick = useCallback(
    (stepId) => {
      const selectedStep = progressSteps.find((step) => step.id === stepId);
      setSelectedStepDetails(selectedStep);

      // Wait for details to render, then scroll
      setTimeout(() => {
        if (detailsRef.current) {
          detailsRef.current.scrollIntoView({
            behavior: "smooth",
            block: "nearest",
          });
        }
      }, 100);
    },
    [progressSteps, setSelectedStepDetails, detailsRef]
  );

  useEffect(() => {
    const tenantId = Cookies.get("tenant_id");
    const loadProjectCost = async () => {
      if (id) {
        try {
          setCostLoading(true);
          const costData = await getProjectLLMCosts(id, tenantId);
          setProjectCost(costData);
        } catch (error) {

        } finally {
          setCostLoading(false);
        }
      }
    };

    loadProjectCost();
  }, [id, pathname, searchParams]); // Added pathname and searchParams to dependencies

  const navigateToSection = (route) => {
    // Construct the full path using the current project ID
    const projectId = pathname.split("/")[2];
    const fullPath = `/project/${projectId}${route}`;
    router.push(fullPath);
  };

  useEffect(() => {
    const getReconfigStatus = async () => {
      try {
        const reconfigData = await getReconfigNodeSectionFlag(id);
        if (reconfigData?.project_reconfig) {
          setEnableReconfig(true);
        }
      } catch (error) {

      }
    };
    getReconfigStatus();
  }, [id]);

  useEffect(() => {
    calculateDimensions();
    setEnableReconfig(false);
    if (reconfigStatus.project_reconfig) {
      setEnableReconfig(true);
    }
  }, [calculateDimensions]);

  const processCountsToSteps = (type, counts) => {
    switch (type) {
      case "requirement":
        const {
          epicCount = 0,
          userStoryCount = 0,
          taskCount = 0,
        } = counts?.counts || {};

        return {
          id: 1,
          title: "Requirements",
          counts: {
            counts: {
              epicCount,
              userStoryCount,
              taskCount,
            },
          },
          completedNodes:
            (epicCount > 0 ? 1 : 0) +
            (userStoryCount > 0 ? 1 : 0) +
            (taskCount > 0 ? 1 : 0),
          totalNodes: 3,
          status:
            epicCount + userStoryCount + taskCount > 0
              ? "completed"
              : "pending",
          description: "Requirements analysis and gathering phase",
          route: "/requirements",
          color: "bg-green-500",
          barColor: "black",
          detailsList: [
            `Epics (${epicCount}): ${epicCount > 0 ? "Complete" : "Pending"}`,
            `User Stories (${userStoryCount}): ${
              userStoryCount > 0 ? "Complete" : "Pending"
            }`,
            `Tasks (${taskCount}): ${taskCount > 0 ? "Complete" : "Pending"}`,
          ],
        };

      case "architecture":
        const {
          systemContextCount = 0,
          containerCount = 0,
          componentCount = 0,
          designCount = 0,
          interfaceCount = 0,
          deploymentCount = 0,
        } = counts?.counts || {};

        return {
          id: 2,
          title: "Architecture",
          counts: {
            counts: {
              systemContextCount,
              containerCount,
              componentCount,
              designCount,
              interfaceCount,
              deploymentCount,
            },
          },
          completedNodes:
            (systemContextCount > 0 ? 1 : 0) +
            (containerCount > 0 ? 1 : 0) +
            (componentCount > 0 ? 1 : 0) +
            (designCount > 0 ? 1 : 0) +
            (interfaceCount > 0 ? 1 : 0) +
            (deploymentCount > 0 ? 1 : 0),
          totalNodes: 6,
          status:
            systemContextCount +
              containerCount +
              componentCount +
              designCount +
              interfaceCount +
              deploymentCount >
            0
              ? "in-progress"
              : "pending",
          description: "System architecture and design phase",
          route: "/architecture",
          color: "bg-blue-500",
          barColor: "black",
          detailsList: [
            `System Context (${systemContextCount}): ${
              systemContextCount > 0 ? "Complete" : "Pending"
            }`,
            `Container (${containerCount}): ${
              containerCount > 0 ? "Complete" : "Pending"
            }`,
            `Component (${componentCount}): ${
              componentCount > 0 ? "Complete" : "Pending"
            }`,
            `Design (${designCount}): ${
              designCount > 0 ? "Complete" : "Pending"
            }`,
            `Interface (${interfaceCount}): ${
              interfaceCount > 0 ? "Complete" : "Pending"
            }`,
            `Deployment (${deploymentCount}): ${
              deploymentCount > 0 ? "Complete" : "Pending"
            }`,
          ],
        };

      case "code":
        return {
          id: 3,
          title: "Code",
          counts: {
            counts: null,
          },
          completedNodes: 0,
          totalNodes: 1,
          status: "pending",
          description: "Code implementation phase",
          route: "/code",
          color: "bg-purple-500",
          barColor: "black",
          detailsList: [`Unit Tests (0): Pending`, `Code Review (0): Pending`],
        };
      case "deployment":
        const { deploymentCount: deplCount = 0 } = counts?.counts || {};

        return {
          id: 4,
          title: "Deployment",
          counts: {
            counts: {
              deploymentCount: deplCount,
            },
          },
          completedNodes: deplCount > 0 ? 1 : 0,
          totalNodes: 1,
          status: deplCount > 0 ? "in-progress" : "pending",
          description: "Deployment preparation and planning phase",
          route: "/deployment",
          color: "bg-orange-500",
          barColor: "black",
          detailsList: [
            `Deployment (${deplCount}): ${
              deplCount > 0 ? "Complete" : "Pending"
            }`,
          ],
        };

      default:
        return {
          id: 0,
          title: "",
          counts: {
            counts: null,
          },
          completedNodes: 0,
          totalNodes: 0,
          status: "pending",
          description: "",
          route: "",
          color: "bg-gray-500",
          barColor: "black",
          detailsList: [],
        };
    }
  };

  useEffect(() => {
    if (id) {
      const fetchAllCounts = async () => {
        setIsLoadingCounts(true);
        const types = ["requirement", "architecture", "code", "deployment"];
        const steps = [];
        let totalNodesCount = 0;
        let completedNodesCount = 0;

        try {
          for (const type of types) {
            try {
              let counts;
              if (type !== "code") {
                counts = await getProjectCounts(id, type);
              }
              const step = processCountsToSteps(type, counts);
              if (step) {
                steps.push(step);
                totalNodesCount += step.totalNodes;
                completedNodesCount += step.completedNodes;
              }
            } catch (error) {

            }
          }

          setTotalNodes(totalNodesCount);
          setCompletedNodes(completedNodesCount);
          setProgressSteps(steps);
        } finally {
          setIsLoadingCounts(false);
        }
      };

      fetchAllCounts();
    }
  }, [id]);

  const getTotalNodeCount = (type, counts) => {
    if (!counts) return "0";

    switch (type.toLowerCase()) {
      case "requirements": {
        const { epicCount = 0, userStoryCount = 0, taskCount = 0 } = counts;
        return `${epicCount + userStoryCount + taskCount}`;
      }
      case "architecture": {
        const {
          systemContextCount = 0,
          containerCount = 0,
          componentCount = 0,
          designCount = 0,
          interfaceCount = 0,
          deploymentCount = 0,
        } = counts;
        return `${
          systemContextCount +
          containerCount +
          componentCount +
          designCount +
          interfaceCount +
          deploymentCount
        }`;
      }
      case "code":
        return "0";
      case "deployment": {
        const { deploymentCount = 0 } = counts;
        return `${deploymentCount}`;
      }

      default:
        return "0";
    }
  };

  const calculateProgress = (type, counts) => {
    if (!counts) return 0;
    switch (type.toLowerCase()) {
      case "requirements": {
        const { epicCount = 0, userStoryCount = 0, taskCount = 0 } = counts;
        if (epicCount > 0 && userStoryCount > 0 && taskCount > 0) return 100;
        if (epicCount > 0 && userStoryCount > 0) return 75;
        if (epicCount > 0) return 50;
        return 0;
      }
      case "architecture": {
        const {
          systemContextCount = 0,
          containerCount = 0,
          componentCount = 0,
          deploymentCount = 0,
        } = counts;
        if (deploymentCount > 0) return 100;
        if (componentCount > 0) return 90;
        if (containerCount > 0) return 60;
        if (systemContextCount > 0) return 20;
        return 0;
      }
      case "code":
        return {
          id: 3,
          title: "Code",
          counts: {
            counts: null,
          },
          completedNodes: 0,
          totalNodes: 1,
          status: "pending",
          description: "Code implementation phase",
          route: "/code",
          color: "bg-purple-500",
          barColor: "black",
          detailsList: [
            `Implementation (0): Pending`,
            `Unit Tests (0): Pending`,
            `Integration Tests (0): Pending`,
            `Code Review (0): Pending`,
          ],
        };
      case "deployment": {
        const { deploymentCount = 0 } = counts;
        return deploymentCount > 0 ? 100 : 0;
      }

      default:
        return 0;
    }
  };

  const getStatusColor = (step) => {
    return {
      dotColor: step.color || "bg-gray-400",
      barColor: step.barColor || "gray",
    };
  };

  const stepsWithPercentage = progressSteps.map((step) => {
    // Get the progress percentage based on the counts
    const progress = calculateProgress(
      step.title.toLowerCase(),
      step.counts?.counts
    );

    return {
      id: step.id,
      title: step.title,
      counts: step.counts,
      completedNodes: step.completedNodes,
      totalNodes: step.totalNodes,
      description: step.description,
      route: step.route,
      detailsList: step.detailsList,
      color: step.color,
      progress: progress,
      statusColor: getStatusColor(step),
    };
  });

  // Auto-select the first step when steps are loaded
  useEffect(() => {
    if (
      !selectedStepDetails &&
      stepsWithPercentage &&
      stepsWithPercentage.length > 0
    ) {
      handleStepClick(stepsWithPercentage[0].id);
    }
  }, [stepsWithPercentage, selectedStepDetails, handleStepClick]);

  const getProjectDetails = async () => {
    if (type === "project") {
      try {
        const project = await fetchNodeBasedOnDataModelById(id, "Project");
        setProjectData(project);

        setLoading(false);
      } catch (error) {

        setError(error);
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    setLoading(true);
    getProjectDetails();
  }, [pathname, id, router, type]);

  useEffect(() => {
    const fetchTasks = async () => {
      try {
        const counts = await fetchTaskCounts(id);
        setTaskCounts(counts);
      } catch (error) {

      } finally {
        setIsLoadingTasks(false);
      }
    };

    if (id) {
      fetchTasks();
    }
  }, [id]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    if (isDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isDropdownOpen]);

  const handleConfigureClick = (id, type) => {
    setNodeType(type);
    setConfigureNodeId(id);
    setConfigureModel(true);
  };
  const handleReconfigureClick = () => {
    setReconfigureModel(true);
  };
  const handleReconfigureClose = () => {
    setReconfigureModel(false);
  };

  const handleReconfigure = () => {
    setConfirmReconfigModal(true);
  };

  const handleCloseModal = () => {
    setConfigureModel(false);
  };

  const updateProject = () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", projectData.node.id);
    newSearchParams.set("node_type", "project");
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const confirmAndDelete = () => {
    setIsDeleteModalOpen(true);
  };

  useEffect(() => {
    if (searchParams.get("AutoConfigure")) {
      handleConfigureClick(id, type);
      const url = new URL(window.location.href);
      url.searchParams.delete("AutoConfigure");
      window.history.pushState({}, "", url);
    }
  }, [searchParams]);

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      const response = await deleteNodeById(id, "Project");

      if (response.status >= 200 && response.status < 300) {
        showAlert("Project deleted successfully", "success");
        deleteTab(id);

        const deletedTabIndex = tabs.findIndex((tab) =>
          tab.href.includes(`/project/${id}`)
        );
        let newCurrentTabIndex =
          deletedTabIndex === tabs.length - 1
            ? deletedTabIndex - 1
            : deletedTabIndex + 1;

        if (deletedTabIndex === 0) {
          router.push("/home");
        } else {
          const newCurrentTab = tabs[newCurrentTabIndex]?.href;
          if (newCurrentTab) {
            router.push(newCurrentTab);
          } else {
            router.push("/home");
          }
        }
        setIsDeleteModalOpen(false);
      } else {
        throw new Error(response.statusText || "Unknown error");
      }
    } catch (error) {

      showAlert("Failed to delete the project!", "danger");
    } finally {
      setIsDeleting(false);
    }
  };

  const openEditModal = () => {
    setModalType("Edit");
    setIsModalOpen(true);
  };

  const closeEditModal = () => {
    setIsModalOpen(false);
  };

  const handlePropertyUpdate = async (key, value) => {
    try {
      const response = await updateNodeByPriority(id, key, value);

      if (response === "success") {
        setProjectData((prev) => ({
          ...prev,
          node: {
            ...prev.node,
            [key]: value,
          },
        }));
        showAlert("Content updated successfully", "success");
      } else {
        throw new Error("Update failed");
      }
    } catch (error) {

      showAlert("Failed to update content", "error");
    }
  };

  const ProgressStepperSkeleton = () => {
    const steps = [
      { label: "Requirements", nodes: "0 nodes" },
      { label: "Architecture", nodes: "1 nodes" },
      { label: "Deployment", nodes: "0 nodes" },
      { label: "Code", nodes: "0 nodes" },
    ];

    return (
      <div className="w-full">
        <div className="grid grid-cols-4 gap-3">
          {steps.map((step, index) => (
            <div key={index} className="flex-1">
              {/* Card skeleton */}
              <div className="bg-white border border-gray-100 rounded-lg p-3 shadow-sm">
                <div className="flex justify-between items-center mb-2">
                  <div className="h-4 w-20 bg-gray-100 animate-pulse rounded" />
                  <div className="h-11 w-11 bg-gray-100 animate-pulse rounded-full" />
                </div>
                <div className="h-3 w-28 bg-gray-100 animate-pulse rounded mt-1" />
              </div>
            </div>
          ))}
        </div>
        {/* Details skeleton */}
        <div className="mt-4 bg-white border border-gray-100 rounded-lg p-3 shadow-sm">
          <div className="h-5 w-36 bg-gray-100 animate-pulse rounded mb-3" />
          <div className="space-y-2">
            <div className="h-3 w-full bg-gray-100 animate-pulse rounded" />
            <div className="h-3 w-3/4 bg-gray-100 animate-pulse rounded" />
            <div className="h-3 w-5/6 bg-gray-100 animate-pulse rounded" />
          </div>
        </div>
      </div>
    );
  };

  {
    /*const runOverviewDriver = () => {
    prepareDriver("overview");
    handleDriveTour(0);
  }*/
  }

  const handleUpdateResponse = async (response) => {
    setProjectData({
      ...projectData,
      node: { id: response.id, ...response.properties },
    });
    updateTabTitle(response.id, response.properties.Title);
    await updateProjectTitle(response.id, response.properties.Title);
    showAlert("Successfully Updated!", "success");
  };

  const handleDropdownToggle = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const handleViewPastDiscussion = () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("view_past_discussions", "true");
    newSearchParams.set("node_id", projectData.node.id);
    newSearchParams.set("discussion_type", "project");
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const handleCloseDetails = () => {
    setSelectedStepDetails(null);
  };

  if (loading) {
    return <TwoColumnSkeletonLoader />;
  }

  if (error) {
    return (
      <ErrorView
        title="Unable to Load Overview"
        message={en.UnableToLoadOverview}
        onRetry={() => getProjectDetails()}
        panelType="main"
      />
    );
  }

  if (!projectData || !projectData.node) {
    return (
      <div className="noProjectFound">
        <EmptyStateView type="projects" />
      </div>
    );
  }

  const { node, ui_metadata } = projectData;

  const renderAccordions = () => {
    const accordions = [];
    const skipFields = ["Type", "Title", "configuration_state"];
    const priorityFields = ["Description", "Scope"];

    // First, add Description and Scope if they exist
    priorityFields.forEach((field) => {
      if (node[field] && ui_metadata.Project[field]) {
        const { Label, display_type } = ui_metadata.Project[field];
        if (!display_type || display_type !== "hidden") {
          accordions.push(
            <Accordion
              key={field}
              title={Label}
              content={node[field]}
              preview={`Open this for ${Label.toLowerCase()}`}
              renderToHtml={display_type === "rich_text"}
              defaultOpen={true}
            />
          );
        }
      }
    });

    // Then add the rest of the fields
    for (const [key, value] of Object.entries(node)) {
      if (
        !skipFields.includes(key) &&
        !priorityFields.includes(key) &&
        ui_metadata.Project[key]
      ) {
        const { Label, display_type } = ui_metadata.Project[key];
        if (!display_type || display_type !== "hidden") {
          accordions.push(
            <Accordion
              key={key}
              title={Label}
              content={value}
              preview={`Open this for ${Label.toLowerCase()}`}
              renderToHtml={display_type === "rich_text"}
              defaultOpen={true}
            />
          );
        }
      }
    }

    return accordions;
  };

  const menuOptions = [
    {
      label: "History",
      icon: BookOpen,
      onClick: handleViewPastDiscussion,
      tooltip: TOOLTIP_CONTENT.overview.viewPastDiscussion,
    },
    {
      label: "Delete",
      icon: Trash2,
      onClick: confirmAndDelete,
      variant: "danger",
      tooltip: !is_having_permission()
        ? "You don't have permission"
        : TOOLTIP_CONTENT.overview.delete,
      disabled: !is_having_permission(),
    },
    {
      label: "Edit",
      icon: Edit2,
      onClick: openEditModal,
      tooltip: !is_having_permission()
        ? "You don't have permission"
        : TOOLTIP_CONTENT.overview.edit,
      disabled: !is_having_permission(),
    },
  ];

  const totalNodeCount = progressSteps.reduce((total, step) => {
    // Get the number part from the nodes string (e.g., "51 nodes" -> 51)
    const nodeCount = parseInt(step.totalNodes) || 0;
    return total + nodeCount;
  }, 0);

  const treeData = Object.entries(projectData?.ui_metadata?.Project || {})
    .filter(
      ([key, value]) =>
        !["Title", "Type"].includes(key) &&
        value.hidden !== true &&
        projectData?.node?.hasOwnProperty(key) // Check if key exists in properties
    )
    .map(([key, value]) => {
      const labelOrKey = value?.Label || key;

      // Function to split camelCase words
      const splitCamelCase = (str) => str.replace(/([a-z])([A-Z])/g, "$1 $2");

      return {
        id: `${splitCamelCase(labelOrKey)
          .toLowerCase()
          .replace(/[_\s]+/g, "-")}`, // Convert to kebab-case
        name: splitCamelCase(labelOrKey).replace(/[_-]/g, " "), // Convert to readable format with spaces
        children: [], // Add children if hierarchical data exists
      };
    });

  const handleScrollToSection = (id) => {
    const element = document.getElementById(id);

    const mainContent = document.getElementById("main-content");

    if (element && mainContent) {
      const topOffset = element.offsetTop;
      mainContent.scrollTo({
        top: topOffset - 80, // Account for header
        behavior: "smooth",
      });
    }
  };

  const confirmReconfigure = async () => {
    let user_level = 1;

    try {
      const data = await configureNodeWithAgent({
        node_id: id,
        node_type: "project",
        user_level: user_level,
        project_id: id,
        type: "re-config",
        configurations: {
          configure: true,
          projectId: id,
          work_item: {
            configure: true,
          },
          requirements: {
            configure: true,
            epic: {
              configure: true,
              user_story: {
                configure: true,
                task: {
                  configure: true,
                },
              },
            },
          },
          architecture: {
            configure: true,
            architectural_requirements: {
              configure: true,
            },
            system_context: {
              configure: true,
              container: {
                configure: true,
                components: {
                  configure: true,
                  design: {
                    configure: true,
                  },
                  interface: {
                    configure: true,
                  },
                },
              },
            },
          },
        },
      });

      if (
        data.error ===
        "You already have a task in progress. Please cancel the current task to start a new one."
      ) {
        if (currentTaskId) {
          if (configStatus[currentTaskId].task_status === "complete") {
            setCurrentTaskId(data.task_id);
            await deleteTask(currentTaskId, true);
            await confirmReconfigure();
            setConfiglabel("re-config");
            setConfirmReconfigModal(false);
          } else {
            showAlert(
              "You already have an auto-configure task in progress. Please cancel the current task to start a new one.",
              "warning"
            );
          }
        }
      } else {
        showAlert("Re-configuration task started successfully! ", "success");
        setCurrentTaskId(data.task_id);
        setConfiglabel("re-config");
        setConfirmReconfigModal(false);
      }
    } catch (error) {

    }
  };

  const ConfirmReconfigModal = () => (
    <div className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={() => setConfirmReconfigModal(false)}
      />

      {/* Modal */}
      <div className="relative bg-white rounded-lg shadow-xl w-full max-w-md mx-4 overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="typography-heading-4 font-weight-semibold text-gray-900">
            Re-Configure Project
          </h2>
          <DynamicButton
            variant="ghost"
            size="sqSmall"
            icon={X}
            onClick={() => setConfirmReconfigModal(false)}
            tooltip="Close"
          />
        </div>

        {/* Content */}
        <div className="p-4 space-y-4">
          <p className="text-gray-600">
            Are you sure you want to proceed with the reconfiguration?
          </p>

          {/* Warning Alert */}
          <div className="flex items-start p-4 rounded-lg bg-red-50 text-red-800">
            <AlertCircle className="w-5 h-5 mt-0.5 flex-shrink-0" />
            <p className="ml-3 typography-body-sm">
              This process will start configuring all the existing nodes.
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 px-4 py-3 bg-gray-50">
          <DynamicButton
            variant="secondary"
            onClick={() => setConfirmReconfigModal(false)}
            text="Cancel"
          />
          <DynamicButton
            variant="primaryLegacy"
            isLoading={isDeleting}
            disabled={isDeleting}
            onClick={() => confirmReconfigure()}
            text={"Confirm"}
          />
        </div>
      </div>
    </div>
  );

  return (
    <>
      {isDeleteModalOpen && (
        <DeleteProjectModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          onDelete={handleDelete}
          isDeleting={isDeleting}
          type="project"
        />
      )}

      {isModalOpen && (
        <CreateProjectModal
          isOpen={isModalOpen}
          onClose={closeEditModal}
          type="Project"
          isEdit={modalType === "Edit"}
          initialValues={{
            id: node.id,
            Title: node.Title || "",
            Description: node.Description || "",
            Scope: node.Scope || "",
          }}
          onUpdateResponse={handleUpdateResponse}
        />
      )}

      {confirmReconfigModal && <ConfirmReconfigModal />}

      {configureModel && (
        <ConfigureModal
          id={id}
          type={"project"}
          isNodeType={isNodeType}
          requirementId={configureNodeId}
          closeModal={handleCloseModal}
          setLoadingAutoConfigure={setLoadingAutoConfigure}
          onSubmitSuccess={() => {
            showAlert(
              `${isNodeType} configuration initiated successfully`,
              "success"
            );
            setIsVertCollapse(false);
          }}
        />
      )}

      {reconfigureModel && (
        <ReConfigureModal
          id={id}
          type={"project"}
          isNodeType={"project"}
          requirementId={configureNodeId}
          closeModal={handleReconfigureClose}
          setLoadingAutoConfigure={setLoadingReconfigure}
          onSubmitSuccess={() => {
            showAlert(
              `project re-configuration initiated successfully`,
              "success"
            );
            setIsVertCollapse(false);
          }}
        />
      )}

      <div className="relative flex max-h-[78vh] overflow-hidden bg-white-50">
        <div>
          {treeData && (
            <NavigationTree
              treeData={treeData}
              handleScrollToSection={handleScrollToSection}
            />
          )}
        </div>

        <main
          id="main-content"
          className={`
          flex-1
          relative
          overflow-y-auto
          overflow-x-hidden
          transition-all
          duration-300
          ease-in-out
        `}
          style={{
            width: mainContentWidth,
          }}
        >
          <div>
            <div className="overviewContainerPadding">
              <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-3 mb-4">
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-2">
                  <div>
                    <h1 className="typography-heading-2 font-weight-semibold text-gray-800 mb-1">
                      {node.Title || "Project Name"}
                    </h1>
                    <div className="flex flex-wrap items-center gap-2">
                      <div className="flex items-center gap-1 px-2 py-0.5 bg-blue-50 rounded-full">
                        <span className="typography-body-sm font-weight-medium text-blue-700">
                          {progressSteps.reduce((total, step) => {
                            if (step.title === "Requirements") {
                              return (
                                total +
                                (step.counts?.counts?.epicCount || 0) +
                                (step.counts?.counts?.userStoryCount || 0) +
                                (step.counts?.counts?.taskCount || 0)
                              );
                            }
                            if (step.title === "Architecture") {
                              return (
                                total +
                                (step.counts?.counts?.systemContextCount || 0) +
                                (step.counts?.counts?.containerCount || 0) +
                                (step.counts?.counts?.componentCount || 0) +
                                (step.counts?.counts?.deploymentCount || 0)
                              );
                            }
                            if (step.title === "Deployment") {
                              return (
                                total +
                                (step.counts?.counts?.deploymentCount || 0)
                              );
                            }
                            return total;
                          }, 0)}{" "}
                          Nodes
                        </span>
                      </div>

                      <div className="flex items-center gap-1 px-2 py-0.5 bg-green-50 rounded-full">
                        <span className="typography-body-sm font-weight-medium text-green-700">
                          {projectCost?.project_credits.toFixed(2) || "0.00"}{" "}
                          Credits
                        </span>
                      </div>

                      <div className="flex items-center gap-1 px-2 py-0.5 bg-purple-50 rounded-full">
                        <span className="typography-body-sm font-weight-medium text-purple-700">
                          {Math.round(
                            (completedNodes / (totalNodes || 1)) * 100
                          )}
                          % Complete
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-wrap items-center gap-2">
                    <BootstrapTooltip title={!is_having_permission() ? "You don't have permission" : TOOLTIP_CONTENT.overview.update}>
                      <span>
                        <DynamicButton
                          variant="primary"
                          icon={Settings}
                          text="Update Project"
                          onClick={updateProject}
                          responsiveText={true}
                          className="whitespace-nowrap"
                          disabled={!is_having_permission()}
                        />
                      </span>
                    </BootstrapTooltip>

                    <div className={`${is_free_user ? "mr-1.5" : ""} relative`}>
                      <DynamicButton
                        variant="primaryLegacy"
                        text="Auto Configure"
                        isLoading={loadingAutoConfigure}
                        onClick={() => handleConfigureClick(id, type)}
                        tooltip={
                          !is_having_permission()
                            ? "You don't have permission"
                            : is_free_user
                            ? "Premium feature - Upgrade to access"
                            : TOOLTIP_CONTENT.overview.autoConfigure
                        }
                        responsiveText={true}
                        className={`whitespace-nowrap`}
                        id="overviewAutoConfigButton"
                        disabled={!is_having_permission() || is_free_user}
                      />
                      {is_free_user && <LockedTabIndicator />}
                    </div>

                    {enableReconfig && (
                      <div className="relative">
                        <DynamicReconfigButton
                          variant="primary"
                          text="Re-Configure"
                          icon={RefreshCw}
                          isLoading={loadingReConfigure}
                          onClick={handleReconfigureClick}
                          tooltip={
                            !is_having_permission()
                              ? "You don't have permission"
                              : is_free_user
                              ? "Premium feature - Upgrade to access"
                              : TOOLTIP_CONTENT.overview.autoConfigure
                          }
                          isBlynk={true}
                          disabled={!is_having_permission() || is_free_user}
                        />
                        {is_free_user && <LockedTabIndicator />}
                      </div>
                    )}

                    <CustomDropdown options={menuOptions} align="right" />
                  </div>
                </div>
              </div>
              <div className="flex flex-col border rounded-md rounded-br-md bg-white">
                {/* Progress Tracking Section */}
                <div>
                  {/* Progress Stepper Component */}
                  <div>
                    {isLoadingCounts ? (
                      <ProgressStepperSkeleton />
                    ) : (
                      <div className="w-full">
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-2 mb-3">
                          {stepsWithPercentage.map((step, index) => {
                            const isActive =
                              selectedStepDetails?.id === step.id ||
                              (!selectedStepDetails && index === 0);
                            return (
                              <div
                                key={step.id}
                                className={`bg-white border rounded-lg p-2 cursor-pointer transition-all duration-200 ${
                                  isActive
                                    ? "border-blue-400 shadow-md"
                                    : "border-gray-200 hover:border-blue-300"
                                }`}
                                onClick={() => handleStepClick(step.id)}
                              >
                                <div className="flex justify-between items-center mb-1">
                                  <h3 className="font-weight-medium text-gray-800">
                                    {step.title}
                                  </h3>
                                  <div className="flex items-center justify-center w-11 h-11 rounded-full bg-gray-50 border border-gray-200">
                                    <span
                                      className="typography-body font-weight-semibold"
                                      style={{
                                        color:
                                          step.progress > 0
                                            ? "#3B82F6"
                                            : "#9CA3AF",
                                      }}
                                    >
                                      {step.progress}%
                                    </span>
                                  </div>
                                </div>
                                <p className="typography-caption text-gray-500">
                                  {step.completedNodes} of {step.totalNodes}{" "}
                                  nodes completed
                                </p>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Details Panel - Always visible */}
                  <div className="bg-white border border-gray-200 rounded-lg shadow-sm mt-2">
                    {selectedStepDetails ? (
                      <>
                        {/* Compact Header with Navigate Button */}
                        <div className="flex items-center justify-between border-b border-gray-100 p-2">
                          <div className="flex items-center gap-2">
                            <h3 className="typography-body font-weight-medium text-gray-800">
                              {selectedStepDetails.title} Details
                            </h3>
                            <span className="typography-caption text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">
                              {selectedStepDetails.completedNodes} of{" "}
                              {selectedStepDetails.totalNodes} completed
                            </span>
                          </div>
                          <BootstrapTooltip
                            title={TOOLTIP_CONTENT.overview.navigateTo}
                          >
                            <button
                              onClick={() =>
                                navigateToSection(selectedStepDetails.route)
                              }
                              className="flex items-center gap-1 px-2 py-1 typography-caption font-weight-medium text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-md transition-colors duration-150"
                            >
                              <span>Go to {selectedStepDetails.title}</span>
                              <ArrowUpRight size={14} />
                            </button>
                          </BootstrapTooltip>
                        </div>

                        {/* Details Content - More compact layout */}
                        {selectedStepDetails.detailsList &&
                          selectedStepDetails.detailsList.length > 0 && (
                            <div className="px-3 py-2">
                              <div className="flex flex-wrap gap-1.5">
                                {selectedStepDetails.detailsList.map(
                                  (detail, index) => {
                                    const [label, status] = detail.split(": ");
                                    const count =
                                      label.match(/\((\d+)\)/)?.[1] || "0";
                                    const labelWithoutCount = label.replace(
                                      /\s*\(\d+\)/,
                                      ""
                                    );

                                    return (
                                      <div
                                        key={index}
                                        className="flex items-center gap-1.5 bg-gray-50 rounded-lg py-0.5 px-1.5 border border-gray-100"
                                      >
                                        {status === "Complete" ? (
                                          <svg
                                            width="14"
                                            height="14"
                                            viewBox="0 0 16 16"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                          >
                                            <circle
                                              cx="8"
                                              cy="8"
                                              r="7"
                                              fill="#D1FAE5"
                                              stroke="#10B981"
                                              strokeWidth="1.5"
                                            />
                                            <path
                                              d="M5.5 8L7 9.5L10.5 6"
                                              stroke="#10B981"
                                              strokeWidth="1.5"
                                              strokeLinecap="round"
                                              strokeLinejoin="round"
                                            />
                                          </svg>
                                        ) : (
                                          <div className="w-3.5 h-3.5 rounded-full border border-gray-300" />
                                        )}
                                        <div className="flex items-center gap-0.5">
                                          <span className="font-weight-medium text-gray-700">
                                            {labelWithoutCount}
                                          </span>
                                          <span className="typography-caption text-gray-500">
                                            ({count})
                                          </span>
                                        </div>
                                        <span
                                          className={`px-1 py-0.5 rounded-full typography-caption font-weight-medium ${
                                            status === "Complete"
                                              ? "bg-green-100 text-green-700"
                                              : "bg-gray-200 text-gray-700"
                                          }`}
                                        >
                                          {status}
                                        </span>
                                      </div>
                                    );
                                  }
                                )}
                              </div>
                            </div>
                          )}
                      </>
                    ) : (
                      <div className="p-3 text-center">
                        <p className="text-gray-500 typography-body-sm">
                          Select a category above to view details
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-4 px-0">
              {projectData && (
                <PropertiesRenderer
                  properties={projectData.node}
                  metadata={projectData.ui_metadata.Project}
                  to_skip={["Type", "Title"]}
                  onUpdate={handlePropertyUpdate}
                />
              )}
            </div>
          </div>
        </main>
      </div>
    </>
  );
};

export default OverviewTabContent;
