"use client";

import React, { useState } from "react";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { MdOutlineKeyboardArrowDown } from "react-icons/md";
import { GoKebabHorizontal } from "react-icons/go";
import { createPortal } from "react-dom";
import { BootstrapTooltip } from "../UIComponents/ToolTip/Tooltip-material-ui";
import LockedTabIndicator from "@/components/UIComponents/LockedTabIndicator";
import { useUser } from "@/components/Context/UserContext";
import { LOCKED_TABS } from "@/components/FreePlanRestriction";
import "@/styles/components/LockedTab.css";

export interface Tab {
    name: string;
    icon: React.ReactNode;
    label: string;
    tooltip: string;
}

interface TabOverflowDropdownProps {
    overflowTabs: Tab[];
    handleTabChange: (value: string) => void;
}

const TabOverflowDropdown: React.FC<TabOverflowDropdownProps> = ({
    overflowTabs,
    handleTabChange
}) => {
    const [isOverflowMenuOpen, setIsOverflowMenuOpen] = useState(false);
    const { is_free_user } = useUser();

    return (
        <DropdownMenu.Root
            open={isOverflowMenuOpen}
            onOpenChange={setIsOverflowMenuOpen}
        >
            <DropdownMenu.Trigger asChild>
                <button className="flex items-center justify-center px-4 py-1.5 h-8 text-gray-600 hover:bg-gray-100 transition-all duration-300 ease-[cubic-bezier(0.4, 0, 0.2, 1)]">
                    <span className="mr-1">
                        <GoKebabHorizontal className="w-4 h-4" />
                    </span>
                    <span>More</span>
                    <span className="ml-1">
                        <MdOutlineKeyboardArrowDown className="w-4 h-4" />
                    </span>
                </button>
            </DropdownMenu.Trigger>
            {createPortal(
                <DropdownMenu.Content className="bg-white border rounded-md z-50 mt-2 shadow-lg flex p-2 flex-col space-y-2">
                    {overflowTabs?.map(tab => (
                        <BootstrapTooltip key={tab.name} title={is_free_user && LOCKED_TABS.includes(tab.name) ? "Premium feature - Upgrade to access" : tab.tooltip} placement="left">
                            <DropdownMenu.Item
                                onSelect={() => {
                                    handleTabChange(tab.name);
                                    setIsOverflowMenuOpen(false);
                                }}
                                className={`flex items-center px-2 py-1 hover:bg-gray-100 cursor-pointer ${is_free_user && LOCKED_TABS.includes(tab.name) ? "locked-tab" : ""}`}
                            >
                                <div className="w-4 h-4">{tab.icon}</div>
                                <div className={`flex items-center ${is_free_user && LOCKED_TABS.includes(tab.name) ? "locked-tab-content" : ""}`}>
                                    {is_free_user && LOCKED_TABS.includes(tab.name) && (
                                        <div className="locked-tab-overlay"></div>
                                    )}
                                    <span className={`typography-body-sm custom-table-text-ellipsis ${is_free_user && LOCKED_TABS.includes(tab.name) ? "locked-tab-text" : ""}`}>{tab.label}</span>
                                    {is_free_user && LOCKED_TABS.includes(tab.name) && (
                                        <LockedTabIndicator />
                                    )}
                                </div>
                            </DropdownMenu.Item>
                        </BootstrapTooltip>
                    ))}
                </DropdownMenu.Content>,
                document.body
            )}
        </DropdownMenu.Root>
    );
};

export default TabOverflowDropdown;
