"use client"

import React, { useEffect, useState,useContext } from "react";
import '@/styles/tabs/architecture/designDetailTab/designTypeDetails.css'
import {
    useRouter,
    usePathname,
    useSearchParams
} from "next/navigation";
import PropertiesRenderer from "@/components/UiMetadata/PropertiesRenderer";
import { fetchNodeById ,updateNodeByPriority} from "@/utils/api";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import ConfigureButtons from "@/components/ConfigureButtons/ConfigureButtons";
import { AlertContext } from "@/components/NotificationAlertService/AlertList"; 
import { Loading2 } from "@/components/Loaders/Loading";
import Badge from "@/components/UIComponents/Badge/Badge"
import en from "@/en.json"
import ErrorView from "@/components/Modal/ErrorViewModal";


const DesignTypeDetails = () => {
    const [loading, setLoading] = useState(true);
    const [designTypeDetails, setDesignTypeDetails] = useState([])
    const { showAlert } = useContext(AlertContext); 
    const [error, setError] = useState(null)
    const pathname = usePathname();
    const projectId = pathname.split("/")[2];
    const designId = pathname.split("/")[5];
    const designType = pathname.split('/')[6]
    const router = useRouter();
    const searchParams = useSearchParams()
    let designTypeId

    const fetchData = async () => {
        designTypeId = sessionStorage.getItem("child design id")
        setLoading(true);
        try {
            const data = await fetchNodeById(designTypeId, designType);
            setDesignTypeDetails(data);
        } catch (error) {
            
            setError(error)
        } finally {
            setLoading(false);
        }
    };


    useEffect(() => {
        fetchData();
    }, [projectId, designId, pathname])


    if (loading) {
        return <Loading2 />;
    }

    const handleUpdateDesign = () => {
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.set("discussion", "new");
        newSearchParams.set("node_id", designId);
        newSearchParams.set("node_type", "Design");
        router.push(`${pathname}?${newSearchParams.toString()}`);
    };
    const handleBack = () => {
        router.back();
    };

    const updateProps = {
        onUpdateClick: handleUpdateDesign,
        buttonText: "Update Design Details",
    };

    if (error) {
        return (
            <ErrorView
                title="Unable to Load Design Type Details"
                message={en.UnableToLoadDesignTypeDetails}
                onRetry={() => fetchData()}
                panelType='main'
            />
        )
    }

    const handlePropertyUpdate = async (key, value) => {
        try {
            designTypeId = sessionStorage.getItem("child design id");
            const response = await updateNodeByPriority(designTypeId, key, value);
            
            if (response === "success") {
                // Update local state
                setDesignTypeDetails(prev => ({
                    ...prev,
                    properties: {
                        ...prev.properties,
                        [key]: value
                    }
                }));
                showAlert("Content updated successfully", "success");
            } else {
                throw new Error('Update failed');
            }
        } catch (error) {
            
            showAlert("Failed to update content", "error");
        }
    };

    return (
        <div className="custom-scrollbar design-type-details-content-wrapper">
            <div className="design-type-details-content-sub-wrapper">
                <div className="design-type-details-container">
                    <div className="design-type-details-header-wrapper">
                        <div className="design-type-details-header-sub-wrapper">
                            <button
                                onClick={handleBack}
                                className="design-type-details-header-back-button"
                            >
                                <div className="design-type-details-header-back-icon-wrapper">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="design-type-details-header-back-icon" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                                    </svg>
                                </div>
                            </button>
                            <h2 className="design-type-details-heading-title">  {designTypeDetails?.properties?.Title || "Design Type Details"}</h2>
                            {designTypeDetails?.properties?.Type ? (
                                <div className="design-type-details-heading-badge">
                                    <Badge type={designTypeDetails?.properties?.Type} />
                                </div>
                            ) : (
                                ""
                            )} </div>
                        <div className="design-type-details-header-button-wrapper">
                            <ConfigureButtons
                                updateProps={updateProps}

                            />
                        </div>
                    </div>
                    {!designTypeDetails ? (<div className="design-type-details-no-items-found"> <EmptyStateView type="designTypeDetails" /></div>) : (
                        <PropertiesRenderer
                            properties={designTypeDetails?.properties}
                            metadata={designTypeDetails?.ui_metadata}
                            to_skip={["configuration_state", "Type", "Title"]}
                            onUpdate={handlePropertyUpdate} 
                        />
                    )}
                </div>
            </div>
        </div>
    )
}

export default DesignTypeDetails;