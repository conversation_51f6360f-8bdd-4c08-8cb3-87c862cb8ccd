"use client";
import ConfigureButtons from "@/components/ConfigureButtons/ConfigureButtons";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { createInterfaceNodeForEdge, fetchNodeById, updateNodeByPriority, } from "@/utils/api";
import { Accordion } from "@/components/UIComponents/Accordions/Accordion";
import { getInterfaceChildren } from "@/utils/architectureAPI";
import { useParams, useRouter, usePathname, useSearchParams, } from "next/navigation";
import React, { useContext, useEffect, useState } from "react";
import PropertiesRenderer from "@/components/UiMetadata/PropertiesRenderer";
import { ArchitectureContext } from "@/components/Context/ArchitectureContext";
import Badge from "@/components/UIComponents/Badge/Badge";
import en from "../../../en.json";
import ErrorView from "@/components/Modal/ErrorViewModal";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { BookOpen, Eye, ArrowLeft, ChevronDown, ChevronUp } from "lucide-react";
import { IconButton } from "@/components/UIComponents/Buttons/IconButton";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import APIDocumentation from "@/components/API/API"
import { updateSessionStorageBackHistory } from "@/utils/helpers";
import InterfaceDefinition from "./InterfaceDefinition";
import NavigationTree from "@/components/Modal/NavigationTreeComponent"
import { TwoColumnSkeletonLoader } from "@/components/UIComponents/Loaders/LoaderGroup"
import { cn } from '@/lib/utils';
import { useResponsiveDimensions } from "@/utils/responsiveness";
import { TOOLTIP_CONTENT } from "@/utils/constant/tooltip";


export default function InterfaceItem() {
  const params = useParams();
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [setInterfaceLoading] = useState(false);
  const [interfaceNodeId, setInterfaceNodeId] = useState(null);
  const [interfaceChildren, setInterfaceChildren] = useState([]);
  const [uiMetadata, setUiMetadata] = useState(null);
  const [properties, setProperties] = useState(null);
  const { showAlert } = useContext(AlertContext);
  const { componentIdVal } = useContext(ArchitectureContext);
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const projectId = pathname.split("/")[2];
  let componentId;
  const { mainContentWidth, calculateDimensions } = useResponsiveDimensions();
  const [isOpen, setIsOpen] = useState(true);
  const toggleAccordion = () => { setIsOpen(!isOpen); };
  const handleUpdateClick = async (e, discussion_type) => {
    if (!interfaceNodeId) {
      await createInterface();
      return;
    }
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", interfaceNodeId);
    newSearchParams.set("node_type", "Interface");
    newSearchParams.set("discussionType", discussion_type);
    updateSessionStorageBackHistory();
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const updateProps = {
    onUpdateClick: (e) => handleUpdateClick(e, "design_details"),
    buttonText: (() => {
      if (
        properties &&
        Object.keys(properties).length === 2 &&
        Object.keys(properties).includes("Title") &&
        Object.keys(properties).includes("incoming_interfaces")
      ) {
        return "Create Interface Design Details";
      } else {
        return "Update Interface Design Details";
      }
    })(),
    tooltip: (() => {
      if (
        properties &&
        Object.keys(properties).length === 2 &&
        Object.keys(properties).includes("Title") &&
        Object.keys(properties).includes("incoming_interfaces")
      ) {
        return TOOLTIP_CONTENT.Architecture.interfaces.create;
      } else {
        return TOOLTIP_CONTENT.Architecture.interfaces.update;
      }
    })(),
  };

  const interface_definition_update_props = {
    onUpdateClick: (e) => handleUpdateClick(e, "definition"),
    buttonText:
      interfaceChildren && Object.keys(interfaceChildren).length > 0
        ? "Update Interface Defnition"
        : "Create Interface Defnition",
  };

  const handleViewPastDiscussion = async (
    discussion_type = "design_details"
  ) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("view_past_discussions", "true");
    newSearchParams.set("node_id", interfaceNodeId);
    newSearchParams.set("discussion_type", discussion_type);
    updateSessionStorageBackHistory();
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  useEffect(() => {
    if (componentIdVal === null) {
      componentId = sessionStorage.getItem("componentId");
    }
  }, []);

  const fetchData = async () => {
    try {
      const interfaceData = await fetchNodeById(params.interfaceId, "Interface");
      setData(interfaceData);
      setLoading(false);
      setInterfaceNodeId(interfaceData?.id);
      setProperties(interfaceData?.properties);
      setUiMetadata(interfaceData?.ui_metadata);
      if (interfaceData?.id) {
        const children = await getInterfaceChildren(interfaceData?.id);
        setInterfaceChildren(children);
      }
    } catch (e) {

      setError(e);
    }
  };

  useEffect(() => {
    fetchData();
  }, [params.interfaceId, searchParams]);

  useEffect(() => {
    calculateDimensions()
  }, [calculateDimensions])

  if (error) {
    return (
      <ErrorView
        title="Unable to Load Interface"
        message={en.UnableToLoadInterface}
        onRetry={() => fetchData()}
        panelType="main"
      />
    );
  }

  const handlePropertyUpdate = async (key, value) => {
    try {
      if (!data?.id) {
        throw new Error("Interface ID not found");
      }

      const response = await updateNodeByPriority(data.id, key, value);

      if (response === "success") {
        setData((prev) => ({
          ...prev,
          properties: {
            ...prev.properties,
            [key]: value,
          },
        }));

        setProperties((prev) => ({
          ...prev,
          [key]: value,
        }));

        showAlert("Content updated successfully", "success");
      } else {
        throw new Error("Update failed");
      }
    } catch (error) {

      showAlert("Failed to update content", "error");
    }
  };

  const createInterface = async () => {
    setInterfaceLoading(true);
    try {
      const response = await createInterfaceNodeForEdge(
        projectId,
        params.interfaceId
      );
      if (response) {
        await fetchData();
      }
      setInterfaceLoading(false);
      showAlert("Interface Created Successfully", "success");
    } catch (error) {
      showAlert("Creating Interface Failed", "danger");
    }
  };

  const handleBackClick = () => {
    if (sessionStorage.getItem("querySet")) {
      const backTabs = Number(sessionStorage.getItem("querySet"));
      sessionStorage.removeItem("querySet");
      if (window.history.length > Number(backTabs) * (-1)) {
        window.history.go(backTabs);
      }
      else {
        router.push(`/project/${projectId}/architecture/architecture-requirement`);
      }
    }
    else {
      router.back();
    }
  };

  const handleGoBack = () => {
    router.push(`/project/${projectId}/architecture/component/${componentId}`);
  };


  const HeaderSection = () => {
    const title = properties?.Title || data?.properties?.Title || "N/A";
    const type = data?.properties?.Type || properties?.Type;

    return (
      <div className="flex flex-col space-y-4 top-1" style={{ zIndex: 5 }}>
        <div className="flex flex-col border border-gray-200">
          <div className="relative px-2 py-1 space-y-2">
            <div className="flex items-center gap-3">
              <IconButton
                icon={<ArrowLeft className="w-5 h-5 text-gray-600" />}
                tooltip="Go back"
                onClick={handleBackClick}
                className="hover:bg-gray-100"
              />
              <div className="flex items-center gap-2">
                <h2 className="typography-body-lg font-weight-semibold text-gray-800">{title}</h2>
                {type && (
                  <div className="flex items-center gap-1">
                    <Badge type={type} />
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center justify-between pl-3">
              {componentId && (
                <DynamicButton
                  type="submit"
                  variant="secondary"
                  size="default"
                  icon={Eye}
                  onClick={handleGoBack}
                  text="View Component Details"
                />
              )}
              <div className="flex items-center gap-2 ml-auto">
                <ConfigureButtons updateProps={updateProps} />

                <button
                  onClick={handleViewPastDiscussion}
                  className="min-w-[100px] flex items-center gap-1 px-3 py-2 bg-white border border-gray-300
                             rounded-md text-black hover:bg-gray-100 focus:ring-2 focus:ring-gray-500
                             focus:ring-offset-2 typography-body-sm"
                >
                  <BookOpen size={14} />
                  History
                </button>
              </div>
            </div>

            <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-100/50 via-blue-300/20 to-transparent"></div>
          </div>
        </div>
      </div>
    );
  };


  const metadataEntries = Object.entries(uiMetadata || {})
    .filter(
      ([key, value]) =>
        !["Title", "Type"].includes(key) &&
        value.hidden !== true &&
        properties?.hasOwnProperty(key)
    )
    .sort(([keyA, valueA], [keyB, valueB]) => {

      if (valueA.display_type === 'rich_text' && valueB.display_type !== 'rich_text') {
        return -1;
      }
      if (valueB.display_type === 'rich_text' && valueA.display_type !== 'rich_text') {
        return 1;
      }
      return 0;
    })
    .map(([key, value]) => {
      const labelOrKey = value?.Label || key;


      const splitCamelCase = (str) => str.replace(/([a-z])([A-Z])/g, '$1 $2');

      return {
        id: `${splitCamelCase(labelOrKey).toLowerCase().replace(/[_\s]+/g, '-')}`,
        name: splitCamelCase(labelOrKey).replace(/[_-]/g, ' '),
        children: [],
      };
    });

  const additionalEntries = Object.entries(interfaceChildren || {})
    .map(([key, value]) => {

      const splitCamelCase = (str) => str.replace(/([a-z])([A-Z])/g, '$1 $2');

      return {
        id: `${splitCamelCase(key).toLowerCase().replace(/[_\s]+/g, '-')}`,
        name: splitCamelCase(key).replace(/[_-]/g, ' '),
        children: [],
      };
    }).sort((a, b) => a.id.localeCompare(b.id));


  const treeData = [...metadataEntries, ...additionalEntries];

  const handleScrollToSection = (id) => {

    const element = document.getElementById(id);

    const mainContent = document.getElementById("main-content")

    if (element && mainContent) {
      const topOffset = element.offsetTop;
      mainContent.scrollTo({
        top: topOffset - 80,
        behavior: "smooth",
      });
    }
  };

  const HeaderDefineSection = () => (

    <div className="border border-gray-200 rounded-lg mb-4">
      <button
        onClick={toggleAccordion}
        className={cn(
          'w-full flex items-center justify-between px-4 py-1 text-left rounded-t-lg transition-colors duration-200',
          'hover:bg-gray-100'
        )}
        type="button"
        aria-expanded={isOpen}
      >
        <span className="flex items-center gap-2 font-weight-semibold typography-body text-[#2A3439]">
          Interface Definition
        </span>
        <div className="flex items-center gap-4">
          {interfaceNodeId && (
            <ConfigureButtons updateProps={interface_definition_update_props} />
          )}
          <span className="text-gray-500">
            {isOpen ? (
              <ChevronUp className="h-5 w-5" />
            ) : (
              <ChevronDown className="h-5 w-5" />
            )}
          </span>
        </div>
      </button>
      {isOpen && (
        <div className="p-4 bg-white border-t border-gray-200 rounded-b-lg typography-body-sm text-[#464F60] font-weight-medium">
          {interfaceChildren ? (
            <>
              <InterfaceDefinition data={interfaceChildren} />
            </>
          ) : (
            <div className="interface-definition-tab-content-no-content-found">
              <span>
                <EmptyStateView type="interfaceDefinition" />
              </span>
            </div>
          )}
        </div>
      )}
    </div>

  );


  if (loading) {
    return <TwoColumnSkeletonLoader />;
  }

  return (
    <div className="relative flex max-h-[74vh] overflow-hidden bg-white-50">
      <div>
        {treeData && (
          <NavigationTree
            treeData={treeData}
            handleScrollToSection={handleScrollToSection}
          />
        )}
      </div>

      <main
        id="main-content"
        className="
              flex-1
              relative
              overflow-y-auto
              overflow-x-hidden
              transition-all
              duration-300
              ease-in-out
            "
        style={{
          width: mainContentWidth,
        }}
      >
        <div className="w-full pl-4 pr-4">
          <div>
            <HeaderSection />
          </div>

          {properties && data && (
            <div className="interface-content-wrapper">
              <PropertiesRenderer
                properties={properties}
                metadata={uiMetadata}
                to_skip={["Title", "PublicAPIDetails", "Type"]}
                onUpdate={handlePropertyUpdate}
              />

              {properties.PublicAPIDetails && (
                <div id="public-interfaces" className="bg-white rounded-lg mb-2">
                  <Accordion title="Public API Reference" defaultOpen={true}>
                    <APIDocumentation apiDetails={properties.PublicAPIDetails} />
                  </Accordion>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="interface-definition-tab-content-wrapper mt-4 space-y-3 pl-6">
          <HeaderDefineSection />
        </div>
      </main>
    </div>
  );

}
