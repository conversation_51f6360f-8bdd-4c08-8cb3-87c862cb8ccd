"use client";

import React, { useEffect, useState, useContext } from "react";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { fetchSystemContextWithContainers, getReconfigNodeStatus, updateNodeByPriority } from "@/utils/api";
import en from "@/en.json";
import { ArchitectureContext } from "@/components/Context/ArchitectureContext";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import Badge from "@/components/UIComponents/Badge/Badge";
import ErrorView from "@/components/Modal/ErrorViewModal";
import { IconButton } from '@/components/UIComponents/Buttons/IconButton';
import { ArrowLeft, CodeXml, Eye, FolderGit2 } from "lucide-react";
import GenericCardGrid from "@/components/UIComponents/GenericCards/GenericCardGrid";
import CardGroupSkeletonLoder from "@/components/UIComponents/Loaders/CardGroupSkeletonLoder";
import RequirementsBanner from "@/components/UIComponents/Badge/RequirementBanner";
import "@/styles/tabs/architecture/container.css";
import CodeGenerationSetupModal from "@/app/modal/CodeGenerationSetupModal";
import CodeGenerationHandler from "@/app/modal/CodeGenerationHandler";
import CodeGenerationModal from "@/app/modal/CodeGenerationModal";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import { PLATFORMS, frameworks, Generic, backendFrameworks, mobileFrameworks } from "@/constants/code_gen/platforms";
import { BranchSelector } from "@/components/Git/BranchSelector";
import { getRepository } from "@/utils/repositoryAPI";
import RepositoryDetailsModal from "@/components/Modal/RepositoryModal";
import PastTasksModal from "@/components/Modal/PastTasksModal";
import { getPastCodeGenerationTasks } from "@/utils/batchAPI";

const ContainerList = ({isModal=false}) => {
  const { showAlert } = useContext(AlertContext);
  const [loading, setLoading] = useState(true);
  const [containerList, setContainerList] = useState([]);
  const { setContainerData } = useContext(ArchitectureContext);
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const projectId = pathname.split("/")[2];
  const router = useRouter();
  const [error, setError] = useState(null);
  const [showBaner, setShowBaner] = useState(false);
  const [reconfigCount, setReconfigCount] = useState(0);

  // Code generation state variables
  const [codeGenSetupModal, setCodeGenSetupModal] = useState(false);
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const [currentPlatform, setCurrentPlatform] = useState({ key: "generic", label: "Generic", icon: <Generic /> });
  const [currentFramework, setCurrentFramework] = useState(frameworks[0]);
  const [selectedContainer, setSelectedContainer] = useState({});
  const [selectedContainerId, setSelectedContainerId] = useState("");
  const { isVisible } = useCodeGeneration();

  // Repository state variables
  const [showRepoDetails, setShowRepoDetails] = useState(false);
  const [isRepoConfigured, setIsRepoConfigured] = useState(false);
  const [repositoryState, setRepositoryState] = useState({
    state: 'initial', // initial, loading, success, error
    data: null
  });

  // Past tasks state variables
  const [isPastTasksCodeModalOpen, setIsPastTasksCodeModalOpen] = useState(false);
  const [pastTasks, setPastTasks] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [limit, setLimit] = useState(10);
  const [skip, setSkip] = useState(0);
  const [isPastTaskLoading, setIsPastTaskLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await fetchSystemContextWithContainers(projectId);

      sessionStorage.setItem(
        "project name",
        data?.data?.systemContext?.properties?.Title
      );

      const reconfig = await getReconfigNodeStatus(projectId);
      let showBanner = false;

      if (reconfig) {
        const hasReconfigNeeded =
        reconfig.Container?.some(item => item.reconfig_needed === true);
        const Count =
        (reconfig.Container?.filter(item => item.reconfig_needed === true)?.length || 0);

        setReconfigCount(Count);
        showBanner = hasReconfigNeeded;
      }
      setShowBaner(showBanner);

      setContainerList(data);
      setContainerData(data?.data?.containers);
    } catch (error) {
      setError(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [projectId]);

  const fetchRepositoryDetails = async (containerId) => {
    try {
      setRepositoryState({
        state: 'loading',
        data: null
      });

      const response = await getRepository(projectId, containerId);

      if (response.repository) {
        setRepositoryState({
          state: 'success',
          data: response.repository
        });
        setIsRepoConfigured(true);
      } else {
        setRepositoryState({
          state: 'error',
          data: null
        });
        setIsRepoConfigured(false);
      }
    } catch (err) {
      setRepositoryState({
        state: 'error',
        data: null
      });
      setIsRepoConfigured(false);
      console.error("Error fetching repository details:", err);
    }
  };

  const handlePropertyUpdate = async (key, value) => {
    try {
      const response = await updateNodeByPriority(selectedContainerId, key, value);

      if (response === "success") {
        // Update local state for the selected container
        setSelectedContainer(prev => ({
          ...prev,
          properties: {
            ...prev.properties,
            [key]: value
          }
        }));

        // Also update in the containerList state
        setContainerList(prevList => {
          const updatedContainers = prevList.data.containers.map(container => {
            if (container.id === selectedContainerId) {
              return {
                ...container,
                properties: {
                  ...container.properties,
                  [key]: value
                }
              };
            }
            return container;
          });

          return {
            ...prevList,
            data: {
              ...prevList.data,
              containers: updatedContainers
            }
          };
        });

      } else {
        throw new Error('Update failed');
      }
    } catch (error) {
      console.error(error);
      showAlert("Failed to update content", "error");
    }
  };

  const handleRowClick = (containerId) => {
    router.push(`/project/${projectId}/architecture/container/${containerId}`);
  };

  const onGenerateCode = (container) => {
    setSelectedContainer(container);
    setSelectedContainerId(container.id);

    // Set platform and framework from container if available
    if (container.properties.platform) {
      const platformData = PLATFORMS.find(p => p.key === container.properties.platform);
      setCurrentPlatform(platformData ? {
        key: platformData.key,
        label: platformData.label,
        icon: platformData.icon
      } : { key: "generic", label: "Generic", icon: <Generic /> });
    } else {
      setCurrentPlatform({ key: "generic", label: "Generic", icon: <Generic /> });
    }

    // Set framework if available in container properties
    if (container.properties.framework) {
      const frameworkData = frameworks.find(f => f.key === container.properties.framework) ||
                          backendFrameworks.find(f => f.key === container.properties.framework) ||
                          mobileFrameworks.find(f => f.key === container.properties.framework);
      if (frameworkData) {
        setCurrentFramework(frameworkData);
      } else {
        setCurrentFramework(frameworks[0]);
      }
    } else {
      setCurrentFramework(frameworks[0]);
    }

    // Fetch repository details for this container
    fetchRepositoryDetails(container.id);

    // Open the code generation setup modal
    setCodeGenSetupModal(true);
  };

  const handleGenerateCode = () => {
    setIsGeneratingCode(true);
  };

  const handlePlatformChange = (platformData) => {
    setCurrentPlatform(platformData);
    handlePropertyUpdate("platform", platformData.key);

    // Update framework based on platform change
    if (platformData.key === "mobile") {
      setCurrentFramework(mobileFrameworks[0]);
      handlePropertyUpdate("framework", mobileFrameworks[0].key);
    } else if (platformData.key === "web") {
      setCurrentFramework(frameworks[0]);
      handlePropertyUpdate("framework", frameworks[0].key);
    } else if (platformData.key === "backend") {
      setCurrentFramework(backendFrameworks[0]);
      handlePropertyUpdate("framework", backendFrameworks[0].key);
    } else {
      setCurrentFramework(frameworks[0]);
      handlePropertyUpdate("framework", frameworks[0].key);
    }
  };

  const handleFrameworkChange = (newFramework) => {
    setCurrentFramework(newFramework);
    handlePropertyUpdate("framework", newFramework.key);
  };

  const handleBranchUpdate = async (newBranch) => {
    try {
      await handlePropertyUpdate("branch", newBranch);
    } catch (error) {
      console.error(error);
      showAlert('Failed to update branch', 'error');
    }
  };

  const BranchSelection = () => {
    return (
      <BranchSelector
        projectId={projectId}
        containerId={selectedContainerId}
        currentBranch={selectedContainer?.properties?.branch}
        onUpdate={handleBranchUpdate}
        tooltip={"Select branch"}
        className="w-full"
      />
    );
  };

  const handleRepoDetailsOpen = () => {
    setShowRepoDetails(true);
  };

  const handleRepoDetailsClose = (success = false) => {
    setShowRepoDetails(false);
    if (success) {
      setIsRepoConfigured(true);
      showAlert('Repository configured successfully', 'success');
      fetchRepositoryDetails(selectedContainerId); // Refresh repository data
    }
  };

  const handleSetPastTasks = (tasks) => {
    setPastTasks([...tasks]);
  };

  const fetchPastTasks = async (currentSkip = 0, currentLimit = limit) => {
    setIsPastTaskLoading(true);
    try {
      const result = await getPastCodeGenerationTasks(
        projectId,
        selectedContainerId,
        currentLimit,
        currentSkip
      );
      handleSetPastTasks(result.tasks);
      setTotalCount(result.total_count);
      setSkip(currentSkip);
      setLimit(currentLimit);
    } catch (error) {
      console.error(error);
      showAlert("Failed to fetch past code generation tasks", "error");
    } finally {
      setIsPastTaskLoading(false);
    }
  };

  const handleViewPastCodeGeneration = async () => {
    await fetchPastTasks();
    setIsPastTasksCodeModalOpen(true);
  };

  const handlePageChange = async (newPage) => {
    const newSkip = (newPage - 1) * limit;
    await fetchPastTasks(newSkip, limit);
  };

  const handleLimitChange = async (newLimit) => {
    await fetchPastTasks(0, newLimit);
  };

  const handleUpdateContainerList = () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", projectId);
    newSearchParams.set("node_type", "Architecture");
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const updateProps = {
    onUpdateClick: handleUpdateContainerList,
    buttonText: "Update Container List",
  };

  const handleBack = () => {
    router.back();
  };

  const HeaderSection = () => (
    <div className="flex flex-col space-y-4 top-1" style={{ zIndex: 5 }}>
      <div className="flex flex-col border border-gray-200">
        <div className="relative px-2.5 py-1 space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <IconButton
                icon={<ArrowLeft className="w-5 h-5 text-gray-600" />}
                tooltip="Go back"
                onClick={handleBack}
                className="hover:bg-gray-100"
              />
              <div className="flex items-center gap-2">
                <h2 className="typography-body-lg font-weight-semibold text-gray-800">
                  {containerList?.data?.systemContext?.properties?.Title || "List of all containers"}
                </h2>
                {containerList?.data?.systemContext?.properties?.Type && (
                  <div className="flex items-center gap-1">
                    <Badge type={"Containers List"} />
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-100/50 via-blue-300/20 to-transparent"></div>
        </div>
      </div>
    </div>
  );

  useEffect(() => {
    if (searchParams.get("task_id")) {
    setCodeGenSetupModal(false);
    setIsGeneratingCode(false);
    }
  },[searchParams])

  if (loading) {
    return <CardGroupSkeletonLoder />;
  }

  if (error) {
    return (
      <ErrorView
        title="Unable to Load Container List"
        message={en.UnableToLoadContainer}
        onRetry={() => fetchData()}
        panelType='main'
      />
    );
  }


  if (!containerList?.data?.containers?.length || (isModal && !(containerList?.data?.containers?.some(
  item => item.properties?.ContainerType?.toLowerCase() === "internal" || item.properties?.ContainerType === undefined
))
)) {
    return <EmptyStateView type="containers" />;
  }

  return (
    <div className="h-full">
      {showBaner && (
        <RequirementsBanner value={`${reconfigCount} container${reconfigCount > 1 ? 's' : ''} need reconfiguration`} />
      )}
      <GenericCardGrid
        isModal={isModal}
        data={containerList.data.containers.map(container => ({
          container_name: container.properties.Title,
          components: [{
            container: {
              title: container.properties.Title,
              id: container.id,
            },
            ...container,
            ...container.properties // Flatten properties into the main object
          }]
        }))}
        onCardClick={(container) => handleRowClick(container.id)}
        actionButtons={[
          {
            icon: <Eye className="h-4 w-4" />,
            label: "View",
            onClick: (comp) => handleRowClick(comp.id),
            className: "bg-gray-50 text-gray-600 hover:bg-gray-100"
          },
          {
            icon: <FolderGit2 className="h-4 w-4" />,
            label: "Repository",
            onClick: (comp) => {
              setSelectedContainer(comp);
              setSelectedContainerId(comp.id);
              fetchRepositoryDetails(comp.id);
              handleRepoDetailsOpen();
            },
            className: "bg-blue-50 text-blue-600 hover:bg-blue-100"
          },
          {
            icon: <CodeXml className="h-4 w-4" />,
            label: "Generate Code",
            onClick: (comp) => onGenerateCode(comp),
            className: "bg-orange-50 text-orange-600 hover:bg-orange-100"
          }
        ]}
        uniquePageIdentifier={`arch-container-list-${projectId}`}
      />

      {/* Repository Modal */}
      {showRepoDetails && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-4 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <RepositoryDetailsModal
              open={true}
              projectId={projectId}
              containerId={selectedContainerId}
              onClose={handleRepoDetailsClose}
              onSuccess={() => handleRepoDetailsClose(true)}
            />
          </div>
        </div>
      )}

      {/* Code Generation Setup Modal */}
      {codeGenSetupModal && (
        <CodeGenerationSetupModal
          onClose={() => setCodeGenSetupModal(false)}
          onConfigureRepo={handleRepoDetailsOpen}
          BranchSelection={BranchSelection}
          currentPlatform={currentPlatform}
          onPlatformChange={handlePlatformChange}
          onConfirm={handleGenerateCode}
          repository={repositoryState.data}
          currentBranch={selectedContainer?.properties?.branch}
          currentFramework={currentFramework}
          onFrameworkChange={handleFrameworkChange}
          isGeneratingCode={isGeneratingCode}
          projectId={projectId}
          containerId={parseInt(selectedContainerId)}
          handleRepoChange={(repo) => {
            setRepositoryState({
              state: 'success',
              data: repo
            });
            showAlert('Repository configured successfully', 'success');
            // Close the repository modal if it's open
            setShowRepoDetails(false);
          }}
        />
      )}

      {/* Code Generation Handler */}
      {isGeneratingCode && (
        <CodeGenerationHandler
          projectId={projectId}
          itemId={selectedContainerId}
          onComplete={() => {
            setIsGeneratingCode(false);
          }}
        />
      )}

      {/* Code Generation Modal */}
      {isVisible && <CodeGenerationModal />}

      {/* Past Tasks Modal */}
      {isPastTasksCodeModalOpen && (
        <PastTasksModal
          isOpen={isPastTasksCodeModalOpen}
          onClose={() => setIsPastTasksCodeModalOpen(false)}
          tasks={pastTasks}
          totalCount={totalCount}
          limit={limit}
          skip={skip}
          onPageChange={handlePageChange}
          onLimitChange={handleLimitChange}
          isLoading={isPastTaskLoading}
        />
      )}
    </div>
  );
};

export default ContainerList;