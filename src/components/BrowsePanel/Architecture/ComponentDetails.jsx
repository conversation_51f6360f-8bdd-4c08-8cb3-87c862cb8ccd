"use client";

import React, { useEffect, useState, useContext } from "react";
import "@/styles/tabs/architecture/components.css";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import PropertiesRenderer from "@/components/UiMetadata/PropertiesRenderer";
import {
  fetchComponentsWithChildren,
  fetchComponentInterfaces,
  fetchNodeById,
} from "@/utils/api";
import en from "@/en.json";
import { ArchitectureContext } from "@/components/Context/ArchitectureContext";
import TableComponent from "@/components/SimpleTable/table";
import ConfigureButtons from "@/components/ConfigureButtons/ConfigureButtons";
import Badge from "@/components/UIComponents/Badge/Badge";
import ErrorView from "@/components/Modal/ErrorViewModal";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { ChevronsLeft, ArrowLeft, <PERSON>ting<PERSON>, Plus, BookOpen } from "lucide-react";
import { IconButton } from '@/components/UIComponents/Buttons/IconButton';
import APIDocumentation from "@/components/API/API";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import {
  updateNodeByPriority,
  getComponentFunctionalRequirements,
} from "@/utils/api";
import { updateSessionStorageBackHistory } from "@/utils/helpers";
import NavigationTree from "@/components/Modal/NavigationTreeComponent"
import { TwoColumnSkeletonLoader, CardLoadingSkeleton } from "@/components/UIComponents/Loaders/LoaderGroup"
import { useResponsiveDimensions } from "@/utils/responsiveness";
import PlaceholderMessage from "@/components/Modal/PlaceHolderMessage";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import TestCaseTable from "@/components/TestCaseTable/TestCaseTable";
import TestCaseDetailModal from "@/components/TestCaseTable/TestCaseDetailModal";
import { getRelatedNodes } from "@/utils/nodeRoute";

const ComponentDetails = () => {
  const [ComponentData, setComponentData] = useState(null);
  const [interfaces, setInterfaces] = useState(null);
  const [loading, setLoading] = useState(true);
  const [functionalReqs, setFunctionalReqs] = useState(null);
  const [error, setError] = useState(null);
  const { showAlert } = useContext(AlertContext);
  const [sourceComponentDetails, setSourceComponentDetails] = useState({});
  const pathname = usePathname();
  const router = useRouter();
  const searchParams = useSearchParams();
  const projectId = pathname.split("/")[2];
  const { containerIdVal, setComponentIdVal } = useContext(ArchitectureContext);
  const componentId = pathname.split("/")[5];
  let activeContainer = containerIdVal;
  const { mainContentWidth, contentHeight, calculateDimensions } = useResponsiveDimensions();

  // Define testCase states
  const [testCases, setTestCases] = useState([]);
  const [testCasesLoading, setTestCasesLoading] = useState(false);
  const [selectedTestCase, setSelectedTestCase] = useState(null);
  const [testCasePage, setTestCasePage] = useState(1);
  const [testCasePageSize, setTestCasePageSize] = useState(5);

  // Define fetchTestCases function before it's used
  const fetchTestCases = async (nodeId) => {
    setTestCasesLoading(true);
    try {
      // Fetch all three types of test relationships
      const [componentTests, systemTests, unitTests] = await Promise.all([
        getRelatedNodes(nodeId, "Component", "COMPONENT_TEST_FOR"),
        getRelatedNodes(nodeId, "Component", "SYSTEM_TEST_INVOLVES"),
        getRelatedNodes(nodeId, "Component", "UNIT_TEST_FOR")
      ]);

      // Combine all test cases and add their type
      const allTestCases = [
        ...(componentTests || []).map(test => ({ ...test, testType: 'Component Test' })),
        ...(systemTests || []).map(test => ({ ...test, testType: 'System Test' })),
        ...(unitTests || []).map(test => ({ ...test, testType: 'Unit Test' }))
      ];

      setTestCases(allTestCases);
    } catch (error) {
      
      showAlert && showAlert("Failed to load test cases", "danger");
    } finally {
      setTestCasesLoading(false);
    }
  };

  // Define test case interaction functions
  const handleTestCaseClick = (testCase) => {
    setSelectedTestCase(testCase);
  };

  const closeTestCaseModal = () => {
    setSelectedTestCase(null);
  };

  const fetchData = async () => {
    try {
      sessionStorage.setItem("componentId", componentId);
      if (containerIdVal === null) {
        activeContainer = sessionStorage.getItem("containerId");
      }
      const data = await fetchComponentsWithChildren(projectId, componentId);
      const interfaceData = await fetchComponentInterfaces(
        projectId,
        componentId
      );

      if (data?.data?.component?.properties?.ImplementedRequirementIDs) {
        const reqData = await getComponentFunctionalRequirements(
          projectId,
          componentId
        );
        setFunctionalReqs(reqData);
      }
      if (interfaceData?.data?.interfaces) {
        const sourceComponents = {};
        const incomingInterfaces = interfaceData.data.interfaces
          .map((intf) =>
            JSON.parse(intf.properties.incoming_interfaces || "[]")
          )
          .flat();

        for (const intf of incomingInterfaces) {
          if (
            intf.source_component_id &&
            !sourceComponents[intf.source_component_id]
          ) {
            try {
              const sourceData = await fetchNodeById(
                intf.source_component_id,
                "Component"
              );

              if (sourceData) {
                sourceComponents[intf.source_component_id] = {
                  title: sourceData.properties?.Title || "",
                  description: sourceData.properties?.Description || "",
                };
              }
            } catch (error) {
              
            }
          }
        }
        setSourceComponentDetails(sourceComponents);
      }

      setComponentData(data);
      setInterfaces(interfaceData);
      setComponentIdVal(componentId);
      
      // Now fetch test cases only if we have valid component data
      if (componentId) {
        fetchTestCases(componentId);
      }
    } catch (err) {
      
      setError("Failed to fetch data");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [projectId, componentId]);

  useEffect(() => {
    calculateDimensions()
  }, [calculateDimensions])

  if (loading) {
    return <TwoColumnSkeletonLoader />;
  }

  if (error) {
    return (
      <ErrorView
        title="Unable to Load Component Details"
        message={en.UnableToLoaComponentDetails}
        onRetry={() => fetchData()}
        panelType="main"
      />
    );
  }

  const handleUpdateComponent = () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", componentId);
    newSearchParams.set("node_type", "Architecture");
    updateSessionStorageBackHistory();
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const handleUpdateDesignDetails = () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", componentId);
    newSearchParams.set("node_type", "Architecture");
    newSearchParams.set("discussionType", "design_details");
    updateSessionStorageBackHistory();
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const updateProps = {
    onUpdateClick: handleUpdateComponent,
    buttonText:
      ComponentData?.data?.component?.properties &&
        Object.keys(ComponentData?.data?.component?.properties).filter(
          (key) => !["configuration_state", "Type", "Title"].includes(key)
        ).length <= 1
        ? "Create Component "
        : "Update Component ",
  };

  const handleViewPastDiscussion = async () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("view_past_discussions", "true");
    newSearchParams.set("node_id", componentId);
    newSearchParams.set("discussion_type", "Architecture");
    updateSessionStorageBackHistory();
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const headers = [
    { key: "id", label: "Id" },
    { key: "title", label: "Title" },
    { key: "type", label: "Type" },
    { key: "description", label: "Description" },
  ];

  const interfaceTableHeaders = [
    { key: "id", label: "ID" },
    { key: "title", label: "Title" },
    { key: "definition_state", label: "Definition State" },
  ];

  const interfaceHeaders = [
    { key: "sourceComponentId", label: "ID" },
    { key: "interfaceName", label: "Interface Name" },
    { key: "sourceComponentTitle", label: "Source Component Title" },
    {
      key: "sourceComponentDescription",
      label: "Source Component Description",
    },
  ];

  const DefinitionStateBadge = ({ state }) => {
    return (
      <span className="bg-gray-200 rounded-xl px-2 py-0.5 breadcrumb-title">
        {state || "Unknown"}
      </span>
    );
  };

  const parseInterfaces = (interfaceData) => {
    if (!interfaceData?.data?.interfaces) return [];

    return interfaceData.data.interfaces.map((intf) => ({
      id: intf.id,
      title: intf.properties.Title,
      definition_state: (
        <DefinitionStateBadge state={intf.properties.definition_state} />
      ),
    }));
  };

  const parseIncomingInterfaces = (interfaceData) => {
    if (!interfaceData?.data?.interfaces) return [];

    const interfaces = [];

    interfaceData.data.interfaces.forEach((intf) => {
      const incomingInterfaces = JSON.parse(
        intf.properties.incoming_interfaces || "[]"
      );

      incomingInterfaces.forEach((incoming) => {
        interfaces.push({
          id: incoming.source_component_id,
          sourceComponentId: incoming.source_component_id,
          sourceComponentTitle:
            sourceComponentDetails[incoming.source_component_id]?.title || "",
          interfaceName: incoming.name || "", // Using the name property from incoming interface
          sourceComponentDescription:
            sourceComponentDetails[incoming.source_component_id]?.description ||
            "",
        });
      });
    });

    return interfaces;
  };

  const onRowClick = (id, type) => {
    if (type === "Interface") {
      router.push(`/project/${projectId}/architecture/interfaces/${id}`);
    }
    if (type === "Design" || type === "Class") {
      router.push(`/project/${projectId}/architecture/design-details/${id}`);
    }
    if (type === "SubComponent") {
      router.push(`/project/${projectId}/architecture/design-details/${id}`);
    }
  };

  const tableData = ComponentData?.data?.childNodes.map((data) => {
    return {
      id: data.id,
      title: data.properties.Title,
      type: data.properties.Type,
      description: data.properties.Description,
    };
  });

  const handlePropertyUpdate = async (key, value) => {
    try {
      const response = await updateNodeByPriority(componentId, key, value);

      if (response === "success") {
        // Update local state
        setComponentData((prev) => ({
          ...prev,
          data: {
            ...prev.data,
            component: {
              ...prev.data.component,
              properties: {
                ...prev.data.component.properties,
                [key]: value,
              },
            },
          },
        }));

        showAlert && showAlert("Content updated successfully", "success");
      } else {
        throw new Error("Update failed");
      }
    } catch (error) {
      
      showAlert && showAlert("Failed to update content", "error");
    }
  };

  const handleNavigateToDesign = () => {
    const currentPath = pathname;
    const newPath = currentPath.replace(
      "/architecture/component/",
      "/architecture/design/"
    );
    router.push(newPath);
  };

  const handleBack = () => {
    if (sessionStorage.getItem("querySet")) {
      const backTabs = Number(sessionStorage.getItem("querySet")); 
      sessionStorage.removeItem("querySet");
      if (window.history.length > Number(backTabs) * (-1)) {
        window.history.go(backTabs); 
      }
      else { 
        router.push(`/project/${projectId}/architecture/architecture-requirement`);
      }
    }
    else {
      router.back();
    }
  };

  const treeData = [...Object.entries(ComponentData?.model?.Component?.ui_metadata || {})
    .filter(
      ([key, value]) =>
        !["Title", "Type", "IsArchitecturalLeaf"].includes(key) &&
        value.hidden !== true &&
        ComponentData?.data?.component?.properties?.hasOwnProperty(key) 
    )
    .map(([key, value]) => ({
      id: `${(value.Label || key).toLowerCase().replace(/[_\s]+/g, '-')}`,
      name: (value.Label || key).replace(/[_-]/g, ' '), 
      children: [], 
    })),
  ...(functionalReqs
    ? [{
      id: "functional-requirements",
      name: "Functional Requirements",
      children: [],
    }]
    : []),

  ...(tableData && tableData.length > 0
    ? [{
      id: "sub-components",
      name: "Sub Components",
      children: [],
    }]
    : []),

  ...(interfaces && interfaces.data?.interfaces?.length > 0
    ? [{
      id: "public-interfaces",
      name: "Public Interfaces",
      children: [],
    }]
    : []),

  ...(interfaces && parseIncomingInterfaces(interfaces).length > 0
    ? [{
      id: "connected-components",
      name: "Connected Components",
      children: [],
    }]
    : []),
  ]

  const handleScrollToSection = (id) => {
    const element = document.getElementById(id);
    const mainContent = document.getElementById("main-content")

    if (element && mainContent) {
      const topOffset = element.offsetTop;
      mainContent.scrollTo({
        top: topOffset - 80, 
        behavior: "smooth",
      });
    }
  };

  const HeaderSection = () => {
    const { Title, Type, IsArchitecturalLeaf } =
      ComponentData?.data?.component?.properties || {};
  
    return (
      <div className="flex flex-col space-y-4 top-1" style={{ zIndex: 5 }}>
        <div className="flex flex-col border border-gray-200">
          <div className="relative px-2 py-1 space-y-2">
            <div className="flex items-center gap-3">
              <IconButton
                icon={<ArrowLeft className="w-5 h-5 text-gray-600" />}
                tooltip="Go back"
                onClick={handleBack}
                className="hover:bg-gray-100"
              />
              <div className="flex items-center gap-2">
                <h2 className="typography-body-lg font-weight-semibold text-gray-800">
                  {Title || "Component Details"}
                </h2>
                {Type && (
                  <div className="flex items-center gap-1">
                    <Badge type={Type} />
                  </div>
                )}
              </div>
            </div>
  
            <div className="flex justify-between items-center w-full">
              <div className="flex items-center justify-between pl-3">
                {IsArchitecturalLeaf === "yes" && (
                  <DynamicButton
                    type="submit"
                    variant="primary"
                    size="default"
                    icon={ChevronsLeft}
                    onClick={handleNavigateToDesign}
                    text="View Design"
                  />
                )}
              </div>
  
              <div className="flex items-center gap-2">
                <DynamicButton
                  type="submit"
                  variant="primary"
                  size="default"
                  icon={Settings}
                  onClick={handleUpdateDesignDetails}
                  text="Update System Architecture"
                />
                {/* <DynamicButton
                  type="submit"
                  variant="primary"
                  size="default"
                  icon={Plus}
                  onClick={() => {
                    const newSearchParams = new URLSearchParams(searchParams);
                    newSearchParams.set("discussion", "new");
                    newSearchParams.set("node_id", componentId);
                    newSearchParams.set("node_type", "Component");
                    newSearchParams.set("discussion_type", "component_testcase_generation");
                    router.push(`${pathname}?${newSearchParams.toString()}`);
                  }}
                  text="Create TestCase"
                /> */}
                <ConfigureButtons updateProps={updateProps} />
                <button
                  onClick={handleViewPastDiscussion}
                  className="min-w-[100px] flex items-center gap-1 px-3 py-2 bg-white border border-gray-300 
                             rounded-md text-black hover:bg-gray-100 focus:ring-2 focus:ring-gray-500 
                             focus:ring-offset-2 typography-body-sm"
                >
                  <BookOpen size={14} />
                  History
                </button>
              </div>
            </div>
  
            <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-100/50 via-blue-300/20 to-transparent"></div>
          </div>
        </div>
      </div>
    );
  };
  
  return (
    <div className="relative flex max-h-[74vh] overflow-hidden bg-white-50">
      <div >
        {treeData && (
          <NavigationTree
            treeData={treeData}
            handleScrollToSection={handleScrollToSection}
          />
        )}
      </div>

      <main
        id="main-content"
        className={`
          flex-1 
          relative 
          overflow-y-auto 
          overflow-x-hidden 
          transition-all 
          duration-300 
          ease-in-out
        `}
        style={{
          width: mainContentWidth,
        }}
      >
        <div className="w-full pl-4">
          <div className="mb-4">
            <HeaderSection />
          </div>

          {ComponentData ? (
            <div>
              <PropertiesRenderer
                properties={ComponentData?.data?.component?.properties}
                metadata={ComponentData?.model?.Component?.ui_metadata}
                to_skip={["configuration_state", "Type", "Title"]}
                onUpdate={handlePropertyUpdate}
              />
              <div className="mt-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="typography-body-lg font-weight-medium text-gray-800">Test Cases</h3>
                  {testCasesLoading ? (
                    <div className="px-4 py-2 typography-body-sm text-gray-600">Loading...</div>
                  ) : testCases.length > 0 ? (
                    <div className="px-4 py-2 typography-body-sm rounded-md bg-blue-50 text-blue-700">
                      {testCases.length} test cases found
                    </div>
                  ) : null}
                </div>
                
                {testCasesLoading ? (
                  <div className="h-40 flex items-center justify-center">
                    <CardLoadingSkeleton />
                  </div>
                ) : testCases.length > 0 ? (
                  <TestCaseTable 
                    testCases={testCases}
                    onItemClick={handleTestCaseClick}
                    currentPage={testCasePage}
                    pageSize={testCasePageSize}
                    onPageChange={setTestCasePage}
                    onPageSizeChange={setTestCasePageSize}
                  />
                ) : (
                  <div className="border rounded-lg p-6 flex flex-col items-center justify-center bg-gray-50">
                    <p className="text-gray-500 mb-4">No test cases are associated with this component</p>
                    <DynamicButton
                      variant="primary"
                      icon={Plus}
                      text="Create Test Case"
                      onClick={() => {
                        const newSearchParams = new URLSearchParams(searchParams);
                        newSearchParams.set("discussion", "new");
                        newSearchParams.set("node_id", componentId);
                        newSearchParams.set("node_type", "Architecture");
                        newSearchParams.set("discussion_type", "component_testcase_generation");
                        router.push(`${pathname}?${newSearchParams.toString()}`);
                      }}
                    />
                  </div>
                )}
              </div>
              <div id="functional-requirements">
                {functionalReqs && (functionalReqs.length>0) ? (
                  <TableComponent
                    data={functionalReqs?.functional_requirements?.map((fr) => ({
                      id: fr.id,
                      title: fr.properties.Title,
                      type: fr.properties.Type,
                      description: fr.properties.Description,
                    }))}
                    headers={[
                      { key: "id", label: "ID" },
                      { key: "title", label: "Title" },
                      { key: "type", label: "Type" },
                      { key: "description", label: "Description" },
                    ]}
                    title="Functional Requirements"
                    itemsPerPage={20}
                    onRowClick={(id) =>
                      router.push(
                        `/project/${projectId}/architecture/component/${componentId}/FunctionalRequirement/${id}`
                      )
                    }
                  />
                ):(
                  <div className="mt-5 mb-3">
                  <PlaceholderMessage type="subComponents" message="No functional requirements are currently available" subMessage="Add a new functional requirements to get started." />
                </div> 
                )
              }</div>
              <div id="sub-components" className="relatedContentDiv ">
                {tableData && tableData.length > 0 ? (
                  <>
                    <TableComponent
                      data={tableData}
                      onRowClick={onRowClick}
                      headers={headers}
                      sortableColumns={{ id: true, title: true, type: true }}
                      itemsPerPage={20}
                      title={en.ChildSubComponentsHeading}
                    />
                  </>
                ) : (
                  <div className="mt-5 mb-3">
                    <PlaceholderMessage type="subComponents" message="No sub components are currently available" subMessage="Add a new sub components to get started." />
                  </div>
                )}
              </div>

              <div id="public-interfaces" className="space-y-4">
                {interfaces && interfaces.data?.interfaces?.length > 0 ? (
                  <>
                    <TableComponent
                      data={parseInterfaces(interfaces)}
                      headers={interfaceTableHeaders}
                      sortableColumns={{
                        id: true,
                        title: true,
                        definition_state: true,
                      }}
                      itemsPerPage={20}
                      onRowClick={(rowId) => {
                        router.push(
                          `/project/${projectId}/architecture/interfaces/${rowId}`
                        );
                      }}
                      title="Public Interfaces"
                    />

                    <APIDocumentation
                      apiDetails={
                        interfaces.data.interfaces[0].properties?.PublicAPIDetails
                      }
                    />
                  </>
                ) : (
                  <div className="mt-5 mb-3">
                    <PlaceholderMessage type="interfaces" message="No interfaces are currently available" subMessage="Add a new interface to get started" />
                  </div>
                )}
              </div>

              <div id="connected-components" className="space-y-4">
                {interfaces && parseIncomingInterfaces(interfaces).length > 0 ? (
                  <TableComponent
                    data={parseIncomingInterfaces(interfaces)}
                    headers={interfaceHeaders}
                    sortableColumns={{
                      sourceComponentId: true,
                      sourceComponentTitle: true,
                    }}
                    itemsPerPage={20}
                    onRowClick={(rowId) => {
                      if (rowId) {
                        router.push(
                          `/project/${projectId}/architecture/component/${rowId}`
                        );
                      }
                    }}
                    title="Connected Components"
                  />
                ) : (
                  <></>
                )}
              </div>
            </div>
          ) : (
            <p className="notFound">
              <EmptyStateView type="componentsDetails" />
            </p>
          )}
        </div>
      </main>
      
      {/* Add the test case detail modal */}
      {selectedTestCase && (
        <TestCaseDetailModal 
          testCase={selectedTestCase}
          onClose={closeTestCaseModal}
        />
      )}
    </div>
  );
};

export default ComponentDetails;
