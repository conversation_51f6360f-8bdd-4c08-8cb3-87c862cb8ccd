"use client";
import React, { useEffect, useState, useContext } from "react";
import "@/styles/tabs/architecture/designDetailTab/designDetailList.css"
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { fetchComponentWithAllChildren } from "@/utils/api";
import TableComponent from "@/components/SimpleTable/ArchitectureTable"
import ConfigureButtons from "@/components/ConfigureButtons/ConfigureButtons";
import { Loading2 } from "@/components/Loaders/Loading";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import Badge from "@/components/UIComponents/Badge/Badge"
import ErrorView from "@/components/Modal/ErrorViewModal";
import en from "@/en.json";
import { ArchitectureContext } from "@/components/Context/ArchitectureContext";


const DesignDetailList = () => {
  const [DesignList, setDesignList] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null)
  const pathname = usePathname();
  const router = useRouter();
  const projectId = pathname.split("/")[2];
  const searchParams = useSearchParams();
  const [projectName, setProjectName] = useState("");
  const { containerIdVal, setComponentIdVal } = useContext(ArchitectureContext)



  const fetchData = async () => {
    try {
      const data = await fetchComponentWithAllChildren(projectId);
      setDesignList(data);
      const name = sessionStorage.getItem("project name")
      setProjectName(name)
    } catch (err) {
      
      setError(err);
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    fetchData();
  }, [projectId, pathname])

  if (loading) {
    return <Loading2 />;
  }

  if (error) {
    return (
      <ErrorView
        title="Unable to Load Design Details List"
        message={en.UnableToLoadDesignDetailsList}
        onRetry={() => fetchData()}
        panelType='main'
      />
    );
  }


  const headers = [
    { key: 'id', label: 'Id' },
    { key: 'title', label: 'Title' },
    { key: 'type', label: 'Type' },
    { key: 'description', label: 'Description' },
  ];

  const tableData = DesignList?.data?.childNodes?.map(data => ({
    id: data.id,
    title: data.properties.Title,
    type: data.type,
    description: data.properties.Description
  }))

  const handleUpdateDesignList = () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", containerId);
    newSearchParams.set("node_type", "Architecture");
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const updateProps = {
    onUpdateClick: handleUpdateDesignList,
    buttonText: "Update Design List  Details",
  };
  const handleRowClick = (id, type) => {
    sessionStorage.setItem("child design id", id)
    const component = DesignList?.data?.childNodes.find(comp => comp.id === id);
    const { component_id } = component.properties
    sessionStorage.setItem("containerId", component_id)
    setComponentIdVal(component_id)
    router.push(`/project/${projectId}/architecture/design-details/${id}`)
  };



  return (
    <div className=" custom-scrollbar design-details-list-content-wrapper">
      <div className="design-details-list-content-sub-wrapper">
        <div className="design-details-list-container">
          <div
            className="design-details-list-header-wrapper"
          >
            <div className="design-details-list-header-sub-wrapper">
              <h2 className="design-details-list-heading-title" >  {"Design Details"}</h2>
              {DesignList?.data?.childNodes ? (
                <div className="design-details-heading-badge">
                  <Badge type={"Child Component"} />
                </div>
              ) : (
                ""
              )}
            </div>
            <div className="design-details-header-button-wrapper">
              <ConfigureButtons
                updateProps={updateProps}

              />
            </div>

          </div>
          <div className="design-details-list-related-child-nodes-wrapper">
            {DesignList?.data?.childNodes?.length > 0 ? (
              <>
                <TableComponent
                  data={tableData}
                  onRowClick={handleRowClick}
                  headers={headers}
                  sortableColumns={{ "id": true, "title": true, "type": true }}
                  itemsPerPage={20}
                  title={en.ChildSubComponentsHeading}
                />

              </>
            ) : (
              <div className="design-details-list-no-items-found">
                <EmptyStateView type="designDetails" />
              </div>
            )}

          </div>
        </div>
      </div>
    </div>
  
  )

}

export default DesignDetailList