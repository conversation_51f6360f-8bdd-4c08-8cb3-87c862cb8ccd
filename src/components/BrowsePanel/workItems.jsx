"use client"
import React, { useState, useContext, useEffect } from 'react';
import Search from './TableComponents/Search';
import { AlertContext } from '../NotificationAlertService/AlertList';
import { fetchChildNodes } from '@/utils/api';
import TableComponent from '../SimpleTable/table';
import { useParams, usePathname, useSearchParams } from 'next/navigation';

import { useRouter } from 'next/navigation';
import ConfigureButtons from '../ConfigureButtons/ConfigureButtons';
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import en from "@/en.json"
import ErrorView from "@/components/Modal/ErrorViewModal";
import { BookOpen } from 'lucide-react';
import { TableLoadingSkeleton } from "@/components/UIComponents/Loaders/LoaderGroup"
import { TOOLTIP_CONTENT } from '@/utils/constant/tooltip';

const WorkItems = () => {
    const [searchTerm, setSearchTerm] = useState("");
    const { showAlert } = useContext(AlertContext);
    const [nodeDetails, setNodeDetails] = useState([]);
    const [error, setError] = useState(null)
    const pathname = usePathname();
    const params = useParams();
    const searchParams = useSearchParams();
    const projectId = params.projectId;
    const router = useRouter();
    const [loading, setLoading] = useState(true);
    const [nodeBased, setNodeBased] = useState(null);

    const showBtn = () => {
        showAlert("Functionality was not implemented", "info")
    }

    const [workItemNodeId, setWorkItemNodeId] = useState(null);

    const fetchData = async () => {
        try {
            setLoading(true);
            const data = await fetchChildNodes(projectId, 'Project', 'WorkItemRoot');
            setWorkItemNodeId(data[0].id);
            const nodes = await fetchChildNodes(data[0].id, 'WorkItem', 'WorkItem');
            setNodeDetails(nodes);

        } catch (e) {
            setError(e)
        } finally {
            setLoading(false);
        }

    }

    useEffect(() => {
        fetchData()
    }, [projectId, router, pathname]);

    const onRowClick = (id) => {
        router.push(`/project/${projectId}/workitems/${id}`);
    };

    const onActionClick = (id) => { };

    if (error) {
        return (
            <ErrorView
                title="Unable to Load Workitems"
                message={en.UnableToLoadWorkitems}
                onRetry={() => fetchData()}
                panelType='main'
            />
        );
    }

    const transformedNodeDetails = nodeDetails.map(item => ({
        id: item.id,
        Title: item.properties.Title,
        Type: item.properties.Type,
        Assignee: item.properties.Assignee,
        Priority: item.properties.Priority,
        Description: item.properties.Description,
    }));

    const filteredData = transformedNodeDetails.filter(
        (item) =>
            item.Title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.Assignee.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.Priority.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.Description.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const headers = [
        { key: "id", label: "Id" },
        { key: "Title", label: "Title" },
        { key: "Assignee", label: "Assignee" },
        { key: "Priority", label: "Priority" },
        { key: "Description", label: "Description" },
    ];

    const updateProps = {
        onUpdateClick: () => {
            if (!workItemNodeId) return;
            const newSearchParams = new URLSearchParams(searchParams);
            newSearchParams.set("discussion", "new");
            newSearchParams.set("node_id", workItemNodeId);
            newSearchParams.set("node_type", "WorkItem");
            router.push(`${pathname}?${newSearchParams.toString()}`);
        },
        buttonText: nodeDetails && nodeDetails.length > 0 ? "Update WorkItems" : "Create WorkItems",
        tooltip: nodeDetails && nodeDetails.length > 0 ? TOOLTIP_CONTENT.WorkItems.update : TOOLTIP_CONTENT.WorkItems.create,
    };

    const configureProps = {
        isConfigurable: false,
        nodeId: workItemNodeId,
        nodeType: "WorkItem",
        setLoadingAutoConfigure: () => { },
        onSubmitSuccess: () => {
            showAlert("Auto configure initiated", "success");
        },
        buttonText: "Auto Configure"
    };

    const handleViewPastDiscussion = async () => {
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.set('view_past_discussions', 'true')
        newSearchParams.set("node_id", workItemNodeId);
        newSearchParams.set('discussion_type', 'WorkItem');
        router.push(`${pathname}?${newSearchParams.toString()}`);
    };

    if (nodeBased) {
        const { node, ui_metadata } = data;
        const workItemMetadata = ui_metadata.WorkItem;

        // Extract and sort keys from ui_metadata based on their labels
        const sortedFields = Object.keys(workItemMetadata)
            .filter(field => node.hasOwnProperty(field))  // Only include fields present in the node
            .sort((a, b) => {
                return (workItemMetadata[a].Label || '').localeCompare(workItemMetadata[b].Label || '');
            });
    }

    const clearSearchQuery = () => {
        setSearchTerm('');
    };

    return (
        <>
            <div className="workItemMainDiv">
                <div className="workItemTopPanel">
                    <div>
                        {!loading && <div className="loadingDivWrapper">
                            <div className="searchDiv">
                                {
                                    nodeDetails.length != 0 && <Search
                                        searchTerm={searchTerm}
                                        setSearchTerm={setSearchTerm}
                                    />
                                }
                            </div>

                            <div className="buttonDiv ">
                                <ConfigureButtons updateProps={updateProps} configureProps={configureProps} />

                                <button
                                    onClick={handleViewPastDiscussion}
                                    className="min-w-[100px] flex items-center gap-1 px-3 py-2 bg-white border border-gray-300
                                            rounded-md text-black hover:bg-gray-100 focus:ring-2 focus:ring-gray-500
                                            focus:ring-offset-2 typography-body-sm"
                                >
                                    <BookOpen size={14} />
                                    History
                                </button>
                            </div>
                        </div>}

                        <div className=' custom-scrollbar mainDiv' >
                            {loading ? <div className='mainDivLoading'><TableLoadingSkeleton /></div> :
                                nodeDetails.length == 0 ?
                                    <div className='stateViewModal'>
                                        <EmptyStateView type="workItems" />
                                    </div> : filteredData.length == 0 ?
                                        <div className='stateViewModal'>
                                            <EmptyStateView type="noSearchResult" onClick={clearSearchQuery} />
                                        </div> :
                                        <TableComponent
                                            data={filteredData}
                                            onRowClick={onRowClick}
                                            headers={headers}
                                            sortableColumns={{ "id": true, "Title": true, "Assignee": true, "Priority": true }}
                                            itemsPerPage={20}
                                            onActionClick={onActionClick}
                                        />
                            }
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default WorkItems;
