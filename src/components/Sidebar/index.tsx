//src/components/Sidebar/index.tsx
"use client";
import React, { useState, useContext, useEffect } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import SidebarItem from "@/components/Sidebar/SidebarItem";
import useLocalStorage from "@/hooks/useLocalStorage";
import SidebarFooter from "./SidebarFooter";
import Drawer from "@/components/Drawer";
import PreloadProjectList from "@/components/ProjectsList/PreloadProjectList";
import PreloadChatList from "../ChatList/PreLoadChatList";
import LogoutProjectModal from "@/components/Modal/LogoutProjectModal";
import { useRouter } from "next/navigation";
import { AlertContext } from "../NotificationAlertService/AlertList";
import { TopBarContext } from "../Context/TopBarContext";
import Logo from "../../../public/logo/kavia_logo.svg";
import Cookies from 'js-cookie';

import "@/styles/sidebar.css";

interface SidebarProps {
  isDarkMode?: boolean;
}

const menuGroups = [
  {
    name: "Main",
    menuItems: [
      {
        icon: (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="currentColor"
            className="menu-list-icon"
          >
            <path d="M11.47 3.841a.75.75 0 0 1 1.06 0l8.69 8.69a.75.75 0 1 0 1.06-1.061l-8.689-8.69a2.25 2.25 0 0 0-3.182 0l-8.69 8.69a.75.75 0 1 0 1.061 1.06l8.69-8.689Z" />
            <path d="m12 5.432 8.159 8.159c.03.03.06.058.091.086v6.198c0 1.035-.84 1.875-1.875 1.875H15a.75.75 0 0 1-.75-.75v-4.5a.75.75 0 0 0-.75-.75h-3a.75.75 0 0 0-.75.75V21a.75.75 0 0 1-.75.75H5.625a1.875 1.875 0 0 1-1.875-1.875v-6.198a2.29 2.29 0 0 0 .091-.086L12 5.432Z" />
          </svg>
        ),
        label: "Home",
        route: "/home",
      },
      {
        icon: (
          <svg
          width="16"
          height="14"
          viewBox="0 0 16 14"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="menu-list-icon"
        >
          <path
            d="M11.25 3.09211V3.59211H11.75H14C14.2737 3.59211 14.5311 3.69535 14.7168 3.8713C14.9016 4.04637 15 4.27787 15 4.51316V12.3289C15 12.5642 14.9016 12.7957 14.7168 12.9708C14.5311 13.1468 14.2737 13.25 14 13.25H2C1.72632 13.25 1.46893 13.1468 1.28321 12.9708L0.941787 13.3312L1.28321 12.9708C1.09841 12.7957 1 12.5642 1 12.3289V4.51316C1 4.27787 1.09841 4.04637 1.28321 3.8713C1.46893 3.69535 1.72632 3.59211 2 3.59211H4.25H4.75V3.09211V2.38158C4.75 1.95785 4.92743 1.54563 5.25288 1.2373L4.91636 0.882085L5.25288 1.2373C5.57925 0.928107 6.02741 0.75 6.5 0.75H9.5C9.97259 0.75 10.4207 0.928107 10.7471 1.2373L11.091 0.874325L10.7471 1.2373C11.0726 1.54563 11.25 1.95785 11.25 2.38158V3.09211ZM10.25 3.59211H10.75V3.09211V2.38158C10.75 2.05154 10.6114 1.74086 10.3742 1.51619C10.138 1.29238 9.82306 1.17105 9.5 1.17105H6.5C6.17694 1.17105 5.86204 1.29238 5.6258 1.51618C5.38864 1.74086 5.25 2.05154 5.25 2.38158V3.09211V3.59211H5.75H10.25ZM14.5 4.51316V4.01316H14H2H1.5V4.51316V7.03126V7.2645L1.67871 7.41437C3.20028 8.69045 5.6381 9.28662 8 9.28662C10.3619 9.28662 12.7997 8.69045 14.3213 7.41437L14.5 7.2645V7.03126V4.51316ZM1.5 12.3289V12.8289H2H14H14.5V12.3289V8.78058V7.95622L13.7689 8.33717C12.0083 9.25463 10.0219 9.71773 8.0098 9.67828L7.99998 9.67809L7.99017 9.67828C5.97805 9.71786 3.99166 9.25476 2.23109 8.33718L1.5 7.95616V8.78058V12.3289ZM8.25 6.64474C8.25 6.73588 8.16389 6.85526 8 6.85526C7.83611 6.85526 7.75 6.73588 7.75 6.64474C7.75 6.55359 7.83611 6.43421 8 6.43421C8.16389 6.43421 8.25 6.55359 8.25 6.64474Z"
            fill="currentColor"
            stroke="currentColor"
            strokeWidth="0.5"
          />
        </svg>

        ),
        label: "Project list",
        route: "#",
      },

      {
        icon: (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="currentColor"
            className="menu-list-icon"
            style={{ opacity: 0.8 }}
          >
            <path
              d="M2.25 3.75a.75.75 0 0 1 .75-.75h7.5a.75.75 0 0 1 .75.75v7.5a.75.75 0 0 1-.75.75h-7.5a.75.75 0 0 1-.75-.75v-7.5ZM12.75 3.75a.75.75 0 0 1 .75-.75h7.5a.75.75 0 0 1 .75.75v7.5a.75.75 0 0 1-.75.75h-7.5a.75.75 0 0 1-.75-.75v-7.5ZM2.25 13.5a.75.75 0 0 1 .75-.75h7.5a.75.75 0 0 1 .75.75v7.5a.75.75 0 0 1-.75.75h-7.5a.75.75 0 0 1-.75-.75v-7.5ZM12.75 13.5a.75.75 0 0 1 .75-.75h7.5a.75.75 0 0 1 .75.75v7.5a.75.75 0 0 1-.75.75h-7.5a.75.75 0 0 1-.75-.75v-7.5Z"
              fill="currentColor"
            />
          </svg>
        ),
        label: "Dashboard",
        route: "/dashboard/profile",
      }
    ],
  },
];


const Sidebar = ({ isDarkMode = false }: SidebarProps) => {


  const pathname = usePathname();
  const [pageName, setPageName] = useLocalStorage("selectedMenu", "dashboard");
  const [isCollapsed] = useState(true);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [activeDrawer, setActiveDrawer] = useState("");
  const [clickedMenuItem, setClickedMenuItem] = useState("");
  const [isLogoutModalOpen, setIsLogoutModalOpen] = useState(false);
  const [isRedirecting, setIsRedirecting] = useState(false);
  const { showAlert } = useContext(AlertContext);
  const { clearTabsFromLocalStorage } = useContext(TopBarContext);
  const [activeMenuItem, setActiveMenuItem] = useState("");
  const [, setIsItemClicked] = useState(false);
  const router = useRouter();
  const [actualIsPublicSelected, setActualIsPublicSelected] = useState(false);

  const handleDrawerToggleFunction = (drawerName: any) => {
    // Store current value from cookie
    const cookieValue = Cookies.get('is_public_selected') === 'true';
    setActualIsPublicSelected(cookieValue);
    // Set cookie to false when drawer opens
    Cookies.set('is_public_selected', 'false');

    setClickedMenuItem(activeDrawer);
    setActiveDrawer(drawerName);
    setIsDrawerOpen(drawerName !== "");
    setIsItemClicked(true);
  };
  const handleDrawerToggle = (drawerName: any) => {
    setActiveDrawer(activeDrawer === drawerName ? "" : drawerName);
  };

  const handleDrawerClose = () => {
    // Restore the actual value from backup when drawer closes
    Cookies.set('is_public_selected', String(actualIsPublicSelected));

    setIsDrawerOpen(false);
    setActiveDrawer("");
    setActiveMenuItem("");
    setIsItemClicked(false);

    const currentPath = pathname.toLowerCase();
    const matchingItem = menuGroups
      .flatMap((group) => group.menuItems)
      .find((item) => currentPath.includes(item.label.toLowerCase()));

    if (matchingItem) {
      setActiveMenuItem(matchingItem.label.toLowerCase());
    }

    if (activeMenuItem === clickedMenuItem) {
      setClickedMenuItem(activeMenuItem);
    }
    setClickedMenuItem("");
  };

  const deleteAllCookies = () => {
    document.cookie.split(";").forEach((cookie) => {
      const name = cookie.split("=")[0];
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
    });
  };

  const handleLogout = async () => {
    try {
      // No need to backup tabs anymore

      // Clear cookies
      deleteAllCookies();

      // Clear localStorage items
      localStorage.removeItem("username");

      // Clear sessionStorage items
      sessionStorage.removeItem("activeItem");
      sessionStorage.removeItem("containerId");
      sessionStorage.removeItem("componentId");
      sessionStorage.removeItem("child design id");
      sessionStorage.removeItem("designId");
      sessionStorage.removeItem("tabs");

      // Preserve tenant_id
      let _tenant_id = localStorage.getItem("tenant_id");

      // Clear tabs from localStorage via context function
      if (typeof clearTabsFromLocalStorage === 'function') {
        clearTabsFromLocalStorage();
      }

      // Set tenant_id back
      localStorage.setItem("tenant_id", _tenant_id || "");

      // Show success message and update UI
      showAlert("Successfully logged out!", "success");
      setIsLogoutModalOpen(false);
      setIsRedirecting(true);

      // Redirect to home page
      window.location.href = "/";
    } catch (error) {

      showAlert("Logout failed. Please try again.", "danger");
      setIsLogoutModalOpen(false);
    }
  };

  useEffect(() => {
    const pageFromUrl = pathname.split("/")[1].toLowerCase();
    const activeItem =
      menuGroups
        .flatMap((group) => group.menuItems)
        .find((item) => pageFromUrl.includes(item.label.toLowerCase()))
        ?.label.toLowerCase() || "";

    setActiveMenuItem(activeItem);
    setPageName(pageFromUrl);
  }, [pathname]);

  const handleMenuItemClick = (label: string) => {
    setActiveMenuItem(label.toLowerCase());
    setClickedMenuItem(label);
    setIsItemClicked(true);
  };

  useEffect(() => {
    if (isLogoutModalOpen) {
      setIsDrawerOpen(false);
      setActiveDrawer("");
    }
  }, [isLogoutModalOpen]);

  return (
    <>
      {isLogoutModalOpen && (
        <LogoutProjectModal
          handleLogout={handleLogout}
          setIsLogoutModalOpen={setIsLogoutModalOpen}
        />
      )}

      {isRedirecting && (
        <div className="redirecting-overlay">
          <div className="spinner"></div>
        </div>
      )}

      <div className="sidebar-container">
        <aside
          id="sidebar-menu-icons"
          className={`sidebar ${isCollapsed ? "collapsed" : ""} ${isDarkMode ? "dark-theme" : ""}`}
        >
          <div className="sidebar-header">
            <Link
              href="https://kavia.ai/"
              target="_blank"
              rel="noopener noreferrer"
            >
              <div
                className={`logo-container ${isCollapsed ? "logo-small" : "logo-large"}`}
              >
                <Image
                  src={Logo}
                  alt="Logo"
                  className="logo-image"
                  fill
                  style={{ objectFit: "contain" }}
                  priority
                />
              </div>
            </Link>
          </div>
          <nav className="sidebar-nav">
            {menuGroups.map((group, groupIndex) => (
              <div key={groupIndex} className="menu-group">
                {group?.name && !isCollapsed && (
                  <h3 className="group-title">{group.name}</h3>
                )}
                <ul className="menu-list">
                  {group.menuItems.map((menuItem, menuIndex) => (
                    <SidebarItem
                      key={`${groupIndex}-${menuIndex}-${menuItem.label}`}
                      index={menuIndex}
                      item={menuItem}
                      pageName={pageName}
                      setPageName={setPageName}
                      isCollapsed={isCollapsed}
                      handleDrawerToggle={() =>
                        handleDrawerToggleFunction(menuItem.label)
                      }
                      isActive={
                        isDrawerOpen
                          ? activeMenuItem === menuItem.label.toLowerCase()
                          : menuItem.label.toLowerCase() ===
                          pathname.split("/")[1]
                      }
                      onClick={() => handleMenuItemClick(menuItem.label)}
                    />
                  ))}
                </ul>
              </div>
            ))}
          </nav>
          <div className="sidebar-footer">
            <SidebarFooter
              setIsLogoutModalOpen={setIsLogoutModalOpen}
              handleDrawerToggle={handleDrawerToggle}
              activeDrawer={activeDrawer}
              theme={isDarkMode ? 'dark' : 'light'}
            />
          </div>
        </aside>

        <Drawer
          isOpen={
            activeDrawer === "Project list" || activeDrawer === "Chat history"
          }
          onClose={handleDrawerClose}
          showBackdrop={false}
          placement="left"
          sidebarWidth="5rem"
          theme={isDarkMode ? 'dark' : 'light'}
          title={
            <span className="drawer-title">
              <div className="drawer-title-container">
                {menuGroups
                  .flatMap((group) => group.menuItems)
                  .filter(
                    (menuItem) =>
                      menuItem.label.toLowerCase() ===
                      activeDrawer.toLowerCase()
                  )
                  .map((menuItem) => (
                    <div key={menuItem.label} className="drawer-title-icon" >
                      {menuItem.icon}
                    </div>
                  ))}
                <span className="drawer-title-name">{activeDrawer}</span>
              </div>
            </span>
          }

        >
          {activeDrawer === "Project list" ? (
            <PreloadProjectList
              handleDrawerToggle={() => setActiveDrawer("")}
              theme={isDarkMode ? 'dark' : 'light'}
            />
          ) : (
            <PreloadChatList handleDrawerToggle={() => setActiveDrawer("")} />
          )}
        </Drawer>
      </div>
    </>
  );
};

export default Sidebar;