// src/services/projectService.js
// Make sure this file is created in the correct location

/**
 * Project Service for handling project-related operations
 */

import { startProjectInit } from "@/utils/projectApi";

/**
 * Fetches the project blueprint based on the user input
 * @param {string} projectDescription - The user's project description
 * @param {string} selectedFramework - The currently selected framework name
 * @returns {Promise<Object>} - The project blueprint object
 */
export const fetchProjectBlueprint = async (projectDescription, selectedFramework = "React JS") => {
  // Log the received project description to verify it's working
  
  

  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  const projectInitResp = await startProjectInit(projectDescription);
  const projectInitResponse = projectInitResp.llmResponse;
  
  
  return {
    id: Math.random().toString(36).substring(2, 9),
    name: projectInitResponse.projectTitle,
    description: projectInitResponse.description,
    features: projectInitResponse.features,
    techStack: {
      frontend: [selectedFramework], // Use the selected framework
      backend: ["Node.js with Express"],
      language: ["JavaScript (ES6+)"]
    },
    colors: projectInitResponse.colors,
    theme: "light",
    estimatedTime: "1-2 weeks",
    complexity: "simple",
    layoutDescription: projectInitResponse.layoutDescription,
    projectInfo: projectInitResp.projectNodeInfo
  };
};

/**
 * Implements the project based on the blueprint
 * @param {Object} blueprintData - The project blueprint data
 * @returns {Promise<Object>} - The implemented project response
 */
export const implementProject = async (blueprintData) => {
  
  
  // Verify we have the project ID from the blueprint
  if (!blueprintData.id) {
    console.error("No project ID found in the blueprint data!");
    throw new Error("Missing project ID in blueprint data");
  }
  
  
  
  // Simulate network delay for API call
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  // Use the ID from the blueprint instead of generating a new one
  const projectId = blueprintData.id;
  
  // Extract project details from the blueprint data
  // This includes any modifications made in the ProjectCreationModal
  const projectName = blueprintData.name || "New Project";
  const projectDesc = blueprintData.description || "";
  const featuresCount = blueprintData.features?.length || 0;
  const frameworkName = blueprintData.techStack?.frontend?.[0] || "React 19";
  const languageName = blueprintData.techStack?.language?.[0] || "JavaScript (ES6+)";
  
  // projectInit API response
  const projectInitResponse = {
    success: true,
    id: projectId, // Use the same ID from the blueprint
    properties: {
      Title: projectName,
      Name: projectName,
      Status: "In Progress",
      CreatedAt: new Date().toISOString(),
      Description: projectDesc,
      Features: featuresCount,
      Framework: frameworkName,
      Language: languageName
    },
    message: "Project implementation started successfully!"
  };
  
  
  
  return projectInitResponse;
};